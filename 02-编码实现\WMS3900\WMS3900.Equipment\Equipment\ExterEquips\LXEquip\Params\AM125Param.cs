﻿using System;
using System.ComponentModel;
using Fpi.Communication.Converter;

namespace Fpi.WMS3000.Equipment.LXAM125
{
    /// <summary>
    /// AM125模块化电源电涌保护器参数
    /// </summary>
    public class AM125Param
    {
        #region 字段属性

        /// <summary>
        /// 实时温度 单位℃
        /// </summary>
        [Description("实时温度(℃)")]
        public int RealTimeTemp { get; set; } = -1;

        /// <summary>
        /// 最高温度 单位℃
        /// </summary>
        [Description("最高温度(℃)")]
        public int MaxTemp { get; set; } = -1;

        /// <summary>
        /// 在线时间 单位天
        /// </summary>
        [Description("在线时间(天)")]
        public int OnlineTime { get; set; } = -1;

        /// <summary>
        /// 当前计数
        /// </summary>
        [Description("当前计数")]
        public int CurrentCount { get; set; } = -1;

        /// <summary>
        /// 合计计数
        /// </summary>
        [Description("合计计数")]
        public int TotalCount { get; set; } = -1;

        /// <summary>
        /// 最高计数
        /// </summary>
        [Description("最高计数")]
        public int MaxCount { get; set; } = -1;

        /// <summary>
        /// 温度报警
        /// </summary>
        [Description("温度报警")]
        public bool TempAlarm { get; set; }

        /// <summary>
        /// 失效报警
        /// </summary>
        [Description("失效报警")]
        public bool FailureAlarm { get; set; }

        /// <summary>
        /// 线路报警
        /// </summary>
        [Description("线路报警")]
        public bool LineAlarm { get; set; }

        /// <summary>
        /// 更换预警
        /// 判断合计次数，达到最高计数的80%时，进行预警提示
        /// </summary>
        [Description("更换预警")]
        public bool ReplaceEarlyAlarm => TotalCount != -1 && MaxCount != -1 && TotalCount > MaxCount * 0.8;

        #endregion

        #region 公共方法

        /// <summary>
        /// 更新参数
        /// </summary>
        /// <param name="data"></param>
        public void UpdateValue(byte[] data, int startIndex)
        {
            if(data.Length < startIndex + 12)
            {
                throw new Exception("读取设备参数回应数据不完整！");
            }

            RealTimeTemp = (int)DataConverter.GetInstance().ToUInt32(data, startIndex);
            MaxTemp = (int)DataConverter.GetInstance().ToUInt32(data, startIndex + 2);
            OnlineTime = (int)DataConverter.GetInstance().ToUInt32(data, startIndex + 4);
            CurrentCount = (int)DataConverter.GetInstance().ToUInt32(data, startIndex + 6);
            TotalCount = (int)DataConverter.GetInstance().ToUInt32(data, startIndex + 8);
            MaxCount = (int)DataConverter.GetInstance().ToUInt32(data, startIndex + 8);
        }

        #endregion
    }
}