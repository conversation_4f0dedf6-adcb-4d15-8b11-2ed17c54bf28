﻿using System.Drawing;

namespace Fpi.WMS3000.SystemConfig
{
    partial class FrmImagePatrolResultShow
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FrmImagePatrolResultShow));
            this.uc_ImagePatrolResult = new Fpi.WMS3000.SystemConfig.ImagePatrol.UI.UC_ImagePatrolResult();
            this.SuspendLayout();
            // 
            // uc_ImagePatrolResult
            // 
            this.uc_ImagePatrolResult.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uc_ImagePatrolResult.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uc_ImagePatrolResult.Location = new System.Drawing.Point(2, 35);
            this.uc_ImagePatrolResult.MinimumSize = new System.Drawing.Size(1, 1);
            this.uc_ImagePatrolResult.Name = "uc_ImagePatrolResult";
            this.uc_ImagePatrolResult.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.uc_ImagePatrolResult.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.uc_ImagePatrolResult.Size = new System.Drawing.Size(1352, 838);
            this.uc_ImagePatrolResult.TabIndex = 0;
            this.uc_ImagePatrolResult.Text = "uC_ImagePatrolResult1";
            this.uc_ImagePatrolResult.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // FrmImagePatrolResultShow
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.BackColor = System.Drawing.Color.White;
            this.ClientSize = new System.Drawing.Size(1356, 875);
            this.Controls.Add(this.uc_ImagePatrolResult);
            this.EscClose = true;
            this.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(15)))), ((int)(((byte)(68)))), ((int)(((byte)(134)))));
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.MaximizeBox = false;
            this.MaximumSize = new System.Drawing.Size(1920, 1080);
            this.MinimizeBox = false;
            this.Name = "FrmImagePatrolResultShow";
            this.Padding = new System.Windows.Forms.Padding(2, 35, 2, 2);
            this.ShowIcon = false;
            this.ShowInTaskbar = false;
            this.ShowRadius = true;
            this.ShowShadow = false;
            this.Text = "图像巡检结果";
            this.ZoomScaleRect = new System.Drawing.Rectangle(15, 15, 800, 600);
            this.ResumeLayout(false);

        }

        #endregion

        private ImagePatrol.UI.UC_ImagePatrolResult uc_ImagePatrolResult;
    }
}