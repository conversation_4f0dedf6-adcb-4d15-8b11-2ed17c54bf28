﻿using System.Collections.Generic;
using System.ComponentModel;
using Fpi.WMS3000.Equipment;

namespace Fpi.WMS3000.SystemConfig.ImagePatrol.Config
{
    /// <summary>
    /// 五参数流通池巡检结果
    /// </summary>
    public class FiveParamFlowPoolPatrolResult : ImageUnitPatrolResultBase
    {
        #region 字段属性

        /// <summary>
        /// 五参数流通池脏污状态
        /// </summary>
        [Description("五参数流通池脏污状态")]
        public eSmutState FiveParamFlowPoolState { get; set; }

        #endregion

        #region 构造

        public FiveParamFlowPoolPatrolResult()
        {
            UnitId = "FiveParamFlowPool";
            UnitName = "五参数流通池";
        }

        #endregion

        #region 方法重写

        /// <summary>
        /// 打印巡检结果
        /// </summary>
        /// <returns></returns>
        public override List<string> GetResultStr()
        {
            return new List<string>
            {
                $"五参数流通池脏污状态：{FiveParamFlowPoolState}"
            };
        }

        #endregion
    }
}