﻿using System.Data;
using System.Threading;
using System.Windows.Forms;
using Fpi.Json;
using Fpi.Util.StatusForm;
using Fpi.WMS3000.DB;
using Fpi.WMS3000.SystemConfig;
using Fpi.WMS3000.SystemConfig.ImagePatrol.Config;

namespace Fpi.WMS3000.UI.DataQuery
{
    /// <summary>
    /// 图像巡检数据查询
    /// </summary>
    public partial class UC_QueryImagePatrolData : UC_QueryDataBase
    {
        #region 构造

        public UC_QueryImagePatrolData()
        {
            InitializeComponent();
            this.dgvData.CellContentClick += dgvData_CellContentClick;

            TitleName = "图像巡检数据";
            DBTableName = DbConfig.IMAGEPATROL_RESULT_TABLE;
            SelectSqlStr = BuildSelectString();
            IsShowStateLabel = false;
        }

        #endregion

        #region 公共方法（重写）

        /// <summary>
        /// 设置表头，子类重写
        /// </summary>
        protected override void SetDataGridViewHead()
        {
            dgvData.ClearColumns();
            dgvData.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;

            var col = dgvData.AddColumn("序号", "num");
            col.FillWeight = 50;
            col = dgvData.AddColumn("诊断开始时间", "datatime");
            col.FillWeight = 150;
            col = dgvData.AddColumn("触发方式", "triggertype");
            col.FillWeight = 120;
            col = dgvData.AddButtonColumn("数据详情", "DetailData");
            col.FillWeight = 100;
        }

        /// <summary>
        /// 表格中填充数据，子类重写
        /// </summary>
        protected override void FillDataGridViewData(IDataReader reader, int curPage, int onePageCount)
        {
            try
            {
                // 打开进度条界面
                FpiStatusFormService.ShowStatusForm(onePageCount, "数据渲染中，请稍候...");

                // 进度条序号值
                int currentStep = 1;
                // 进度条满值
                int currentPageCount = onePageCount;
                // 若当前是最后一页，则数据不足onePageCount
                if(curPage * onePageCount > pagination.TotalCount)
                {
                    currentPageCount = pagination.TotalCount - (curPage - 1) * onePageCount;
                }

                // 表格中数据序号值
                int rowIndex = (curPage - 1) * onePageCount + 1;

                while(reader.Read())
                {
                    int index = dgvData.Rows.Add();
                    try
                    {
                        DataGridViewRow dr = dgvData.Rows[index];
                        dr.Cells[0].Value = rowIndex++;

                        dr.Cells[1].Value = reader.GetDateTime(1).ToString(DbConfig.DATETIME_FORMAT);

                        string jsonData = reader.GetString(2);
                        // 处理文件路径转义问题
                        jsonData = jsonData.Replace(@"\", @"\\");
                        OldImagePatrolResult result = FpiJsonHelper.JsonToModel<OldImagePatrolResult>(jsonData);
                        if(result != null)
                        {
                            dr.Tag = result;
                            dr.Cells[2].Value = result.PatrolTriggerType;
                            dr.Cells[3].Value = "数据详情";
                        }
                    }
                    catch
                    {
                        dgvData.Rows.RemoveAt(index);
                    }
                }

                FpiStatusFormService.SetDescription($"数据渲染中[{currentStep++}/{currentPageCount}]......");
                FpiStatusFormService.StepIt();
            }
            finally
            {
                // 线程切换，防止最终进度界面无法关闭
                Thread.Sleep(100);
                // 隐藏进度条界面
                FpiStatusFormService.HideStatusForm();
            }
        }

        #endregion

        #region 事件

        private void dgvData_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {
            if(e.ColumnIndex != 3)
            {
                return;
            }

            if(e.ColumnIndex >= 0 && e.RowIndex >= 0)
            {
                DataGridViewRow row = dgvData.Rows[e.RowIndex];
                if(row?.Tag is OldImagePatrolResult result)
                {
                    new FrmImagePatrolResultShow(result).ShowDialog();
                }
            }
        }

        #endregion

        #region 私有方法

        private string BuildSelectString()
        {
            return "select id,datatime,patrolresult";
        }

        #endregion
    }
}