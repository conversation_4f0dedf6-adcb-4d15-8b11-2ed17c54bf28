﻿using System.Collections.Generic;
using System.Windows.Forms;
using Fpi.Devices.Channel;
using Fpi.WMS3000.Equipment.Interface;
using Fpi.Xml;
using Sunny.UI;

namespace Fpi.WMS3000.Equipment.UI
{
    public partial class UC_DL10BDeviceState : UIUserControl, IRefreshUI
    {
        #region 字段属性

        private DL10BEquipment _device;

        /// <summary>
        /// 子控件列表
        /// </summary>
        private List<IRefreshUI> _ucList = new List<IRefreshUI>();

        #endregion

        #region 构造

        public UC_DL10BDeviceState()
        {
            InitializeComponent();
        }

        #endregion

        #region 公共方法

        public void SetTragetDevice(DL10BEquipment device)
        {
            _device = device;
            SetDataChannels(_device.InValueChannels);
        }

        /// <summary>
        /// 设置界面关联的数据通道
        /// </summary>
        /// <param name="dataChannels"></param>
        internal void SetDataChannels(NodeList dataChannels)
        {
            this.pnlMain.Controls.Clear();
            _ucList.Clear();

            foreach(DataChannel channel in dataChannels)
            {
                if(!string.IsNullOrEmpty(channel.VarNodeId))
                {
                    UC_OneTempNodeValue nodePanel = new UC_OneTempNodeValue(channel.VarNodeId);
                    _ucList.Add(nodePanel);
                    this.pnlMain.Controls.Add(nodePanel);
                }
            }
        }

        #endregion

        #region 事件

        private void UC_DL10BDeviceState_Paint(object sender, PaintEventArgs e)
        {
            if(!string.IsNullOrWhiteSpace(this.Text))
            {
                gbDatas.Text = this.Text;
            }
        }

        #endregion


        #region IRefreshUI

        /// <summary>
        /// 刷新界面数据显示
        /// </summary>
        public void RefreshUI()
        {
            foreach(var uc in _ucList)
            {
                uc.RefreshUI();
            }
        }

        #endregion
    }
}