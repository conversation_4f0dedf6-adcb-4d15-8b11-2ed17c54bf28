﻿using System.Collections.Generic;
using System.ComponentModel;
using Fpi.WMS3000.Equipment;

namespace Fpi.WMS3000.SystemConfig.ImagePatrol.Config
{
    /// <summary>
    /// 总磷总氮质控仪巡检结果
    /// </summary>
    public class TPTNQCDPatrolResult : ImageUnitPatrolResultBase
    {
        #region 字段属性

        #region 总磷质控仪

        /// <summary>
        /// 总磷质控水样管状态
        /// </summary>
        [Description("总磷质控水样管状态")]
        public eSmutState TPWaterTubeSmutState { get; set; }

        /// <summary>
        /// 总磷质控标样管状态
        /// </summary>
        [Description("总磷质控标样管状态")]
        public eSmutState TPSampleTubeSmutState { get; set; }

        /// <summary>
        /// 总磷质控水样杯脏污
        /// </summary>
        [Description("总磷质控水样杯脏污")]
        public eSmutState TPWaterCupSmutState { get; set; }

        /// <summary>
        /// 总磷质控标样杯脏污
        /// </summary>
        [Description("总磷质控标样杯脏污")]
        public eSmutState TPSampleCupSmutState { get; set; }

        #endregion

        #region 总氮质控仪

        /// <summary>
        /// 总氮质控水样管状态
        /// </summary>
        [Description("总氮质控水样管状态")]
        public eSmutState TNWaterTubeSmutState { get; set; }

        /// <summary>
        /// 总氮质控标样管状态
        /// </summary>
        [Description("总氮质控标样管状态")]
        public eSmutState TNSampleTubeSmutState { get; set; }

        /// <summary>
        /// 总氮质控水样杯脏污
        /// </summary>
        [Description("总氮质控水样杯脏污")]
        public eSmutState TNWaterCupSmutState { get; set; }

        /// <summary>
        /// 总氮质控标样杯脏污
        /// </summary>
        [Description("总氮质控标样杯脏污")]
        public eSmutState TNSampleCupSmutState { get; set; }

        #endregion

        #region 水桶

        /// <summary>
        /// 总磷总氮废水桶状态
        /// </summary>
        [Description("总磷总氮废水桶状态")]
        public eEarlyWarnState TPTNWasteWaterBucketState { get; set; }

        /// <summary>
        /// 总磷总氮废液桶状态
        /// </summary>
        [Description("总磷总氮废液桶状态")]
        public eEarlyWarnState TPTNWasteTankBucketState { get; set; }

        /// <summary>
        /// 总磷总氮纯水桶状态
        /// </summary>
        [Description("总磷总氮纯水桶状态")]
        public eEarlyWarnState TPTNWaterBucketState { get; set; }

        #endregion

        #endregion

        #region 构造

        public TPTNQCDPatrolResult()
        {
            UnitId = "TPTNQCD";
            UnitName = "总磷总氮质控仪";
        }

        #endregion

        #region 方法重写

        /// <summary>
        /// 打印巡检结果
        /// </summary>
        /// <returns></returns>
        public override List<string> GetResultStr()
        {
            return new List<string>
            {
                $"总磷质控水样管状态：{TPWaterTubeSmutState}",
                $"总磷质控标样管状态：{TPSampleTubeSmutState}",
                $"总磷质控水样杯脏污：{TPWaterCupSmutState}",
                $"总磷质控标样杯脏污：{TPSampleCupSmutState}",
                $"总氮质控水样管状态：{TNWaterTubeSmutState}",
                $"总氮质控标样管状态：{TNSampleTubeSmutState}",
                $"总氮质控水样杯脏污：{TNWaterCupSmutState}",
                $"总氮质控标样杯脏污：{TNSampleCupSmutState}",
                $"总磷总氮废水桶状态：{TPTNWasteWaterBucketState}",
                $"总磷总氮废液桶状态：{TPTNWasteTankBucketState}",
                $"总磷总氮纯水桶状态：{TPTNWaterBucketState}"
            };
        }

        #endregion
    }
}