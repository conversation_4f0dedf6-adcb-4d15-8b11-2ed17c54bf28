using System.Collections.Generic;
using System.ComponentModel;
using Fpi.Util.Extensions;
using Fpi.WMS3000.Equipment;
using Fpi.WMS3000.Equipment.Config;
using Fpi.WMS3000.SystemConfig.SmartPatrol.Helper;

namespace Fpi.WMS3000.SystemConfig.ImagePatrol.Config
{
    /// <summary>
    /// 采水点巡检结果
    /// </summary>
    public class WaterPointPatrolResult : ImageUnitPatrolResultBase
    {
        #region 字段属性

        /// <summary>
        /// 采水装置偏移状态
        /// </summary>
        [Description("采水装置偏移状态")]
        public eWaterTrapMigration WaterTrapMigration { get; set; }

        /// <summary>
        /// 采水点水面漂浮物
        /// </summary>
        [Description("采水点水面漂浮物")]
        public eModuleWorkingState WaterTrapFloat { get; set; }

        /// <summary>
        /// 采水点人员入侵
        /// </summary>
        [Description("采水点人员入侵")]
        public eModuleWorkingState WaterTrapIntrusion { get; set; }

        /// <summary>
        /// 采水点水体颜色
        /// </summary>
        [Description("采水点水体颜色")]
        public eWaterColor WaterTrapWaterColor { get; set; }

        /// <summary>
        /// 采水点断面实际水位
        /// </summary>
        [Description("采水点断面实际水位")]
        public double WaterTrapWaterLevel { get; set; }

        #endregion

        #region 构造

        public WaterPointPatrolResult()
        {
            UnitId = "WaterPoint";
            UnitName = "采水点";
        }

        #endregion

        #region 方法重写

        /// <summary>
        /// 打印巡检结果
        /// </summary>
        /// <returns></returns>
        public override List<string> GetResultStr()
        {
            return new List<string>
            {
                $"采水装置偏移状态：{WaterTrapMigration}",
                $"采水点水面漂浮物：{WaterTrapFloat}",
                $"采水点人员入侵：{WaterTrapIntrusion}",
                $"采水点水体颜色：{WaterTrapWaterColor}",
                $"采水点断面实际水位：{WaterTrapWaterLevel.ToDisplayFormat(PatrolDataDisplayHelper.FloatFormat, "℃")}"
            };
        }

        #endregion
    }
}
