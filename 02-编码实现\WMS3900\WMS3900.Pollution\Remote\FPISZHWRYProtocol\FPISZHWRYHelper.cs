﻿using System;
using System.Collections.Generic;
using System.Text;
using Fpi.Data.Config;
using Fpi.DB;
using Fpi.HB.Business.Protocols.Helper;
using Fpi.WMS3000.Equipment.Config;
using Fpi.WMS3000.Pollution.DB;
using Fpi.WMS3000.Remote.GJDBS;
using Fpi.WMS3000.Remote.GJDBS.Config;
using Fpi.WMS3000.Remote.GJDBS.GJDBSDataFrame;
using Fpi.Xml;

namespace Fpi.WMS3000.Pollution.Remote.FPISZHWRY
{
    /// <summary>
    /// 谱育科技数智化污染源数据传输协议-数据处理帮助类
    /// </summary>
    public static class FPISZHWRYHelper
    {
        #region 实时数据组装

        /// <summary>
        /// 实时数据组装
        /// </summary>
        /// <param name="gjdbsDesc"></param>
        /// <returns></returns>
        public static GJDBSCommand BuildRealTimeData(GJDBSProtocolDesc gjdbsDesc)
        {
            GJDBSCommand gjdbsCmd = null;
            var gjdbsSingleCfg = gjdbsDesc.GjdbsSingleCfg;
            if(gjdbsSingleCfg != null)
            {
                //从数据库取数
                if(gjdbsSingleCfg.FromDB)
                {
                    var list = GetRealDataFromDb(gjdbsDesc, DateTime.Now.AddMinutes(-1), DateTime.Now, eGetDataCount.最新一条数据);
                    if(list != null && list.Count > 0)
                    {
                        gjdbsCmd = list[0];
                        if(gjdbsCmd != null)
                        {
                            ChangeGbPolParameterFlag(gjdbsCmd);
                            return gjdbsCmd;
                        }
                    }
                }

                // 不从数据库获取，或数据库中没有
                gjdbsCmd = GetRealDataFromSys(gjdbsDesc);
            }

            return gjdbsCmd;
        }

        /// <summary>
        /// 开关量数据组装
        /// </summary>
        /// <param name="gjdbsDesc"></param>
        /// <returns></returns>
        public static GJDBSCommand BuildStateNodeData(GJDBSProtocolDesc gjdbsDesc)
        {
            GJDBSCommand gbCmd = null;
            var gjdbsSingleCfg = gjdbsDesc.GjdbsSingleCfg;
            if(gjdbsSingleCfg != null)
            {
                if(gjdbsSingleCfg.StateNodes.GetCount() > 0)
                {
                    gbCmd = new GJDBSCommand
                    {
                        QN = DateTime.Now.ToString(GJDBSProtocolDesc.QNDateTimeFormat),
                        ST = gjdbsDesc.ST,
                        CN = "2011",
                        PW = gjdbsDesc.PW,
                        MN = gjdbsDesc.MN,
                        Flag = gjdbsSingleCfg.Flag,
                        CP = new GJDBSCommandParameter()
                    };

                    var recordTime = DateTime.Now;
                    if(recordTime.Second % 30 != 0)
                    {
                        recordTime = recordTime.AddSeconds(-(recordTime.Second % 30));
                    }
                    gbCmd.CP.DataTime = recordTime.ToString(GJDBSProtocolDesc.DateTimeFormat);
                    foreach(GJDBSPolNode gbNode in gjdbsSingleCfg.StateNodes)
                    {
                        StateNode stateNode = DataManager.GetInstance().GetStateNodeById(gbNode.id);
                        if(stateNode == null)
                        {
                            continue;
                        }

                        var polParameter = new GJDBSPolParameter
                        {
                            PolId = gbNode.GbID,
                            Format = "F0"
                        };

                        int originData;
                        try
                        {
                            originData = stateNode.GetValue() ? 1 : 0;
                        }
                        catch
                        {
                            originData = 0;
                        }


                        // 转换标志位
                        polParameter.Flag = (eValueNodeState.N).ToString();
                        polParameter.Rtd = originData;

                        if(!gbCmd.CP.PolCmdParameter.Contains(polParameter))
                        {
                            gbCmd.CP.PolCmdParameter.Add(polParameter);
                        }
                    }
                }

            }
            ChangeGbPolParameterFlag(gbCmd);
            return gbCmd;
        }

        /// <summary>
        /// 从当前数据获取实时数据
        /// </summary>
        /// <param name="gjdbsDesc"></param>
        /// <returns></returns>
        private static GJDBSCommand GetRealDataFromSys(GJDBSProtocolDesc gjdbsDesc)
        {
            GJDBSCommand gbCmd = null;
            var gjdbsSingleCfg = gjdbsDesc.GjdbsSingleCfg;
            if(gjdbsSingleCfg != null && gjdbsSingleCfg.PolNodes.GetCount() > 0)
            {
                gbCmd = new GJDBSCommand
                {
                    QN = DateTime.Now.ToString(GJDBSProtocolDesc.QNDateTimeFormat),
                    ST = gjdbsDesc.ST,
                    CN = "2011",
                    PW = gjdbsDesc.PW,
                    MN = gjdbsDesc.MN,
                    Flag = gjdbsSingleCfg.Flag,
                    CP = new GJDBSCommandParameter()
                };

                var recordTime = DateTime.Now;
                if(recordTime.Second % 30 != 0)
                {
                    recordTime = recordTime.AddSeconds(-(recordTime.Second % 30));
                }
                gbCmd.CP.DataTime = recordTime.ToString(GJDBSProtocolDesc.DateTimeFormat);

                foreach(GJDBSPolNode gbNode in gjdbsSingleCfg.PolNodes)
                {
                    var valueNode = DataManager.GetInstance().GetValueNodeById(gbNode.id);
                    if(valueNode == null)
                    {
                        continue;
                    }

                    var polParameter = new GJDBSPolParameter
                    {
                        PolId = gbNode.GbID,
                        Format = "F" + gbNode.Format
                    };

                    float originData;
                    try
                    {
                        originData = (float)valueNode.GetValue();
                    }
                    catch
                    {
                        originData = 0;
                    }

                    int state;
                    try
                    {
                        state = valueNode.State;
                    }
                    catch
                    {
                        state = (int)eValueNodeState.F;
                    }
                    // 转换标志位
                    polParameter.Flag = ((eValueNodeState)state).ToString();
                    polParameter.Rtd = UnitManager.GetInstance().TransFromSelfUnitValue(valueNode, originData, gbNode.UnitID);

                    if(!gbCmd.CP.PolCmdParameter.Contains(polParameter))
                    {
                        gbCmd.CP.PolCmdParameter.Add(polParameter);
                    }
                }
            }

            ChangeGbPolParameterFlag(gbCmd);

            return gbCmd;
        }

        /// <summary>
        /// 实时数据组装
        /// </summary>
        /// <param name="beginTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="gjdbsDesc"></param>
        /// <param name="dataCount"></param>
        /// <returns></returns>
        public static List<GJDBSCommand> BuildRealTimeData(GJDBSProtocolDesc gjdbsDesc, DateTime beginTime, DateTime endTime, eGetDataCount dataCount, string qnDateTime = "")
        {
            var cmdList = new List<GJDBSCommand>();
            var gjdbsSingleCfg = gjdbsDesc.GjdbsSingleCfg;
            if(gjdbsSingleCfg != null)
            {
                // 从数据库取数
                cmdList = GetRealDataFromDb(gjdbsDesc, beginTime, endTime, dataCount, qnDateTime);
            }

            if(cmdList != null)
            {
                foreach(var gjdbsCmd in cmdList)
                {
                    ChangeGbPolParameterFlag(gjdbsCmd);
                }
            }

            return cmdList;
        }

        /// <summary>
        /// 从数据库组装实时数据
        /// </summary>
        /// <param name="beginTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="gjdbsDesc"></param>
        /// <param name="dataCount"></param>
        /// <returns></returns>
        private static List<GJDBSCommand> GetRealDataFromDb(GJDBSProtocolDesc gjdbsDesc, DateTime beginTime, DateTime endTime, eGetDataCount dataCount, string qnDateTime = "")
        {
            var cmdList = new List<GJDBSCommand>();
            var gjdbsSingleCfg = gjdbsDesc.GjdbsSingleCfg;

            if(gjdbsSingleCfg != null && gjdbsSingleCfg.PolNodes.GetCount() > 0)
            {
                #region 查询语句

                // 查询语句
                var sqlStr = new StringBuilder($"select * from {DbConfig.POLLUTION_MEASURE_DATA_TABLE} where datatype='{(int)ePollutionDataType.实时数据}'");
                // 取最新一组数据
                if(dataCount == eGetDataCount.最新一条数据)
                {
                    sqlStr.Append($" order by datatime Desc limit 0,1");
                }
                // 按时间筛选数据
                else
                {
                    sqlStr.Append($" and datatime>='{beginTime.ToString(DbConfig.DATETIME_FORMAT)}' and datatime<='{endTime.ToString(DbConfig.DATETIME_FORMAT)}' order by datatime Desc");
                }

                #endregion

                using(var reader = DbAccess.ExecuteQueryReturnDataReader(sqlStr.ToString()))
                {
                    while(reader.Read()) //没有符合条件的查询记录
                    {
                        if(gjdbsSingleCfg.PolNodes.GetCount() > 0)
                        {
                            var gjdbsCmd = new GJDBSCommand
                            {
                                QN = DateTime.Now.ToString(GJDBSProtocolDesc.QNDateTimeFormat),
                                ST = gjdbsDesc.ST,
                                CN = "2011",
                                PW = gjdbsDesc.PW,
                                MN = gjdbsDesc.MN,
                                Flag = gjdbsSingleCfg.Flag,
                                CP = new GJDBSCommandParameter()
                            };
                            if(!string.IsNullOrWhiteSpace(qnDateTime))
                            {
                                gjdbsCmd.QN = qnDateTime;
                            }

                            var recordTime = Convert.ToDateTime(reader["datatime"]);
                            if(recordTime.Second % 30 != 0)
                            {
                                recordTime = recordTime.AddSeconds(-(recordTime.Second % 30));
                            }
                            gjdbsCmd.CP.DataTime = recordTime.ToString(GJDBSProtocolDesc.DateTimeFormat);

                            foreach(IdNameNode node in gjdbsSingleCfg.PolNodes)
                            {
                                var gbNode = node as GJDBSPolNode;
                                var valueNode = DataManager.GetInstance().GetValueNodeById(node.id);
                                if(valueNode == null)
                                {
                                    continue;
                                }

                                var polParameter = new GJDBSPolParameter
                                {
                                    PolId = gbNode.GbID,
                                    Format = "F" + gbNode.Format
                                };
                                float originData;
                                try
                                {
                                    originData = Convert.ToSingle(reader[$"{DbConfig.PREFIX_F}{gbNode.id}"]);
                                }
                                catch
                                {
                                    originData = 0;
                                }
                                int state;
                                try
                                {
                                    state = Convert.ToInt32(reader[$"{DbConfig.PREFIX_F}{gbNode.id}{DbConfig.POSTFIX}"]);
                                }
                                catch
                                {
                                    state = (int)eValueNodeState.F;
                                }

                                polParameter.Rtd = UnitManager.GetInstance().TransFromSelfUnitValue(valueNode, originData, gbNode.UnitID);
                                // 转换标志位
                                polParameter.Flag = ((eValueNodeState)state).ToString();

                                gjdbsCmd.CP.PolCmdParameter.Add(polParameter);
                            }

                            cmdList.Add(gjdbsCmd);
                        }
                    }
                }
            }
            return cmdList;
        }

        #endregion

        #region 统计数据组装

        /// <summary>
        /// 统计数据组装
        /// </summary>
        /// <param name="gjdbsDesc">协议配置</param>
        /// <param name="beginTime">开始时间</param>
        /// <param name="endTime">结束时间</param>
        /// <param name="dataType">数据类型</param>
        /// <param name="dataCount">数量要求</param>
        /// <returns></returns>
        public static List<GJDBSCommand> BuildHistoryData(GJDBSProtocolDesc gjdbsDesc, DateTime beginTime, DateTime endTime, eUploadDataType dataType, eGetDataCount dataCount, string qnDateTime = "")
        {
            ePollutionDataType pollutionDataType = ePollutionDataType.十分钟数据;
            string cn = string.Empty;
            switch(dataType)
            {
                case eUploadDataType.小时数据:
                    pollutionDataType = ePollutionDataType.小时数据;
                    cn = "2061";
                    break;
                case eUploadDataType.分钟数据:
                    pollutionDataType = ePollutionDataType.十分钟数据;
                    cn = "2051";
                    break;
                case eUploadDataType.日数据:
                    pollutionDataType = ePollutionDataType.日数据;
                    cn = "2031";
                    break;
                // 只处理这些类型
                default:
                    return null;
            }

            // 累计流量因子
            var totalFlowNodeId = ExterEquipConfigManager.GetInstance().PollutionDataSaveConfigInfo.TotalFlowNodeId;
            // 瞬时流量因子
            var realTimeFlowNodeId = ExterEquipConfigManager.GetInstance().PollutionDataSaveConfigInfo.RealTimeFlowNodeId;

            var cmdList = new List<GJDBSCommand>();
            var gjdbsSingleCfg = gjdbsDesc.GjdbsSingleCfg;
            if(gjdbsSingleCfg != null && gjdbsSingleCfg.PolNodes.GetCount() > 0)
            {
                #region 查询语句

                var sqlStr = new StringBuilder($"select * from {DbConfig.POLLUTION_MEASURE_DATA_TABLE} where datatime>='{beginTime.ToString(DbConfig.DATETIME_FORMAT)}' and datatime<='{endTime.ToString(DbConfig.DATETIME_FORMAT)}' and datatype='{(int)pollutionDataType}' order by datatime Desc");

                if(dataCount == eGetDataCount.最新一条数据)
                {
                    sqlStr.Append(" limit 0,1 ");
                }

                #endregion

                // 组装各因子平均值，状态
                using(var reader = DbAccess.ExecuteQueryReturnDataReader(sqlStr.ToString()))
                {
                    while(reader.Read()) //没有符合条件的查询记录
                    {
                        var gbCmd = new GJDBSCommand
                        {
                            QN = DateTime.Now.ToString(GJDBSProtocolDesc.QNDateTimeFormat),
                            ST = gjdbsDesc.ST,
                            CN = cn,
                            PW = gjdbsDesc.PW,
                            MN = gjdbsDesc.MN,
                            Flag = gjdbsSingleCfg.Flag,
                            CP = new GJDBSCommandParameter()
                        };

                        if(!string.IsNullOrWhiteSpace(qnDateTime))
                        {
                            gbCmd.QN = qnDateTime;
                        }

                        var recordTime = Convert.ToDateTime(reader["datatime"]);
                        gbCmd.CP.DataTime = recordTime.ToString(GJDBSProtocolDesc.DateTimeFormat);

                        // 获取累积流量
                        double totalFlowValue = double.NaN;
                        try
                        {
                            totalFlowValue = Convert.ToDouble(reader[$"{DbConfig.PREFIX_F}{totalFlowNodeId}"]);
                        }
                        catch
                        {
                        }

                        foreach(GJDBSPolNode gbNode in gjdbsSingleCfg.PolNodes)
                        {
                            var valueNode = DataManager.GetInstance().GetValueNodeById(gbNode.id);
                            // 因子不存在，或者为累计流量
                            if(valueNode == null || valueNode.id == totalFlowNodeId)
                            {
                                continue;
                            }

                            var polParameter = new GJDBSPolParameter
                            {
                                PolId = gbNode.GbID,
                                Format = "F" + gbNode.Format
                            };

                            int state;
                            var value = double.NaN;
                            try
                            {
                                state = Convert.ToInt32(reader[$"{DbConfig.PREFIX_F}{gbNode.id}{DbConfig.POSTFIX}"]);
                            }
                            catch
                            {
                                state = (int)eValueNodeState.F;
                            }

                            try
                            {
                                var originValue = Convert.ToDouble(reader[$"{DbConfig.PREFIX_F}{gbNode.id}"]);
                                value = UnitManager.GetInstance().TransFromSelfUnitValue(valueNode, originValue, gbNode.UnitID);
                                polParameter.Avg = value;
                            }
                            // 非合法值，取消上传
                            catch
                            {
                            }

                            // 瞬时流量因子，赋统计值（即累计流量）
                            if(realTimeFlowNodeId == valueNode.id && !double.IsNaN(totalFlowValue))
                            {
                                polParameter.Cou = totalFlowValue;
                            }

                            // 转换标志位
                            polParameter.Flag = ((eValueNodeState)state).ToString();

                            gbCmd.CP.PolCmdParameter.Add(polParameter);
                        }

                        cmdList.Add(gbCmd);
                    }
                }

                // 待查询数据类型
                int queryDataType = GetQueryDataType(pollutionDataType);

                #region 查询语句

                StringBuilder sb = new StringBuilder("SELECT ");
                foreach(GJDBSPolNode gbNode in gjdbsSingleCfg.PolNodes)
                {
                    var valueNode = DataManager.GetInstance().GetValueNodeById(gbNode.id);
                    // 因子不存在，或者为累计流量
                    if(valueNode == null || valueNode.id == totalFlowNodeId)
                    {
                        continue;
                    }

                    string colName = $"{DbConfig.PREFIX_F}{valueNode.id}";
                    sb.Append($"min({colName}),max({colName}),");
                }

                var selectStr = $"{sb.ToString().TrimEnd(',')} FROM {DbConfig.POLLUTION_MEASURE_DATA_TABLE} WHERE datatype='{queryDataType}'";

                #endregion

                // 逐条补充各因子最大值，最小值
                foreach(var cmd in cmdList)
                {
                    if(DateTime.TryParseExact(cmd.CP.DataTime, GJDBSProtocolDesc.DateTimeFormat, null, System.Globalization.DateTimeStyles.None, out var time))
                    {
                        // 查询数据时间范围
                        var (timeStart, timeEnd) = GetPeriodRange(time, pollutionDataType);

                        // 构建具体查询SQL
                        var querySql = $"{selectStr} AND datatime >='{timeStart.ToString(DbConfig.DATETIME_FORMAT)}' AND datatime <='{timeEnd.ToString(DbConfig.DATETIME_FORMAT)}'";

                        // 查询各因子最大最小值
                        using(var reader = DbAccess.ExecuteQueryReturnDataReader(querySql))
                        {
                            while(reader.Read()) // 没有符合条件的查询记录
                            {
                                // 逐项补充因子最大最小值
                                foreach(GJDBSPolParameter polParameter in cmd.CP.PolCmdParameter)
                                {
                                    ValueNode valueNode = null;
                                    foreach(GJDBSPolNode gbNode in gjdbsSingleCfg.PolNodes)
                                    {
                                        // 找到对应的上传因子配置
                                        if(polParameter.PolId == gbNode.GbID)
                                        {
                                            valueNode = DataManager.GetInstance().GetValueNodeById(gbNode.id);
                                            // 因子不存在，或者为累计流量
                                            if(valueNode == null || valueNode.id == totalFlowNodeId)
                                            {
                                                break;
                                            }

                                            // 读取最大值
                                            try
                                            {
                                                var originValue = Convert.ToDouble(reader[$"max({DbConfig.PREFIX_F}{valueNode.id})"]);
                                                polParameter.Max = UnitManager.GetInstance().TransFromSelfUnitValue(valueNode, originValue, gbNode.UnitID);
                                            }
                                            catch
                                            {
                                            }

                                            // 读取最小值
                                            try
                                            {
                                                var originValue = Convert.ToDouble(reader[$"min({DbConfig.PREFIX_F}{valueNode.id})"]);
                                                polParameter.Min = UnitManager.GetInstance().TransFromSelfUnitValue(valueNode, originValue, gbNode.UnitID);
                                            }
                                            catch
                                            {
                                            }

                                            break;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // 修改状态标记
            foreach(var gjdbsCmd in cmdList)
            {
                ChangeGbPolParameterFlag(gjdbsCmd);
            }

            return cmdList;
        }

        #endregion

        #region 辅助方法

        #region 按照平台要求修改数据标识

        /// <summary>
        /// 按照平台要求修改数据标识
        /// </summary>
        /// <param name="gbCmd"></param>
        public static void ChangeGbPolParameterFlag(GJDBSCommand gbCmd)
        {
            if(gbCmd != null && gbCmd.CP != null)
            {
                foreach(var pol in gbCmd.CP.PolCmdParameter)
                {
                    // 状态非空
                    if(!string.IsNullOrEmpty(pol.Flag))
                    {
                        switch(pol.Flag.ToLower())
                        {
                            // 仪器通信故障
                            case "f":
                                pol.Flag = eValueNodeState.B.ToString();
                                break;

                            // 状态集里的标识，不修改传原值
                            // 正常
                            case "n":
                            // 超上限
                            case "t":
                            // 超下限
                            case "l":
                            // 电源故障
                            case "p":
                            // 仪器故障
                            case "d":
                            // 仪器离线
                            case "b":
                            // 取水点无水样
                            case "z":
                            // 手工输入数据
                            case "s":
                            // 维护调试数据
                            case "m":
                            // 手动测量数据
                            case "hd":
                            // 缺试剂
                            case "lr":
                            // 缺纯水
                            case "lp":
                            // 缺水样
                            case "lw":
                            // 缺标样
                            case "ls":
                                break;

                            // 不在状态集里的标识，转为N
                            default:
                                pol.Flag = eValueNodeState.N.ToString();
                                break;
                        }
                    }
                }
            }
        }

        #endregion

        /// <summary>
        /// 获取计算当前数据类型时所用数据时段
        /// </summary>
        /// <param name="time">时间</param>
        /// <param name="dataType">数据类型</param>
        /// <returns>开始时间和结束时间</returns>
        private static (DateTime StartTime, DateTime EndTime) GetPeriodRange(DateTime time, ePollutionDataType dataType)
        {
            switch(dataType)
            {
                case ePollutionDataType.十分钟数据:
                    // 1点0分的数据应统计 1点0分0秒至1点10分0秒 的30秒数据（不含头1点0分0秒，含尾1点10分0秒）
                    return (time.AddSeconds(1), time.AddMinutes(10));
                case ePollutionDataType.小时数据:
                    // 1点的数据应统计 1点0分至2点0分 的10分钟数据（含头1点0分，不含尾2点0分）
                    return (time, time.AddHours(1).AddSeconds(-1));
                case ePollutionDataType.日数据:
                    // 1日的数据应统计 1日0点至2日0点 的小时数据（含头1日0点，不含尾2日0点）
                    return (time, time.AddDays(1).AddSeconds(-1));
                default:
                    return (time, time);
            }
        }

        /// <summary>
        /// 获取计算当前数据类型时应查询的源数据类型
        /// </summary>
        /// <param name="dataType"></param>
        /// <returns></returns>
        private static int GetQueryDataType(ePollutionDataType dataType)
        {
            // 待查询数据类型
            int queryDataType;
            switch(dataType)
            {
                case ePollutionDataType.分钟数据:
                case ePollutionDataType.十分钟数据:
                    queryDataType = (int)ePollutionDataType.实时数据; // 计算分钟或十分钟数据时，查询实时数据
                    break;
                case ePollutionDataType.小时数据:
                    queryDataType = (int)ePollutionDataType.十分钟数据; // 计算小时数据时，查询十分钟数据
                    break;
                case ePollutionDataType.日数据:
                    queryDataType = (int)ePollutionDataType.小时数据; // 计算日数据时，查询小时数据
                    break;
                default:
                    queryDataType = (int)ePollutionDataType.实时数据;
                    break;
            }

            return queryDataType;
        }

        #endregion
    }
}