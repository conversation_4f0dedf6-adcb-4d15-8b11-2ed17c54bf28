﻿using Fpi.Communication.Converter;
using System;
using System.Collections.Concurrent;
using System.ComponentModel;

namespace Fpi.WMS3000.Equipment.SIA3900
{
    /// <summary>
    /// 器件寿命信息
    /// </summary>
    public class SIA3900LifeMonitor
    {
        #region 字段属性

        /// <summary>
        /// 器件寿命信息
        /// </summary>
        [Description("器件寿命信息")]
        public ConcurrentDictionary<eSIA3900DeviceType, SIA3900OneDeviceLifeInfo> DeviceLifeInfos { get; private set; }

        #endregion

        #region 构造

        public SIA3900LifeMonitor()
        {
            DeviceLifeInfos = new ConcurrentDictionary<eSIA3900DeviceType, SIA3900OneDeviceLifeInfo>();
            foreach(var info in Enum.GetValues(typeof(eSIA3900DeviceType)))
            {
                DeviceLifeInfos.TryAdd((eSIA3900DeviceType)info, new SIA3900OneDeviceLifeInfo
                {
                    DeviceType = (eSIA3900DeviceType)info,
                });
            }
        }

        #endregion

        #region 公有方法

        public void UpdataValue(byte[] data, int startIndex)
        {
            if(data.Length < startIndex + 220)
            {
                throw new Exception("读取诊断记录回应数据不完整！");
            }

            for(int i = 0; i < 11; i++)
            {
                SIA3900OneDeviceLifeInfo deviceInfo = new()
                {
                    DeviceType = (eSIA3900DeviceType)DataConverter.GetInstance().ToInt32(data, startIndex + i * 20),
                    DeviceChangeTimeStamp = DataConvertHelper.GetDateTimeFromUnixTimeSeconds2143(data, startIndex + 2 + i * 20),
                    DeviceWorkTime = DataConvertHelper.ParseIntValue2143(data, startIndex + 6 + i * 20),
                    DeviceValidTimeStamp = DataConvertHelper.GetDateTimeFromUnixTimeSeconds2143(data, startIndex + 10 + i * 20),
                    DeviceHealth = (eSIA3900DeviceHealth)DataConverter.GetInstance().ToInt32(data, startIndex + 14 + i * 20),
                    MaxUsageTime = DataConvertHelper.ParseIntValue2143(data, startIndex + 16 + i * 20),
                };

                // 检查类型是否存在
                if(DeviceLifeInfos.Keys.Contains(deviceInfo.DeviceType))
                {
                    DeviceLifeInfos[deviceInfo.DeviceType].DeviceChangeTimeStamp = deviceInfo.DeviceChangeTimeStamp;
                    DeviceLifeInfos[deviceInfo.DeviceType].DeviceWorkTime = deviceInfo.DeviceWorkTime;
                    DeviceLifeInfos[deviceInfo.DeviceType].DeviceValidTimeStamp = deviceInfo.DeviceValidTimeStamp;
                    DeviceLifeInfos[deviceInfo.DeviceType].DeviceHealth = deviceInfo.DeviceHealth;
                    DeviceLifeInfos[deviceInfo.DeviceType].MaxUsageTime = deviceInfo.MaxUsageTime;
                }
            }
        }

        #endregion
    }

    /// <summary>
    /// 设备寿命信息
    /// </summary>
    public class SIA3900OneDeviceLifeInfo
    {
        #region 字段属性

        /// <summary>
        /// 类型
        /// </summary>
        [Description("类型")]
        public eSIA3900DeviceType DeviceType { get; set; }

        /// <summary>
        /// 更换时间
        /// </summary>
        [Description("更换时间")]
        public DateTime DeviceChangeTimeStamp { get; set; } = DateTime.MinValue;

        /// <summary>
        /// 部件已用次数（时长/秒/天）
        /// </summary>
        [Description("部件已用次数（时长/秒/天）")]
        public int DeviceWorkTime { get; set; } = -1;

        /// <summary>
        /// 最大使用次数（时长/秒/天）
        /// </summary>
        [Description("最大使用次数（时长/秒/天）")]
        public int MaxUsageTime { get; set; } = -1;

        /// <summary>
        /// 有效期时间
        /// </summary>
        [Description("有效期时间")]
        public DateTime DeviceValidTimeStamp { get; set; } = DateTime.MinValue;

        /// <summary>
        /// 健康状态
        /// </summary>
        [Description("健康状态")]
        public eSIA3900DeviceHealth DeviceHealth { get; set; } = (eSIA3900DeviceHealth)(-1);

        #endregion
    }
}