{"RootPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\WMS3900.SystemConfig", "ProjectFileName": "Fpi.WMS3900.SystemConfig.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "CustomTask\\ImagePatrolTask.cs"}, {"SourceFile": "DeviceUsageStatistics\\Config\\EquipUsageInfo.cs"}, {"SourceFile": "DeviceUsageStatistics\\UI\\FrmEquipUsageInfoModify.cs"}, {"SourceFile": "DeviceUsageStatistics\\UI\\FrmEquipUsageInfoModify.Designer.cs"}, {"SourceFile": "DeviceUsageStatistics\\UI\\FrmDeviceUsageInfoModify.cs"}, {"SourceFile": "DeviceUsageStatistics\\UI\\FrmDeviceUsageInfoModify.Designer.cs"}, {"SourceFile": "DeviceUsageStatistics\\UI\\UC\\UC_OneEquipUsageInfo.cs"}, {"SourceFile": "DeviceUsageStatistics\\UI\\UC\\UC_OneEquipUsageInfo.Designer.cs"}, {"SourceFile": "DeviceUsageStatistics\\UI\\UC_AllCameraUsageInfos.cs"}, {"SourceFile": "DeviceUsageStatistics\\UI\\UC_AllCameraUsageInfos.Designer.cs"}, {"SourceFile": "DeviceUsageStatistics\\UI\\UC_AllEquipUsageInfos.cs"}, {"SourceFile": "DeviceUsageStatistics\\UI\\UC_AllEquipUsageInfos.Designer.cs"}, {"SourceFile": "ImagePatrol\\Config\\Base\\ImageUnitSmartPatrolBase.cs"}, {"SourceFile": "ImagePatrol\\Config\\Base\\ImageUnitPatrolResultBase.cs"}, {"SourceFile": "ImagePatrol\\Config\\ImagePatrolManager.cs"}, {"SourceFile": "ImagePatrol\\Config\\OldImagePatrolResult.cs"}, {"SourceFile": "ImagePatrol\\Config\\ImagePatrolResult.cs"}, {"SourceFile": "ImagePatrol\\Config\\PatrolDetail\\FiveParamFlowPoolPatrol.cs"}, {"SourceFile": "ImagePatrol\\Config\\PatrolDetail\\FiveParamBucketPatrol.cs"}, {"SourceFile": "ImagePatrol\\Config\\PatrolDetail\\CodMnNH4EquipPatrol.cs"}, {"SourceFile": "ImagePatrol\\Config\\PatrolDetail\\TPTNEquipPatrol.cs"}, {"SourceFile": "ImagePatrol\\Config\\PatrolDetail\\CodMnNH4QCDPatrol.cs"}, {"SourceFile": "ImagePatrol\\Config\\PatrolDetail\\TPTNQCDPatrol.cs"}, {"SourceFile": "ImagePatrol\\Config\\PatrolDetail\\PipePatrol.cs"}, {"SourceFile": "ImagePatrol\\Config\\PatrolDetail\\SandSinkPatrol.cs"}, {"SourceFile": "ImagePatrol\\Config\\ResultDetail\\CodMnNH4QCDPatrolResult.cs"}, {"SourceFile": "ImagePatrol\\Config\\ResultDetail\\FiveParamFlowPoolPatrolResult.cs"}, {"SourceFile": "ImagePatrol\\Config\\ResultDetail\\TPTNEquipPatrolResult.cs"}, {"SourceFile": "ImagePatrol\\Config\\ResultDetail\\TPTNQCDPatrolResult.cs"}, {"SourceFile": "ImagePatrol\\Config\\ResultDetail\\FiveParamBucketPatrolResult.cs"}, {"SourceFile": "ImagePatrol\\Config\\ResultDetail\\CodMnNH4EquipPatrolResult.cs"}, {"SourceFile": "ImagePatrol\\Config\\ResultDetail\\SandSinkPatrolResult.cs"}, {"SourceFile": "ImagePatrol\\Config\\ResultDetail\\PipePatrolResult.cs"}, {"SourceFile": "ImagePatrol\\Enums.cs"}, {"SourceFile": "ImagePatrol\\ImagePatrolLogHelper.cs"}, {"SourceFile": "ImagePatrol\\UI\\FrmImagePatrolCtrl.cs"}, {"SourceFile": "ImagePatrol\\UI\\FrmImagePatrolCtrl.Designer.cs"}, {"SourceFile": "ImagePatrol\\UI\\FrmOneImageShow.cs"}, {"SourceFile": "ImagePatrol\\UI\\FrmOneImageShow.Designer.cs"}, {"SourceFile": "ImagePatrol\\UI\\FrmImagePatrolResultShow.cs"}, {"SourceFile": "ImagePatrol\\UI\\FrmImagePatrolResultShow.Designer.cs"}, {"SourceFile": "ImagePatrol\\UI\\FrmImagePatrolConfig.cs"}, {"SourceFile": "ImagePatrol\\UI\\FrmImagePatrolConfig.Designer.cs"}, {"SourceFile": "ImagePatrol\\UI\\UC\\UC_ImagePatrolResult.cs"}, {"SourceFile": "ImagePatrol\\UI\\UC\\UC_ImagePatrolResult.Designer.cs"}, {"SourceFile": "ImagePatrol\\UI\\UC\\UC_OneModelImageResult.cs"}, {"SourceFile": "ImagePatrol\\UI\\UC\\UC_OneModelImageResult.Designer.cs"}, {"SourceFile": "NodeRelationAlarm\\Config\\NodeRelationAlarmManager.cs"}, {"SourceFile": "NodeRelationAlarm\\Config\\RelateInfo.cs"}, {"SourceFile": "NodeRelationAlarm\\UI\\FrmNodeRelationAlarmConfig.cs"}, {"SourceFile": "NodeRelationAlarm\\UI\\FrmNodeRelationAlarmConfig.Designer.cs"}, {"SourceFile": "SmartPatrol\\Config\\Base\\ElectrodeUsageStatistics.cs"}, {"SourceFile": "SmartPatrol\\Config\\Base\\ReagentUsageState.cs"}, {"SourceFile": "SmartPatrol\\Config\\PatrolDetail\\TXSFUnitSmartPatrol.cs"}, {"SourceFile": "SmartPatrol\\Config\\PatrolDetail\\FYUnitSmartPatrol.cs"}, {"SourceFile": "SmartPatrol\\Config\\PatrolDetail\\FZUnitSmartPatrol.cs"}, {"SourceFile": "SmartPatrol\\Config\\PatrolDetail\\LYUnitSmartPatrol.cs"}, {"SourceFile": "SmartPatrol\\Config\\PatrolDetail\\YCLUnitSmartPatrol.cs"}, {"SourceFile": "SmartPatrol\\Config\\PatrolDetail\\SJUnitSmartPatrol.cs"}, {"SourceFile": "SmartPatrol\\Config\\PatrolDetail\\SPUnitSmartPatrol.cs"}, {"SourceFile": "SmartPatrol\\Config\\PatrolDetail\\ZKUnitSmartPatrol.cs"}, {"SourceFile": "SmartPatrol\\Config\\PatrolDetail\\FXUnitSmartPatrol.cs"}, {"SourceFile": "SmartPatrol\\Helper\\PatrolDataDisplayHelper.cs"}, {"SourceFile": "SmartPatrol\\Helper\\PatroReportPdfExport.cs"}, {"SourceFile": "SmartPatrol\\Config\\ResultDetail\\FZUnitPatrolResult.cs"}, {"SourceFile": "SmartPatrol\\Config\\ResultDetail\\YCLUnitPatrolResult.cs"}, {"SourceFile": "SmartPatrol\\Config\\ResultDetail\\SPUnitPatrolResult.cs"}, {"SourceFile": "SmartPatrol\\Config\\ResultDetail\\FYUnitPatrolResult.cs"}, {"SourceFile": "SmartPatrol\\Config\\ResultDetail\\LYUnitPatrolResult.cs"}, {"SourceFile": "SmartPatrol\\Config\\ResultDetail\\SJUnitPatrolResult.cs"}, {"SourceFile": "SmartPatrol\\Config\\ResultDetail\\Sub\\QCDEquipResult.cs"}, {"SourceFile": "SmartPatrol\\Config\\Base\\KeyDeviceUsageStatistics.cs"}, {"SourceFile": "SmartPatrol\\Config\\ResultDetail\\Sub\\WCSEquipResult.cs"}, {"SourceFile": "SmartPatrol\\Config\\ResultDetail\\Sub\\SIAEquipResult.cs"}, {"SourceFile": "SmartPatrol\\Config\\ResultDetail\\ZKUnitPatrolResult.cs"}, {"SourceFile": "SmartPatrol\\Config\\ResultDetail\\FXUnitPatrolResult.cs"}, {"SourceFile": "SmartPatrol\\Config\\ResultDetail\\PSUnitPatrolResult.cs"}, {"SourceFile": "SmartPatrol\\Config\\SmartPatrolManager.cs"}, {"SourceFile": "SmartPatrol\\Config\\Base\\SubModelResultBase.cs"}, {"SourceFile": "SmartPatrol\\Config\\PatrolDetail\\PSUnitSmartPatrol.cs"}, {"SourceFile": "SmartPatrol\\Config\\PatrolDetail\\CSUnitSmartPatrol.cs"}, {"SourceFile": "SmartPatrol\\Config\\PatrolDetail\\KZUnitSmartPatrol.cs"}, {"SourceFile": "SmartPatrol\\Config\\SmartPatrolResult.cs"}, {"SourceFile": "SmartPatrol\\Config\\Base\\SingleUnitPatrolResultBase.cs"}, {"SourceFile": "SmartPatrol\\Config\\Base\\SingleUnitSmartPatrolBase.cs"}, {"SourceFile": "SmartPatrol\\Config\\ResultDetail\\CSUnitPatrolResult.cs"}, {"SourceFile": "SmartPatrol\\Config\\ResultDetail\\KZUnitPatrolResult.cs"}, {"SourceFile": "SmartPatrol\\Config\\ResultDetail\\Sub\\ExterCSModelResult.cs"}, {"SourceFile": "SmartPatrol\\Config\\ResultDetail\\Sub\\PumpModelResult.cs"}, {"SourceFile": "SmartPatrol\\Enums.cs"}, {"SourceFile": "SmartPatrol\\Helper\\InspectionStructureBuilder.cs"}, {"SourceFile": "SmartPatrol\\SmartPatrolLogHelper.cs"}, {"SourceFile": "SmartPatrol\\UI\\FrmOnePatrolResultShow.cs"}, {"SourceFile": "SmartPatrol\\UI\\FrmOnePatrolResultShow.Designer.cs"}, {"SourceFile": "SmartPatrol\\UI\\FrmPatrolErrorResultShow.cs"}, {"SourceFile": "SmartPatrol\\UI\\FrmPatrolErrorResultShow.Designer.cs"}, {"SourceFile": "SmartPatrol\\UI\\FrmPatrolResultShow.cs"}, {"SourceFile": "SmartPatrol\\UI\\FrmPatrolResultShow.Designer.cs"}, {"SourceFile": "SmartPatrol\\UI\\FrmSmartPatrolConfig.cs"}, {"SourceFile": "SmartPatrol\\UI\\FrmSmartPatrolConfig.Designer.cs"}, {"SourceFile": "SmartPatrol\\UI\\FrmPatrolResultUploadConfig.cs"}, {"SourceFile": "SmartPatrol\\UI\\FrmPatrolResultUploadConfig.Designer.cs"}, {"SourceFile": "SmartPatrol\\UI\\FrmSmartPatrolCtrl.cs"}, {"SourceFile": "SmartPatrol\\UI\\FrmSmartPatrolCtrl.Designer.cs"}, {"SourceFile": "CustomTask\\SmartPatrolTask.cs"}, {"SourceFile": "CustomTask\\AirConditioningControlTask.cs"}, {"SourceFile": "CustomTask\\AirVentilatorControlTask.cs"}, {"SourceFile": "CustomTask\\DehumidifierControlTask.cs"}, {"SourceFile": "DeviceUsageStatistics\\Config\\DeviceUsageInfo.cs"}, {"SourceFile": "DeviceUsageStatistics\\UsageStatisticsManager.cs"}, {"SourceFile": "DeviceUsageStatistics\\UI\\UC\\UC_OneDeviceUsageInfo.cs"}, {"SourceFile": "DeviceUsageStatistics\\UI\\UC\\UC_OneDeviceUsageInfo.Designer.cs"}, {"SourceFile": "DeviceUsageStatistics\\UI\\UC_AllDeviceUsageInfos.cs"}, {"SourceFile": "DeviceUsageStatistics\\UI\\UC_AllDeviceUsageInfos.Designer.cs"}, {"SourceFile": "DynamicAdd\\FrmConfigDynamic.cs"}, {"SourceFile": "DynamicAdd\\FrmConfigDynamic.Designer.cs"}, {"SourceFile": "DynamicAdd\\DynamicAddManager.cs"}, {"SourceFile": "FiveParamCheck\\Config\\FiveParamCheckManager.cs"}, {"SourceFile": "FiveParamCheck\\Config\\ParamCheckInfo.cs"}, {"SourceFile": "FiveParamCheck\\UI\\FrmFiveParamManualCheck.cs"}, {"SourceFile": "FiveParamCheck\\UI\\FrmFiveParamManualCheck.Designer.cs"}, {"SourceFile": "FiveParamCheck\\UI\\UC_NodeCheckConfig.cs"}, {"SourceFile": "FiveParamCheck\\UI\\UC_NodeCheckConfig.Designer.cs"}, {"SourceFile": "FlowCount\\FormFlowCount.cs"}, {"SourceFile": "FlowCount\\FormFlowCount.designer.cs"}, {"SourceFile": "Properties\\Resources.Designer.cs"}, {"SourceFile": "StationInfo\\StationInfoManager.cs"}, {"SourceFile": "StationInfo\\UI\\FrmStationInfoManager.cs"}, {"SourceFile": "StationInfo\\UI\\FrmStationInfoManager.Designer.cs"}, {"SourceFile": "SystemParam\\UI\\FrmConfigSystemParam.cs"}, {"SourceFile": "SystemParam\\UI\\FrmConfigSystemParam.Designer.cs"}, {"SourceFile": "SystemPowerNode\\Config\\SystemPowerManager.cs"}, {"SourceFile": "SystemPowerNode\\UI\\FormSystemPowerNode.cs"}, {"SourceFile": "SystemPowerNode\\UI\\FormSystemPowerNode.Designer.cs"}, {"SourceFile": "EnumRelated\\Enums.cs"}, {"SourceFile": "ExcepQuit\\Config\\ExcepQuitManager.cs"}, {"SourceFile": "ExcepQuit\\UI\\FormExcepQuitConfig.cs"}, {"SourceFile": "ExcepQuit\\UI\\FormExcepQuitConfig.Designer.cs"}, {"SourceFile": "ManualQC\\Config\\ManualQCPlanManager.cs"}, {"SourceFile": "ManualQC\\Config\\QCPlanInfo.cs"}, {"SourceFile": "ManualQC\\UI\\FrmManualQCConfig.cs"}, {"SourceFile": "ManualQC\\UI\\FrmManualQCConfig.Designer.cs"}, {"SourceFile": "ManualQC\\UI\\FrmManualQC.cs"}, {"SourceFile": "ManualQC\\UI\\FrmManualQC.Designer.cs"}, {"SourceFile": "ManualQC\\UI\\UC_OneQCFlow.cs"}, {"SourceFile": "ManualQC\\UI\\UC_OneQCFlow.Designer.cs"}, {"SourceFile": "Properties\\AssemblyInfo.cs"}, {"SourceFile": "SystemConfig\\SystemHelper.cs"}, {"SourceFile": "SystemConfig\\SystemLogHelper.cs"}, {"SourceFile": "SystemConfig\\SystemManager.cs"}, {"SourceFile": "SystemParam\\Config\\SystemParam.cs"}, {"SourceFile": "SystemParam\\Config\\SystemParamManager.cs"}, {"SourceFile": "SystemParam\\UI\\FrmEditSystemParam.cs"}, {"SourceFile": "SystemParam\\UI\\FrmEditSystemParam.Designer.cs"}, {"SourceFile": "SystemParam\\UI\\FrmSelectNode.cs"}, {"SourceFile": "SystemParam\\UI\\FrmSelectNode.Designer.cs"}, {"SourceFile": "SystemParam\\UI\\UC_ConfigSystemParam.cs"}, {"SourceFile": "SystemParam\\UI\\UC_ConfigSystemParam.Designer.cs"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.8.AssemblyAttributes.cs"}], "References": [{"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Alarm\\bin\\Debug\\Fpi.Alarm.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Alarm\\bin\\Debug\\Fpi.Alarm.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Camera\\bin\\Debug\\Fpi.Camera.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Camera\\bin\\Debug\\Fpi.Camera.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Communication\\bin\\Debug\\Fpi.Communication.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Communication\\bin\\Debug\\Fpi.Communication.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Data\\bin\\Debug\\Fpi.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Data\\bin\\Debug\\Fpi.Data.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.DB\\bin\\Debug\\Fpi.DB.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.DB\\bin\\Debug\\Fpi.DB.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Device\\bin\\Debug\\Fpi.Device.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Device\\bin\\Debug\\Fpi.Device.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.HB.Business\\bin\\Debug\\Fpi.HB.Business.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.HB.Business\\bin\\Debug\\Fpi.HB.Business.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Json\\bin\\Debug\\Fpi.Json.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Json\\bin\\Debug\\Fpi.Json.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Log\\bin\\Debug\\Fpi.Log.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Log\\bin\\Debug\\Fpi.Log.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Operation\\bin\\Debug\\Fpi.Operation.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Operation\\bin\\Debug\\Fpi.Operation.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Timer\\bin\\Debug\\Fpi.Timer.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Timer\\bin\\Debug\\Fpi.Timer.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.UI.Common\\bin\\Debug\\Fpi.UI.Common.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.UI.Common\\bin\\Debug\\Fpi.UI.Common.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.UI.PC\\bin\\Debug\\Fpi.UI.PC.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.UI.PC\\bin\\Debug\\Fpi.UI.PC.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.User\\bin\\Debug\\Fpi.User.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.User\\bin\\Debug\\Fpi.User.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Util\\bin\\Debug\\Fpi.Util.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Util\\bin\\Debug\\Fpi.Util.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\Product\\Debug\\bin\\Fpi.WMS3900.Algorithm.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\Product\\Debug\\bin\\Fpi.WMS3900.Algorithm.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\Product\\Debug\\bin\\Fpi.WMS3900.DB.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\Product\\Debug\\bin\\Fpi.WMS3900.DB.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\Product\\Debug\\bin\\Fpi.WMS3900.Equipment.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\Product\\Debug\\bin\\Fpi.WMS3900.Equipment.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\Product\\Debug\\bin\\Fpi.WMS3900.Inspection.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\Product\\Debug\\bin\\Fpi.WMS3900.Inspection.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Xml\\bin\\Debug\\Fpi.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Xml\\bin\\Debug\\Fpi.Xml.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\FpiDLL\\HZH_Controls.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\FpiDLL\\itextsharp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\FpiDLL\\Newtonsoft.Json.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\FpiDLL\\OpenCvSharp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\PresentationCore.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\PresentationFramework.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\FpiDLL\\SunnyUI.Common.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\FpiDLL\\SunnyUI.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Design.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Drawing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Web.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Windows.Forms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Xaml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\UIAutomationProvider.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\WindowsBase.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\WindowsFormsIntegration.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\FpiDLL\\WinFormsUI.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\Product\\Debug\\bin\\Fpi.WMS3900.SystemConfig.dll", "OutputItemRelativePath": "Fpi.WMS3900.SystemConfig.dll"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}