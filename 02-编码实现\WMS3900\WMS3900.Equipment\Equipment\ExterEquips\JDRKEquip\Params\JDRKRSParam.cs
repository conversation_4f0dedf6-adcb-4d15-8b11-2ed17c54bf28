﻿using System;
using System.ComponentModel;
using Fpi.Communication.Converter;

namespace Fpi.WMS3000.Equipment.JDRKRS
{
    /// <summary>
    /// 建大仁科RS-KTC-N01-1空调控制器参数
    /// </summary>
    public class JDRKRSParam
    {
        #region 字段属性

        /// <summary>
        /// 温度 单位℃
        /// </summary>
        [Description("温度(℃)")]
        public double Temp { get; set; } = double.NaN;

        /// <summary>
        /// 湿度 单位%
        /// </summary>
        [Description("湿度(%)")]
        public double Humidity { get; set; } = double.NaN;

        /// <summary>
        /// 互感电流 单位A
        /// </summary>
        [Description("互感电流(A)")]
        public double ACurrent { get; set; } = double.NaN;

        ///// <summary>
        ///// B相电流 单位A
        ///// </summary>
        //[Description("B相电流(A)")]
        //public double BCurrent { get; set; } = double.NaN;

        ///// <summary>
        ///// C相电流 单位A
        ///// </summary>
        //[Description("C相电流(A)")]
        //public double CCurrent { get; set; } = double.NaN;

        /// <summary>
        /// 当前状态
        /// </summary>
        [Description("当前状态")]
        public eACState DeviceState { get; set; } = eACState.关机;

        #endregion

        #region 公共方法

        /// <summary>
        /// 更新参数
        /// </summary>
        /// <param name="data"></param>
        public void UpdateParam(byte[] data, int startIndex)
        {
            if(data.Length < startIndex + 12)
            {
                throw new Exception("读取空调控制器参数回应数据不完整！");
            }

            Humidity = DataConverter.GetInstance().ToInt32(data, startIndex) / 10f;
            Temp = DataConverter.GetInstance().ToInt32(data, startIndex + 2) / 10f;
            ACurrent = DataConverter.GetInstance().ToInt32(data, startIndex + 8) / 10f;

            //ACurrent = DataConverter.GetInstance().ToInt32(data, startIndex + 4) / 10f;
            //BCurrent = DataConverter.GetInstance().ToInt32(data, startIndex + 6) / 10f;
            //CCurrent = DataConverter.GetInstance().ToInt32(data, startIndex + 8) / 10f;
            //DeviceState = data[11] == 2 ? eACState.关机 : eACState.开机;
        }

        #endregion
    }
}