﻿using System.Collections.Generic;
using System.ComponentModel;

namespace Fpi.WMS3000.SystemConfig.ImagePatrol.Config
{
    /// <summary>
    /// 一个单元图像巡检结果基类
    /// </summary>
    public class ImageUnitPatrolResultBase
    {
        #region 字段属性

        /// <summary>
        /// 单元编码
        /// </summary>
        [Description("单元编码")]
        public string UnitId { get; set; }

        /// <summary>
        /// 单元名称
        /// </summary>
        [Description("单元名称")]
        public string UnitName { get; set; }

        /// <summary>
        /// 图像存储路径
        /// </summary>
        [Description("图像存储路径")]
        public string ImagePath { get; set; }

        #endregion

        #region 方法重写

        /// <summary>
        /// 打印巡检结果
        /// </summary>
        /// <returns></returns>
        public virtual List<string> GetResultStr()
        {
            return null;
        }

        #endregion
    }
}