﻿using System.ComponentModel;
using System.Windows.Forms;
using Fpi.Devices;
using Fpi.WMS3000.Equipment.SIA3900;
using Sunny.UI;

namespace Fpi.WMS3000.Equipment.UI
{
    public partial class UC_SIA3900CalibrateCoefficientDetail : UIUserControl
    {
        #region 字段属性

        private SIA3900Equipment _device;

        private SIA3900CalibrateCoefficient _calibrateCoefficient;

        private string _deviceType;

        /// <summary>
        /// 是否显示刷新按钮
        /// </summary>
        [Description("是否显示刷新按钮")]
        public bool IsRefresh
        {
            get => btnRefresh.Visible;
            set => uc_SIA3900CalibrateCoefficient.CanRefresh = btnRefresh.Visible = value;
        }

        #endregion

        #region 构造

        public UC_SIA3900CalibrateCoefficientDetail()
        {
            InitializeComponent();
            uc_SIA3900CalibrateCoefficient.CanRefresh = IsRefresh;
        }

        #endregion

        #region 事件

        private void btnRefresh_Click(object sender, System.EventArgs e)
        {
            if(_device != null)
            {
                _deviceType = _device.TypeDesc;
                _calibrateCoefficient = _device.CalibrateCoefficient;
            }

            RefreshDetail();
        }

        #endregion

        #region 公共方法

        internal void SetTragetDevice(SIA3900Equipment device)
        {
            _device = device;

            if(_device != null)
            {
                _deviceType = _device.TypeDesc;
                _calibrateCoefficient = _device.CalibrateCoefficient;
            }

            InitUI();
        }

        internal void SetTragetData(SIA3900CalibrateCoefficient calibrateCoefficient, string type)
        {
            _deviceType = type;
            _calibrateCoefficient = calibrateCoefficient;

            InitUI();
        }

        #endregion

        #region 私有方法

        private void InitUI()
        {
            if(_deviceType.Equals(eDeviceMeasureType.CODMn.ToString()))
            {
                uc_SIA3900CalibrateCoefficient.SetTragetParams(_calibrateCoefficient?.GZCalibrateCoefficient);
            }
            else
            {
                uc_SIA3900CalibrateCoefficient.SetTragetParams(_calibrateCoefficient?.RoutineCalibrateCoefficient);
                RefreshDetail();
            }
        }

        private void RefreshDetail()
        {
            pnlMain.Controls.Clear();

            if(_calibrateCoefficient != null && _calibrateCoefficient.CalibrateCoefficientDetail != null && _calibrateCoefficient.CalibrateCoefficientDetail.PointCoefficients != null)
            {
                // 详细标定区展示
                int count = _calibrateCoefficient.CalibrateCoefficientDetail.PointCoefficients.Count;
                for(int i = 0; i < count; i++)
                {
                    UC_DeviceParamData deviceParamData = new()
                    {
                        Text = $"标定点{i + 1}参数",
                        CanRefresh = false,
                        Width = pnlMain.Width / 2 - 20,
                        Height = 690
                    };
                    deviceParamData.SetTragetParams(_calibrateCoefficient?.CalibrateCoefficientDetail?.PointCoefficients[i], new Padding(103, 0, 0, 0));

                    pnlMain.Controls.Add(deviceParamData);
                }
            }
        }

        #endregion
    }
}