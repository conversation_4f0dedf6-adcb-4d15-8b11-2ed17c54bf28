﻿using Fpi.Communication.Protocols;
using Fpi.WMS3000.Remote.FPISZH;

namespace Fpi.WMS3000.Pollution.Remote.FPISZHWRY
{
    /// <summary>
    /// 谱育科技数智化污染源数据传输协议
    /// </summary>
    public class FPISZHWRYProtocol : FPISZHProtocol
    {
        /// <summary>
        /// 协议名称
        /// </summary>
        public override string FriendlyName => "谱育科技数智化污染源数据传输协议";

        /// <summary>
        /// 描述
        /// </summary>
        /// <returns></returns>
        protected override ProtocolDesc ConstructProtocolDesc()
        {
            return new FPISZHWRYProtocolDesc();
        }

        /// <summary>
        /// 发送器
        /// </summary>
        /// <returns></returns>
        protected override Sender ConstructSender()
        {
            return new FPISZHWRYSender();
        }

        /// <summary>
        /// 接收器
        /// </summary>
        /// <returns></returns>
        protected override Receiver ConstructReceiver()
        {
            return new FPISZHWRYReceiver();
        }
    }
}
