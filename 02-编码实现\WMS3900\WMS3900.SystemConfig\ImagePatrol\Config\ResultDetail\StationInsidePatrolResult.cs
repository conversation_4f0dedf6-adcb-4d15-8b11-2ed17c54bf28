using System.Collections.Generic;
using System.ComponentModel;
using Fpi.WMS3000.Equipment.Config;

namespace Fpi.WMS3000.SystemConfig.ImagePatrol.Config
{
    /// <summary>
    /// 监测站内巡检结果
    /// </summary>
    public class StationInsidePatrolResult : ImageUnitPatrolResultBase
    {
        #region 字段属性

        /// <summary>
        /// 监测站内人员入侵
        /// </summary>
        [Description("监测站内人员入侵")]
        public eModuleWorkingState StationInsideIntrusion { get; set; }

        /// <summary>
        /// 未穿工服
        /// </summary>
        [Description("未穿工服")]
        public eModuleWorkingState UndressedSuit { get; set; }

        /// <summary>
        /// 人员吸烟
        /// </summary>
        [Description("人员吸烟")]
        public eModuleWorkingState Smoking { get; set; }

        /// <summary>
        /// 物料乱堆放及物品遗留
        /// </summary>
        [Description("物料乱堆放及物品遗留")]
        public eModuleWorkingState MaterialStacking { get; set; }

        #endregion

        #region 构造

        public StationInsidePatrolResult()
        {
            UnitId = "StationInside";
            UnitName = "监测站内";
        }

        #endregion

        #region 方法重写

        /// <summary>
        /// 打印巡检结果
        /// </summary>
        /// <returns></returns>
        public override List<string> GetResultStr()
        {
            return new List<string>
            {
                $"监测站内人员入侵：{StationInsideIntrusion}",
                $"未穿工服：{UndressedSuit}",
                $"人员吸烟：{Smoking}",
                $"物料乱堆放及物品遗留：{MaterialStacking}"
            };
        }

        #endregion
    }
}
