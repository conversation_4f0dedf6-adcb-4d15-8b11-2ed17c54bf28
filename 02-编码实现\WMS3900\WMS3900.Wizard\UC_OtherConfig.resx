﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="btnRemote.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6
        JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAH8UlEQVRYR61XaVCV5xW206bLTDuTzrTa
        H23StD/yp9OZTmuTppOlEuqStolxQYljjTVh4hDUEFZtbRiislzRKg3LhQuxyGY07AKXRUARBBSQRfZV
        uBv3ci+X5XLt03POx8cQy2Uynb4zz7zfOe9Znu+8y/d+61a0r+p0uo/T0tKQmpr6X2A9j7OdYv7/a0/E
        xcVZY2NjJYnL5cJqjfVpOh2io6MI0f8TOIdGoymknF9TUivtSVLCbndCq02G2/0In/4rCxmZObiclSs9
        y6zXarVwzs5hxumEY4Yx8yWg2hKcs0KCcn5HSa209aycNBiRlJSER243snOv4rNr+bj6eYH0LD965BYC
        V67mIffKNeTkXkFuTi4hZw3kil0O+WcRDEYLYmJimMD3ldRKEwIjI6NCYHFxEZ9Rkrz8IuQXFEvPstu9
        iJQULfJZn5eHqv551PQ7UTfgGTzOdvmf5+Eavcjo6LhKYL2SWmlCoG9gEMlEwOVaxLW8IhQUXUdhcan0
        LLOeCRSR7npxIeJrrXj5bDe84rrgdY76x0F6Hme7kqJC5BcWo39giKbAA4Gurl4kJydjYX4B+ZS0+Ho5
        Skr10rO8sOBCCk1BaZkeFWXXEVowhd2pY/DVEdJWAel5nO30pSUUpwzd3T3qGvgiAY0mFm33O6ElAnPz
        82JcWl6BMn2l9CyznteAvqIKVRVl8LuygANZThxcAzzOdpX6MpSV69Ha1rE6AVa2tLRJBWZnZylpJSWq
        RkXlDelZniM9j7OugohV68tRQ0RqKz2Dx9muQl+Bcn0Vmprueiag7gInbZWKympUVdeg+kat9CyznsfH
        xh/Kgh0ZGfnSGF7C+MOJ1RchKydUArR3qyjxjdqbqKm7JT3LTtr7vEhr6upRXVMnOgE/ryGvtB1jAnQg
        cU4ltdKEgMFoEgIzjhnU1Naj7mYDbt5qkJ5lJ+mTk5NQf/uO6Gvrbi/bKHae5EaR2f7hxKSciJxTSa20
        LxBwTNtxq74BtxvuLINlh90hBO40tZCuCVOWKVinrMjOykLjnWZMTXmWGxqbUd/QuDYBo8ksBKanp8mh
        SRKpYJn1PN5ytxXNLfcQFaNBtCYOOdnZuHuvDVGxZ1eVs0lme44zMWnwTMBkslCCRNhsNjQ135NEKli2
        2RQCre0duNfaTlvqPtroua29U549y4o9x5mcMHomYDZbhYDVakULvQE7qWDZZuPxJLR3dElQs9lCZbYi
        MzMT7XSGWNaQVRKGSZNnAhbLEgFyYgcOooJl1vN4V3cvOrse4Ex0LKKpzFlZmeh+0Lum3EH2HMdAO211
        AqS0UokTExOFNTtwEhUss57He/oGJEFPbz96+wYFD+i5p3fAg6zYd9IxbDSbVyfASpvNLgksFgsekMPj
        YH1iYgIG6IPSSwlMtGgttBMuX76Mgf6hNeQM9BGJnp4+WWceCUzTNmMCZrNJWKvo6x8kDAkBnoLhkXEM
        DY/iNJWYVzrPMetOR8XgDO0Mlkfos6tOAcuDZN8/MIwpq80zAYdjVgiYTCYMDg0LhunI5aOX96/dbkdq
        SopsVz62jfQ2BvMUxgwWjIxPYHTJjreaMm6GiRaikeKxbmJykk7TOWhW+xYwAb5qcYmNdCCNjo1LsPsd
        nYjRnEVQaDhCwo4j7PhfERJ+AsH0HBx2Amc+CsTM8V9iwmyTZLxQ0z9NhybunNiwDyM4VEEo+X8UEYlX
        vTf/bCm3NCEwN+dCQkKCVMBgMKGDttuHwWF0CDXK9NjpKOZ73czSXXCa5Ab6up08Hk6LbgCxcedx7vwF
        hJ34G52ETWTvoKoqPuxrpxgcp7m5BRERH+Ndv/eepdxfWSbANx4mYDQa5dAJDgmXQCOjYxL4vcP+eD/g
        KI4c/QABR45Bm6LDOFWJj+lTp6Pk5nzyZASdgq1SvYvxn4gd+7Dvu36H8UlCklS3/nYDAoNCjJT768sE
        +NarVMAo07H/z2/T53MS5/9xEUnalOUVfpbe9IMPgxFwNBD+AZSAkxAp7gOWen9+PhaIY4FBiKLFuEgX
        3fmFBSQmaSXeJB1I/v5HeC08uUyAm0rA5XLDZ4+v7Hmew86ubir9DDIyMnDg4CH6YlI56cjmU5NhWwFV
        x+MOWri++/bDvfhvzM+76NrXJfH6Bodx6B2/5cUoFxJufOMx0+rl9vobO9BEH5GTf4+Qco7TVPBVO5wW
        0s5dPtixczfe3LGLsHvpWe1Zt0ueGUHBoRLPRNv4Yny8xLtLxzJXeCWBaf454T8jXgMOhx2H/d+n0utQ
        UlKKoJAwvEVvsv3Nndi1e49U4e2/vIPE5BRE0vy/sWOnXFj37PXFqVNRoj94yA/79h8Qsn96fTv2+u6T
        OCV0v9SlX2LZrRLgefgFwTsyMrIgPT0dly5doulIlGQXLv5TrtQFRSV0Tafbcmk5Emguz12IRxhtSe/f
        b8GvNm4MZv+NG38d7L15q2zX8zR+gSrHPox88uc4XE2fvW/hmZ/8dDv5fJew7gkCk/gh4eeElwheS9iy
        ecs219ZtfwBj22t/hJeXN557/gW88NsX8eJLr0ytX79hD9nxlmL/Zzds+IHP7za96ubx58nu5Vc2iZ8a
        Y8vW16w/euppH7J9mvANwnLjv95vE75H4NIwniE8R9hEUEmpYB2PcaBvEdif+x8TfkN43H6lz1OEbxLk
        HFir8T7l6qiEHgePsY0aiHt+Ky7tavaMFT7r1v0Hi+MXQM0Mth8AAAAASUVORK5CYII=
</value>
  </data>
  <data name="btnData.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6
        JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAAG4UlEQVRYR71XaUxUVxS2SWNto2kbdahK
        0+WHaWu1bolNjalLNBBN2sQauyX905jaarW17gsgCrINw8BMxwEcQGAAURFQtLjQAgUpVusS0CIOy7DM
        DAwMMyyz8PWc+xQ76YwktvZLvtz33r33fN8957wHM8YPno5JPFy8NyQcX36zuZjvpcePxOPs8YsJMdo0
        DLiH8elXm8D30uNH4nH2CDzdv3ZasWdtADxrJgMfTQQ+eBaHNGloaDJi7bpvgdUTgDWTxLwvjrqHYrMG
        a0mS3pgw8GEA3Bf0cJ/PgoeIczpEaXSoqr2Gj9dtBC5kYPhCtrTGB/3vyRIxeQ1rsJYk6Y3JvSsDMFig
        Rc/yF2ALmgjn8vGI0elxqrQMn2z4AZ6g59EXPAm9wZN90kZzvvdMFDE5NmuwliTpDZl5xRTYlTtgWh4A
        S9A09K0IgGLrJmyPVWHrp6vhCJqCruBAmINf9kkLzfncExQoYnJs1mAtSdIbMuPil2D+bjVaFwfAuHQq
        TEunIHvGeOTNeA76tyfAtGwqjMumoXVZoE/ynM89FItjcmzWYC1J0huyptnj0bTkNTS8NRYNM8bh7tvj
        YJg5Ds0zn0ETjXzfMAp97qFYHJNjswZrSZLekKUXncOTBmuwliTpDVl6dq5YdNfQjLtNzJb/iBSLYjJY
        g7UkSW/I0jIyxaJ7hiYYaJOBNjObmv8dDUSOyWAN1pIkvSE7knZULGpuaUVLq3GExrZ2tHd0wmS2oNNk
        Rlt7h9dcR6dJPGfyGrNFIl/z3IP1DNZgLUnSG7Lk1HSxqNXYJgI/EDZbusWo06UhXqFAV7eVgnaKwGZL
        F45mHkVuzjEUF5dAczgZGm2K4I+aZBQWnkZdfb0ww0g5ku7fgDZFJxa1dbBwh6DJbMalsktYuHAhFixY
        gLlz58Le76DnJjFn67Nh587dJKyFQi5HdORBRB0MlxhxEPLYWCQmqvFT6QURO1X3CAPsmtHJKb1PC53w
        t9paqNRqqFRJmD9/PoaGnOjq6obV2oNSCqxKUkGRqELkiRqEXQXCbwERdUDUTSC2sAbxMdFQKhNRU1NL
        Jcjwb0Ct0QoDnK4H5BRbe3ox5HQhJSUV8+bNhdPlRk+vDbfv/Ik4uQKaJCX2VzmgugNctVIGB4A6G1DV
        BRyj0iv/cODHRCWUZDI5Nc2/gSS1RhiwdHV5sdtqxcDgILTaZMyZMwfu4WE4+vuRm5cPDWUm/sx1qG4D
        dhc1sAO4QSaqzMDFTqCoDdDco9pfuoHkw4cpixr/BhKoVoxuarK/02rtxeDgEBnQCgMMLkN4eASyM9IQ
        9rt08ma7JF5N/XbJBJxpB3JaAB19AlINgP5oBuSUMdaSJL0hi09QieA9PTZi7whttj44qQTJyVIGGB6P
        B6FhB1CQn4ewG5T2/ofiZXT6c3T6k2RATxnIpjKk03VB/jFEx8j9G4iLV4rgfdTZNttD2u12uN1u6oEU
        zJ49W6wBhrF7TwhKik4hnJqtrldKO5+cxU8RT9D1SWI+XWfTXElRESIio/0biI5TiNB2hwN9JPqAXG8P
        1Z0NvDNrlljD2LZ9F0pLikW3/3q/5pz2E8TjJHyaMnKGWMDNSPelZ0uwn8rGWpKkN2SHouUicH//AByO
        /hHyvZv+x1NTw02fPl2sIT/Q0Sulz8qC/korsqnG3HBc82wa+eQsXkKZOUnj2Toj8nJy6a1J8G8g4lCM
        CD5IHT8wMOBFp9Mp5hhcDmZjYyN27dqL6spyaOs9UDVIzZbVKqWdT36yGyjs9KC6ogJhoeGIT1D7N3Ag
        IkoIuFxO6vKhf9BFJlwulzDDIyMrS4+QkDBcu1KD0/UWYeAIGcjsAPIoC2UGC83VijcmJ/cY7rW0IU4t
        /iCNlWQfQhYWHimCuqnDnSw0CrkvGJlkYtvWHSgsKED9zRtovFMvWEfXRYVF2Lc3FHy48spqGE09MDec
        Q6X8PZyJWPQu6T4lyZOBkP0HRUDGMAUfjQx+He12B678fhUJCYlYv34Dvv5648gYFR2Hzd9vQ7xSjZTM
        46j7mf7etOcD9ZtRLl/MmRgnyYsMRIigjwO2c+tWHX46fxHlFVVoNBhQWXUZl3+rxT36f2DTlt0oS6Hf
        CXmvAr98TiZOA3e2oFq5dKQck7fv3FMRQh+XPfv2Y/e+sFG55/4YGRWL6Nh4xMqVkCuSqNGSaEwcIX/g
        4pKO4Pi+pVSvQCD3TaD8C/rmn0WlYgkbeJEN8I+FGcT3icueEIP0IavIxCv0oXgD1/M+K61QLB95K/jn
        EpvgHw384EnwdeIifegq5ISsRF5oMIvPI4oM/B/gWgcSFxCXEFl8CvEfr+STBIvxiTkjPNL9mDF/AUmG
        4i67HN9DAAAAAElFTkSuQmCC
</value>
  </data>
</root>