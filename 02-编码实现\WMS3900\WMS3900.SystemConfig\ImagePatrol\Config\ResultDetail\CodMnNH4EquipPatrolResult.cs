﻿using System.Collections.Generic;
using System.ComponentModel;
using Fpi.WMS3000.Equipment;

namespace Fpi.WMS3000.SystemConfig.ImagePatrol.Config
{
    /// <summary>
    /// 高指氨氮分析仪巡检结果
    /// </summary>
    public class CodMnNH4EquipPatrolResult : ImageUnitPatrolResultBase
    {
        #region 字段属性

        #region 高指分析仪

        /// <summary>
        /// 高指管路脏污状态
        /// </summary>
        [Description("高指管路脏污状态")]
        public eSmutState CodMnPipeSmutState { get; set; }

        /// <summary>
        /// 高指反应单元脏污状态
        /// </summary>
        [Description("高指反应单元脏污状态")]
        public eSmutState CodMnReactionUnitSmutState { get; set; }

        #endregion

        #region 氨氮分析仪

        /// <summary>
        /// 氨氮管路脏污状态
        /// </summary>
        [Description("氨氮管路脏污状态")]
        public eSmutState NH4PipeSmutState { get; set; }

        /// <summary>
        /// 氨氮反应单元脏污状态
        /// </summary>
        [Description("氨氮反应单元脏污状态")]
        public eSmutState NH4ReactionUnitSmutState { get; set; }

        #endregion

        #endregion

        #region 构造

        public CodMnNH4EquipPatrolResult()
        {
            UnitId = "CodMnNH4Equip";
            UnitName = "高指氨氮分析仪";
        }

        #endregion

        #region 方法重写

        /// <summary>
        /// 打印巡检结果
        /// </summary>
        /// <returns></returns>
        public override List<string> GetResultStr()
        {
            return new List<string>
            {
                $"高指管路脏污状态：{CodMnPipeSmutState}",
                $"高指反应单元脏污状态：{CodMnReactionUnitSmutState}",
                $"氨氮管路脏污状态：{NH4PipeSmutState}",
                $"氨氮反应单元脏污状态：{NH4ReactionUnitSmutState}"
            };
        }

        #endregion
    }
}