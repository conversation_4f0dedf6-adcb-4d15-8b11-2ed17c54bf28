using System;
using System.Collections.Generic;
using System.ComponentModel;
using Fpi.DB.Manager;
using Fpi.Json;
using Fpi.WMS3000.DB;
using Fpi.WMS3000.Equipment;
using Fpi.WMS3000.Equipment.Config;
using Newtonsoft.Json;

namespace Fpi.WMS3000.SystemConfig.ImagePatrol.Config
{
    /// <summary>
    /// 老版单条图像巡检结果
    /// 数据库转换完成后就不再保留
    /// </summary>
    public class OldImagePatrolResult : BaseJsonNode
    {
        #region 字段属性

        /// <summary>
        /// 巡检开始时间
        /// </summary>
        [Description("巡检开始时间")]
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 触发方式
        [Description("触发方式")]
        /// </summary>
        public eImagePatrolTriggerType PatrolTriggerType { get; set; }

        /// <summary>
        /// 巡检结果列表
        /// 为null时代表这个是老巡检类，需要转换为新巡检类
        /// </summary>
        [Description("巡检结果列表")]
        public List<ImageUnitPatrolResultBase> ModelResultList { get; set; }

        #region 采水点

        /// <summary>
        /// 采水点摄像机图像地址
        /// </summary>
        [Description("采水点摄像机图像地址")]
        public string WaterPointCameraImagePath { get; set; }

        /// <summary>
        /// 采水装置偏移状态
        /// </summary>
        [Description("采水装置偏移状态")]
        public eWaterTrapMigration WaterTrapMigration { get; set; }

        /// <summary>
        /// 采水点水面漂浮物
        /// </summary>
        [Description("采水点水面漂浮物")]
        public eModuleWorkingState WaterTrapFloat { get; set; }

        /// <summary>
        /// 采水点人员入侵
        /// </summary>
        [JsonIgnore]
        [Description("采水点人员入侵")]
        public eModuleWorkingState WaterTrapIntrusion { get; set; }

        /// <summary>
        /// 采水点水体颜色
        /// </summary>
        [Description("采水点水体颜色")]
        public eWaterColor WaterTrapWaterColor { get; set; }

        /// <summary>
        /// 采水点断面实际水位
        /// </summary>
        [Description("采水点断面实际水位")]
        public double WaterTrapWaterLevel { get; set; }

        #endregion

        #region 监测站外

        /// <summary>
        /// 监测站外围摄像机图像地址
        /// </summary>
        [Description("监测站外围摄像机图像地址")]
        public string StationOutsideCameraImagePath { get; set; }

        /// <summary>
        /// 监测站外围人员入侵
        /// </summary>
        [JsonIgnore]
        [Description("监测站外围人员入侵")]
        public eModuleWorkingState StationOutsideIntrusion { get; set; }

        #endregion

        #region 室内

        /// <summary>
        /// 室内摄像机图像地址
        /// </summary>
        [Description("室内摄像机图像地址")]
        public string StationInsideCameraImagePath { get; set; }

        /// <summary>
        /// 监测站内人员入侵
        /// </summary>
        [JsonIgnore]
        [Description("监测站内人员入侵")]
        public eModuleWorkingState StationInsideIntrusion { get; set; }

        /// <summary>
        /// 未穿工服
        /// 实时判断，算法模块调用
        /// </summary>
        [JsonIgnore]
        [Description("未穿工服")]
        public eModuleWorkingState UndressedSuit { get; set; }

        /// <summary>
        /// 人员吸烟
        /// 实时判断，算法模块调用
        /// </summary>
        [JsonIgnore]
        [Description("人员吸烟")]
        public eModuleWorkingState Smoking { get; set; }

        /// <summary>
        /// 物料乱堆放及物品遗留
        /// 实时判断，算法模块调用
        /// </summary>
        [JsonIgnore]
        [Description("物料乱堆放及物品遗留")]
        public eModuleWorkingState MaterialStacking { get; set; }

        #endregion

        #region 配水预处理沉砂池

        /// <summary>
        /// 配水预处理沉砂池摄像机图像地址
        /// </summary>
        [Description("配水预处理沉砂池摄像机图像地址")]
        public string SandSinkCameraImagePath { get; set; }

        /// <summary>
        /// 配水预处理沉砂池脏污状态
        /// </summary>
        [Description("配水预处理沉砂池脏污状态")]
        public eSmutState SandSinkSmutState { get; set; }

        #endregion

        #region 配水预处理管路

        /// <summary>
        /// 配水预处理管路摄像机图像地址
        /// </summary>
        [Description("配水预处理管路摄像机图像地址")]
        public string PipeCameraImagePath { get; set; }

        /// <summary>
        /// 配水预处理管路脏污状态
        /// </summary>
        [Description("配水预处理管路脏污状态")]
        public eSmutState PipeSmutState { get; set; }

        #endregion

        #region 五参数流通池

        /// <summary>
        /// 五参数流通池摄像机图像地址
        /// </summary>
        [Description("五参数流通池摄像机图像地址")]
        public string FiveParamFlowPoolCameraImagePath { get; set; }

        /// <summary>
        /// 五参数流通池脏污状态
        /// </summary>
        [Description("五参数流通池脏污状态")]
        public eSmutState FiveParamFlowPoolState { get; set; }

        #endregion

        #region 五参数水桶

        /// <summary>
        /// 五参数水桶摄像机图像地址
        /// </summary>
        [Description("五参数水桶摄像机图像地址")]
        public string FiveParamBucketCameraImagePath { get; set; }

        ///// <summary>
        ///// 五参数废水桶状态
        ///// </summary>
        //[Description("五参数废水桶状态")]
        //public eEarlyWarnState FiveParamWasteWaterBucketState { get; set; }

        /// <summary>
        /// 五参数废液桶状态
        /// </summary>
        [Description("五参数废液桶状态")]
        public eEarlyWarnState FiveParamWasteTankBucketState { get; set; }

        /// <summary>
        /// 五参数纯水桶状态
        /// </summary>
        [Description("五参数纯水桶状态")]
        public eEarlyWarnState FiveParamWaterBucketState { get; set; }

        #endregion

        #region 高指氨氮分析仪

        /// <summary>
        /// 高指氨氮分析仪摄像机图像地址
        /// </summary>
        [Description("高指氨氮分析仪摄像机图像地址")]
        public string CodMnNH4EquipCameraImagePath { get; set; }

        #region 高指分析仪

        /// <summary>
        /// 高指管路脏污状态
        /// </summary>
        [Description("高指管路脏污状态")]
        public eSmutState CodMnPipeSmutState { get; set; }

        /// <summary>
        /// 高指反应单元脏污状态
        /// </summary>
        [Description("高指反应单元脏污状态")]
        public eSmutState CodMnReactionUnitSmutState { get; set; }

        #endregion

        #region 氨氮分析仪

        /// <summary>
        /// 氨氮管路脏污状态
        /// </summary>
        [Description("氨氮管路脏污状态")]
        public eSmutState NH4PipeSmutState { get; set; }

        /// <summary>
        /// 氨氮反应单元脏污状态
        /// </summary>
        [Description("氨氮反应单元脏污状态")]
        public eSmutState NH4ReactionUnitSmutState { get; set; }

        #endregion

        #endregion

        #region 总磷总氮分析仪

        /// <summary>
        /// 总磷总氮分析仪摄像机图像地址
        /// </summary>
        [Description("总磷总氮分析仪摄像机图像地址")]
        public string TPTNEquipCameraImagePath { get; set; }

        #region 总磷分析仪

        /// <summary>
        /// 总磷管路脏污状态
        /// </summary>
        [Description("总磷管路脏污状态")]
        public eSmutState TPPipeSmutState { get; set; }

        /// <summary>
        /// 总磷反应单元脏污状态
        /// </summary>
        [Description("总磷反应单元脏污状态")]
        public eSmutState TPReactionUnitSmutState { get; set; }

        #endregion

        #region 总氮分析仪

        /// <summary>
        /// 总氮管路脏污状态
        /// </summary>
        [Description("总氮管路脏污状态")]
        public eSmutState TNPipeSmutState { get; set; }

        /// <summary>
        /// 总氮反应单元脏污状态
        /// </summary>
        [Description("总氮反应单元脏污状态")]
        public eSmutState TNReactionUnitSmutState { get; set; }

        #endregion

        #endregion

        #region 高指氨氮质控仪

        /// <summary>
        /// 高指氨氮质控仪摄像机图像地址
        /// </summary>
        [Description("高指氨氮质控仪摄像机图像地址")]
        public string CodMnNH4QCDCameraImagePath { get; set; }

        #region 高指质控仪

        /// <summary>
        /// 高指质控水样管状态
        /// </summary>
        [Description("高指质控水样管状态")]
        public eSmutState CodMnWaterTubeSmutState { get; set; }

        /// <summary>
        /// 高指质控标样管状态
        /// </summary>
        [Description("高指质控标样管状态")]
        public eSmutState CodMnSampleTubeSmutState { get; set; }

        /// <summary>
        /// 高指质控水样杯脏污
        /// </summary>
        [Description("高指质控水样杯脏污")]
        public eSmutState CodMnWaterCupSmutState { get; set; }

        /// <summary>
        /// 高指质控标样杯脏污
        /// </summary>
        [Description("高指质控标样杯脏污")]
        public eSmutState CodMnSampleCupSmutState { get; set; }

        #endregion

        #region 氨氮质控仪

        /// <summary>
        /// 氨氮质控水样管状态
        /// </summary>
        [Description("氨氮质控水样管状态")]
        public eSmutState NH4WaterTubeSmutState { get; set; }

        /// <summary>
        /// 氨氮质控标样管状态
        /// </summary>
        [Description("氨氮质控标样管状态")]
        public eSmutState NH4SampleTubeSmutState { get; set; }

        /// <summary>
        /// 氨氮质控水样杯脏污
        /// </summary>
        [Description("氨氮质控水样杯脏污")]
        public eSmutState NH4WaterCupSmutState { get; set; }

        /// <summary>
        /// 氨氮质控标样杯脏污
        /// </summary>
        [Description("氨氮质控标样杯脏污")]
        public eSmutState NH4SampleCupSmutState { get; set; }

        #endregion

        #region 水桶

        /// <summary>
        /// 高指氨氮废水桶状态
        /// </summary>
        [Description("高指氨氮废水桶状态")]
        public eEarlyWarnState CodMnNH4WasteWaterBucketState { get; set; }

        /// <summary>
        /// 高指氨氮废液桶状态
        /// </summary>
        [Description("高指氨氮废液桶状态")]
        public eEarlyWarnState CodMnNH4WasteTankBucketState { get; set; }

        /// <summary>
        /// 高指氨氮纯水桶状态
        /// </summary>
        [Description("高指氨氮纯水桶状态")]
        public eEarlyWarnState CodMnNH4WaterBucketState { get; set; }

        #endregion

        #endregion

        #region 总磷总氮质控仪

        /// <summary>
        /// 总磷总氮质控仪摄像机图像地址
        /// </summary>
        [Description("总磷总氮质控仪摄像机图像地址")]
        public string TPTNQCDCameraImagePath { get; set; }

        #region 总磷质控仪

        /// <summary>
        /// 总磷质控水样管状态
        /// </summary>
        [Description("总磷质控水样管状态")]
        public eSmutState TPWaterTubeSmutState { get; set; }

        /// <summary>
        /// 总磷质控标样管状态
        /// </summary>
        [Description("总磷质控标样管状态")]
        public eSmutState TPSampleTubeSmutState { get; set; }

        /// <summary>
        /// 总磷质控水样杯脏污
        /// </summary>
        [Description("总磷质控水样杯脏污")]
        public eSmutState TPWaterCupSmutState { get; set; }

        /// <summary>
        /// 总磷质控标样杯脏污
        /// </summary>
        [Description("总磷质控标样杯脏污")]
        public eSmutState TPSampleCupSmutState { get; set; }

        #endregion

        #region 总氮质控仪

        /// <summary>
        /// 总氮质控水样管状态
        /// </summary>
        [Description("总氮质控水样管状态")]
        public eSmutState TNWaterTubeSmutState { get; set; }

        /// <summary>
        /// 总氮质控标样管状态
        /// </summary>
        [Description("总氮质控标样管状态")]
        public eSmutState TNSampleTubeSmutState { get; set; }

        /// <summary>
        /// 总氮质控水样杯脏污
        /// </summary>
        [Description("总氮质控水样杯脏污")]
        public eSmutState TNWaterCupSmutState { get; set; }

        /// <summary>
        /// 总氮质控标样杯脏污
        /// </summary>
        [Description("总氮质控标样杯脏污")]
        public eSmutState TNSampleCupSmutState { get; set; }

        #endregion

        #region 水桶

        /// <summary>
        /// 总磷总氮废水桶状态
        /// </summary>
        [Description("总磷总氮废水桶状态")]
        public eEarlyWarnState TPTNWasteWaterBucketState { get; set; }

        /// <summary>
        /// 总磷总氮废液桶状态
        /// </summary>
        [Description("总磷总氮废液桶状态")]
        public eEarlyWarnState TPTNWasteTankBucketState { get; set; }

        /// <summary>
        /// 总磷总氮纯水桶状态
        /// </summary>
        [Description("总磷总氮纯水桶状态")]
        public eEarlyWarnState TPTNWaterBucketState { get; set; }

        #endregion

        #endregion

        #endregion

        #region 构造

        public OldImagePatrolResult()
        {
            StartTime = DateTime.Now;
        }

        public OldImagePatrolResult(eImagePatrolTriggerType patrolTriggerType)
        {
            PatrolTriggerType = patrolTriggerType;
            StartTime = DateTime.Now;
        }

        #endregion

        #region 公共方法

        #region 数据保存

        [NonSerialized]
        private static readonly object lockObj = new object();

        /// <summary>
        /// 写图像巡检结果数据到数据库
        /// </summary>
        /// <param name="nodeType"></param>
        /// <param name="dataTime"></param>
        /// <param name="checkType"></param>
        /// <param name="keyparams"></param>
        internal void SaveToDb()
        {
            try
            {
                FpiTable table = FpiDataBase.GetInstance().FindTableByName(DbConfig.IMAGEPATROL_RESULT_TABLE);
                if(table == null)
                {
                    throw new Exception("图像巡检结果数据表不存在！");
                }

                lock(lockObj)
                {
                    FpiRow row = new FpiRow();
                    row.SetFieldValue("datatime", this.StartTime);
                    row.SetFieldValue("patrolresult", FpiJsonHelper.ModelToJson(this));
                    table.AddRecord(row);
                }
            }
            catch(Exception e)
            {

                throw new Exception($"保存图像巡检结果数据出错：{e.Message}");
            }
        }

        /// <summary>
        /// 转换格式到新版结构
        /// </summary>
        internal void ConvertToImagePatrolResult()
        {
            ImagePatrolResult newResult = new ImagePatrolResult();

            newResult.StartTime = StartTime;
            newResult.PatrolTriggerType = PatrolTriggerType;

            // 如果是老巡检类，则把每组属性的值转换为ImagePatrolResult类ModelResultList列表中对应值。
            if(ModelResultList == null)
            {
                SandSinkPatrolResult sandSinkPatrolResult = new SandSinkPatrolResult();
                sandSinkPatrolResult.ImagePath = SandSinkCameraImagePath;
                sandSinkPatrolResult.SandSinkSmutState = SandSinkSmutState;



            }

        }

        #endregion

        #endregion
    }
}