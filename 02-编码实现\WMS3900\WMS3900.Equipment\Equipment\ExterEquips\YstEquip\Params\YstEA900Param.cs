﻿using System.ComponentModel;
using Fpi.WMS3000.Equipment.Config;

namespace Fpi.WMS3000.Equipment.UPS
{
    /// <summary>
    /// 易事特EA900系列UPS主机参数
    /// </summary>
    public class YstEA900Param
    {
        #region 字段属性

        /// <summary>
        /// 输入电压 单位V
        /// </summary>
        [Description("输入电压(V)")]
        public double InputVoltage { get; set; } = double.NaN;

        /// <summary>
        /// 输出电压 单位V
        /// </summary>
        [Description("输出电压(V)")]
        public double OutputVoltage { get; set; } = double.NaN;

        /// <summary>
        /// 负载百分比
        /// </summary>
        [Description("负载百分比(%)")]
        public double LoadPercent { get; set; } = double.NaN;

        /// <summary>
        /// 输入频率 单位Hz
        /// </summary>
        [Description("输入频率(Hz)")]
        public double InputFrequency { get; set; } = double.NaN;

        /// <summary>
        /// 电池电压 单位V
        /// </summary>
        [Description("电池电压(V)")]
        public double BatteryVoltage { get; set; } = double.NaN;

        /// <summary>
        /// UPS温度 单位℃
        /// </summary>
        [Description("UPS温度(℃)")]
        public double UPSTemp { get; set; } = double.NaN;

        /// <summary>
        /// 剩余电量 单位%
        /// </summary>
        [Description("剩余电量(%)")]
        public double BatteryLevel { get; set; } = double.NaN;

        /// <summary>
        /// 是否UPS供电
        /// </summary>
        [Description("是否UPS供电")]
        public bool PowerType { get; set; }

        /// <summary>
        /// 告警信息
        /// </summary>
        [Description("告警信息")]
        public eUPSAlarmState AlarmStatus { get; set; }

        /// <summary>
        /// 断电报警
        /// </summary>
        [Description("断电报警")]
        public bool PowerOffAlarm { get; set; }

        #endregion
    }
}