﻿namespace Fpi.WMS3000.Pollution.Remote.FPISZHWRY.ConfigUI
{
    partial class FPISZHWRYConfigUC
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if(disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.gbBase = new System.Windows.Forms.GroupBox();
            this.nuFlag = new System.Windows.Forms.NumericUpDown();
            this.label3 = new System.Windows.Forms.Label();
            this.btnStateConfig = new System.Windows.Forms.Button();
            this.btnDeviceLog = new System.Windows.Forms.Button();
            this.btnSystemParamConfig = new System.Windows.Forms.Button();
            this.label11 = new System.Windows.Forms.Label();
            this.nuCmdInterval = new System.Windows.Forms.NumericUpDown();
            this.btnPolNodeConfig = new System.Windows.Forms.Button();
            this.label1 = new System.Windows.Forms.Label();
            this.txtMN = new System.Windows.Forms.TextBox();
            this.label10 = new System.Windows.Forms.Label();
            this.label2 = new System.Windows.Forms.Label();
            this.cmbST = new System.Windows.Forms.ComboBox();
            this.nuReCount = new System.Windows.Forms.NumericUpDown();
            this.label9 = new System.Windows.Forms.Label();
            this.nuOverTime = new System.Windows.Forms.NumericUpDown();
            this.label5 = new System.Windows.Forms.Label();
            this.txtPwd = new System.Windows.Forms.TextBox();
            this.chkNeedQN = new System.Windows.Forms.CheckBox();
            this.chkNeedFlag = new System.Windows.Forms.CheckBox();
            this.gbReport = new System.Windows.Forms.GroupBox();
            this.chkDataTimeNotCut = new System.Windows.Forms.CheckBox();
            this.chkUpHeart = new System.Windows.Forms.CheckBox();
            this.chkUploadStateNode = new System.Windows.Forms.CheckBox();
            this.chkAutoReportLost = new System.Windows.Forms.CheckBox();
            this.chkFromDb = new System.Windows.Forms.CheckBox();
            this.chkState = new System.Windows.Forms.CheckBox();
            this.label7 = new System.Windows.Forms.Label();
            this.label14 = new System.Windows.Forms.Label();
            this.chkHour = new System.Windows.Forms.CheckBox();
            this.toolTip1 = new System.Windows.Forms.ToolTip(this.components);
            this.chkDay = new System.Windows.Forms.CheckBox();
            this.cmbMinuteSpan = new System.Windows.Forms.ComboBox();
            this.label4 = new System.Windows.Forms.Label();
            this.chkMinute = new System.Windows.Forms.CheckBox();
            this.gbBase.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.nuFlag)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nuCmdInterval)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nuReCount)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.nuOverTime)).BeginInit();
            this.gbReport.SuspendLayout();
            this.SuspendLayout();
            // 
            // gbBase
            // 
            this.gbBase.Controls.Add(this.nuFlag);
            this.gbBase.Controls.Add(this.label3);
            this.gbBase.Controls.Add(this.btnStateConfig);
            this.gbBase.Controls.Add(this.btnDeviceLog);
            this.gbBase.Controls.Add(this.btnSystemParamConfig);
            this.gbBase.Controls.Add(this.label11);
            this.gbBase.Controls.Add(this.nuCmdInterval);
            this.gbBase.Controls.Add(this.btnPolNodeConfig);
            this.gbBase.Controls.Add(this.label1);
            this.gbBase.Controls.Add(this.txtMN);
            this.gbBase.Controls.Add(this.label10);
            this.gbBase.Controls.Add(this.label2);
            this.gbBase.Controls.Add(this.cmbST);
            this.gbBase.Controls.Add(this.nuReCount);
            this.gbBase.Controls.Add(this.label9);
            this.gbBase.Controls.Add(this.nuOverTime);
            this.gbBase.Controls.Add(this.label5);
            this.gbBase.Controls.Add(this.txtPwd);
            this.gbBase.Dock = System.Windows.Forms.DockStyle.Left;
            this.gbBase.Location = new System.Drawing.Point(0, 0);
            this.gbBase.Name = "gbBase";
            this.gbBase.Size = new System.Drawing.Size(251, 415);
            this.gbBase.TabIndex = 0;
            this.gbBase.TabStop = false;
            this.gbBase.Text = "基本";
            // 
            // nuFlag
            // 
            this.nuFlag.Location = new System.Drawing.Point(95, 222);
            this.nuFlag.Maximum = new decimal(new int[] {
            99,
            0,
            0,
            0});
            this.nuFlag.Name = "nuFlag";
            this.nuFlag.Size = new System.Drawing.Size(135, 21);
            this.nuFlag.TabIndex = 6;
            // 
            // label3
            // 
            this.label3.AutoSize = true;
            this.label3.Location = new System.Drawing.Point(11, 225);
            this.label3.Name = "label3";
            this.label3.Size = new System.Drawing.Size(77, 12);
            this.label3.TabIndex = 17;
            this.label3.Text = "数据Flag标志";
            // 
            // btnStateConfig
            // 
            this.btnStateConfig.Location = new System.Drawing.Point(95, 303);
            this.btnStateConfig.Name = "btnStateConfig";
            this.btnStateConfig.Size = new System.Drawing.Size(135, 23);
            this.btnStateConfig.TabIndex = 8;
            this.btnStateConfig.Text = "开关量因子配置";
            this.btnStateConfig.UseVisualStyleBackColor = true;
            this.btnStateConfig.Click += new System.EventHandler(this.btnStateConfig_Click);
            // 
            // btnDeviceLog
            // 
            this.btnDeviceLog.Location = new System.Drawing.Point(95, 375);
            this.btnDeviceLog.Name = "btnDeviceLog";
            this.btnDeviceLog.Size = new System.Drawing.Size(135, 23);
            this.btnDeviceLog.TabIndex = 10;
            this.btnDeviceLog.Text = "设备日志配置";
            this.btnDeviceLog.UseVisualStyleBackColor = true;
            this.btnDeviceLog.Click += new System.EventHandler(this.btnDeviceLog_Click);
            // 
            // btnSystemParamConfig
            // 
            this.btnSystemParamConfig.Location = new System.Drawing.Point(95, 339);
            this.btnSystemParamConfig.Name = "btnSystemParamConfig";
            this.btnSystemParamConfig.Size = new System.Drawing.Size(135, 23);
            this.btnSystemParamConfig.TabIndex = 9;
            this.btnSystemParamConfig.Text = "系统参数配置";
            this.btnSystemParamConfig.UseVisualStyleBackColor = true;
            this.btnSystemParamConfig.Click += new System.EventHandler(this.btnSystemParamConfig_Click);
            // 
            // label11
            // 
            this.label11.AutoSize = true;
            this.label11.Location = new System.Drawing.Point(11, 188);
            this.label11.Name = "label11";
            this.label11.Size = new System.Drawing.Size(77, 12);
            this.label11.TabIndex = 16;
            this.label11.Text = "指令间隔(ms)";
            // 
            // nuCmdInterval
            // 
            this.nuCmdInterval.Location = new System.Drawing.Point(95, 186);
            this.nuCmdInterval.Maximum = new decimal(new int[] {
            20000,
            0,
            0,
            0});
            this.nuCmdInterval.Minimum = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.nuCmdInterval.Name = "nuCmdInterval";
            this.nuCmdInterval.Size = new System.Drawing.Size(135, 21);
            this.nuCmdInterval.TabIndex = 5;
            this.nuCmdInterval.Value = new decimal(new int[] {
            100,
            0,
            0,
            0});
            // 
            // btnPolNodeConfig
            // 
            this.btnPolNodeConfig.Location = new System.Drawing.Point(95, 267);
            this.btnPolNodeConfig.Name = "btnPolNodeConfig";
            this.btnPolNodeConfig.Size = new System.Drawing.Size(135, 23);
            this.btnPolNodeConfig.TabIndex = 7;
            this.btnPolNodeConfig.Text = "输出因子配置";
            this.btnPolNodeConfig.UseVisualStyleBackColor = true;
            this.btnPolNodeConfig.Click += new System.EventHandler(this.btnPolNodeConfig_Click);
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Location = new System.Drawing.Point(11, 57);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(53, 12);
            this.label1.TabIndex = 12;
            this.label1.Text = "系统编码";
            // 
            // txtMN
            // 
            this.txtMN.Location = new System.Drawing.Point(95, 23);
            this.txtMN.Name = "txtMN";
            this.txtMN.Size = new System.Drawing.Size(135, 21);
            this.txtMN.TabIndex = 0;
            // 
            // label10
            // 
            this.label10.AutoSize = true;
            this.label10.Location = new System.Drawing.Point(11, 26);
            this.label10.Name = "label10";
            this.label10.Size = new System.Drawing.Size(77, 12);
            this.label10.TabIndex = 11;
            this.label10.Text = "唯一标识MN号";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Location = new System.Drawing.Point(11, 88);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(53, 12);
            this.label2.TabIndex = 13;
            this.label2.Text = "访问密码";
            // 
            // cmbST
            // 
            this.cmbST.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbST.FormattingEnabled = true;
            this.cmbST.Items.AddRange(new object[] {
            "20",
            "21",
            "22",
            "23",
            "31",
            "32",
            "33",
            "34",
            "35",
            "36",
            "37",
            "38",
            "41",
            "91"});
            this.cmbST.Location = new System.Drawing.Point(95, 54);
            this.cmbST.Name = "cmbST";
            this.cmbST.Size = new System.Drawing.Size(135, 20);
            this.cmbST.TabIndex = 1;
            // 
            // nuReCount
            // 
            this.nuReCount.Location = new System.Drawing.Point(95, 119);
            this.nuReCount.Maximum = new decimal(new int[] {
            99,
            0,
            0,
            0});
            this.nuReCount.Name = "nuReCount";
            this.nuReCount.Size = new System.Drawing.Size(135, 21);
            this.nuReCount.TabIndex = 3;
            // 
            // label9
            // 
            this.label9.AutoSize = true;
            this.label9.Location = new System.Drawing.Point(11, 154);
            this.label9.Name = "label9";
            this.label9.Size = new System.Drawing.Size(53, 12);
            this.label9.TabIndex = 15;
            this.label9.Text = "超时时间";
            // 
            // nuOverTime
            // 
            this.nuOverTime.Location = new System.Drawing.Point(95, 152);
            this.nuOverTime.Maximum = new decimal(new int[] {
            99999,
            0,
            0,
            0});
            this.nuOverTime.Name = "nuOverTime";
            this.nuOverTime.Size = new System.Drawing.Size(135, 21);
            this.nuOverTime.TabIndex = 4;
            // 
            // label5
            // 
            this.label5.AutoSize = true;
            this.label5.Location = new System.Drawing.Point(11, 121);
            this.label5.Name = "label5";
            this.label5.Size = new System.Drawing.Size(53, 12);
            this.label5.TabIndex = 14;
            this.label5.Text = "重发次数";
            // 
            // txtPwd
            // 
            this.txtPwd.Location = new System.Drawing.Point(95, 85);
            this.txtPwd.Name = "txtPwd";
            this.txtPwd.PasswordChar = '*';
            this.txtPwd.Size = new System.Drawing.Size(135, 21);
            this.txtPwd.TabIndex = 2;
            // 
            // chkNeedQN
            // 
            this.chkNeedQN.AutoSize = true;
            this.chkNeedQN.Location = new System.Drawing.Point(16, 243);
            this.chkNeedQN.Name = "chkNeedQN";
            this.chkNeedQN.Size = new System.Drawing.Size(204, 16);
            this.chkNeedQN.TabIndex = 6;
            this.chkNeedQN.Text = "上报统计值指令需要请求编号(QN)";
            this.chkNeedQN.UseVisualStyleBackColor = true;
            // 
            // chkNeedFlag
            // 
            this.chkNeedFlag.AutoSize = true;
            this.chkNeedFlag.Location = new System.Drawing.Point(16, 214);
            this.chkNeedFlag.Name = "chkNeedFlag";
            this.chkNeedFlag.Size = new System.Drawing.Size(204, 16);
            this.chkNeedFlag.TabIndex = 5;
            this.chkNeedFlag.Text = "实时数据指令需要因子状态(Flag)";
            this.chkNeedFlag.UseVisualStyleBackColor = true;
            // 
            // gbReport
            // 
            this.gbReport.Controls.Add(this.chkDay);
            this.gbReport.Controls.Add(this.cmbMinuteSpan);
            this.gbReport.Controls.Add(this.label4);
            this.gbReport.Controls.Add(this.chkMinute);
            this.gbReport.Controls.Add(this.chkDataTimeNotCut);
            this.gbReport.Controls.Add(this.chkUpHeart);
            this.gbReport.Controls.Add(this.chkUploadStateNode);
            this.gbReport.Controls.Add(this.chkAutoReportLost);
            this.gbReport.Controls.Add(this.chkFromDb);
            this.gbReport.Controls.Add(this.chkNeedFlag);
            this.gbReport.Controls.Add(this.chkState);
            this.gbReport.Controls.Add(this.chkNeedQN);
            this.gbReport.Controls.Add(this.label7);
            this.gbReport.Controls.Add(this.label14);
            this.gbReport.Controls.Add(this.chkHour);
            this.gbReport.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gbReport.Location = new System.Drawing.Point(251, 0);
            this.gbReport.Name = "gbReport";
            this.gbReport.Size = new System.Drawing.Size(248, 415);
            this.gbReport.TabIndex = 1;
            this.gbReport.TabStop = false;
            this.gbReport.Text = "主动上报";
            // 
            // chkDataTimeNotCut
            // 
            this.chkDataTimeNotCut.AutoSize = true;
            this.chkDataTimeNotCut.Location = new System.Drawing.Point(16, 388);
            this.chkDataTimeNotCut.Name = "chkDataTimeNotCut";
            this.chkDataTimeNotCut.Size = new System.Drawing.Size(180, 16);
            this.chkDataTimeNotCut.TabIndex = 11;
            this.chkDataTimeNotCut.Text = "小时数据时间不截取为整小时";
            this.toolTip1.SetToolTip(this.chkDataTimeNotCut, "勾选后，小时数据按数据库保存时间戳上传，不再截取为整小时数。");
            this.chkDataTimeNotCut.UseVisualStyleBackColor = true;
            // 
            // chkUpHeart
            // 
            this.chkUpHeart.AutoSize = true;
            this.chkUpHeart.Location = new System.Drawing.Point(16, 330);
            this.chkUpHeart.Name = "chkUpHeart";
            this.chkUpHeart.Size = new System.Drawing.Size(186, 16);
            this.chkUpHeart.TabIndex = 9;
            this.chkUpHeart.Text = "是否只上传心跳/不传实时数据";
            this.chkUpHeart.UseVisualStyleBackColor = true;
            // 
            // chkUploadStateNode
            // 
            this.chkUploadStateNode.AutoSize = true;
            this.chkUploadStateNode.Location = new System.Drawing.Point(16, 272);
            this.chkUploadStateNode.Name = "chkUploadStateNode";
            this.chkUploadStateNode.Size = new System.Drawing.Size(156, 16);
            this.chkUploadStateNode.TabIndex = 7;
            this.chkUploadStateNode.Text = "上报开关量因子实时数据";
            this.chkUploadStateNode.UseVisualStyleBackColor = true;
            // 
            // chkAutoReportLost
            // 
            this.chkAutoReportLost.AutoSize = true;
            this.chkAutoReportLost.Location = new System.Drawing.Point(16, 359);
            this.chkAutoReportLost.Name = "chkAutoReportLost";
            this.chkAutoReportLost.Size = new System.Drawing.Size(96, 16);
            this.chkAutoReportLost.TabIndex = 10;
            this.chkAutoReportLost.Text = "自动数据补缺";
            this.chkAutoReportLost.UseVisualStyleBackColor = true;
            // 
            // chkFromDb
            // 
            this.chkFromDb.AutoSize = true;
            this.chkFromDb.Location = new System.Drawing.Point(16, 301);
            this.chkFromDb.Name = "chkFromDb";
            this.chkFromDb.Size = new System.Drawing.Size(144, 16);
            this.chkFromDb.TabIndex = 8;
            this.chkFromDb.Text = "实时数据从数据库获取";
            this.chkFromDb.UseVisualStyleBackColor = true;
            // 
            // chkState
            // 
            this.chkState.AutoSize = true;
            this.chkState.Location = new System.Drawing.Point(16, 160);
            this.chkState.Name = "chkState";
            this.chkState.Size = new System.Drawing.Size(198, 16);
            this.chkState.TabIndex = 4;
            this.chkState.Text = "上报系统/仪器状态参数日志信息";
            this.chkState.UseVisualStyleBackColor = true;
            // 
            // label7
            // 
            this.label7.AutoSize = true;
            this.label7.Location = new System.Drawing.Point(96, 106);
            this.label7.Name = "label7";
            this.label7.Size = new System.Drawing.Size(113, 12);
            this.label7.TabIndex = 13;
            this.label7.Text = "整点上报小时统计值";
            // 
            // label14
            // 
            this.label14.AutoSize = true;
            this.label14.Location = new System.Drawing.Point(96, 189);
            this.label14.Name = "label14";
            this.label14.Size = new System.Drawing.Size(137, 12);
            this.label14.TabIndex = 14;
            this.label14.Text = "上报频率与实时数据相同";
            // 
            // chkHour
            // 
            this.chkHour.AutoSize = true;
            this.chkHour.Location = new System.Drawing.Point(16, 77);
            this.chkHour.Name = "chkHour";
            this.chkHour.Size = new System.Drawing.Size(108, 16);
            this.chkHour.TabIndex = 2;
            this.chkHour.Text = "上报小时统计值";
            this.chkHour.UseVisualStyleBackColor = true;
            // 
            // chkDay
            // 
            this.chkDay.AutoSize = true;
            this.chkDay.Location = new System.Drawing.Point(16, 131);
            this.chkDay.Name = "chkDay";
            this.chkDay.Size = new System.Drawing.Size(96, 16);
            this.chkDay.TabIndex = 3;
            this.chkDay.Text = "上报日统计值";
            this.chkDay.UseVisualStyleBackColor = true;
            // 
            // cmbMinuteSpan
            // 
            this.cmbMinuteSpan.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.cmbMinuteSpan.FormattingEnabled = true;
            this.cmbMinuteSpan.Items.AddRange(new object[] {
            "1",
            "2",
            "5",
            "10",
            "15",
            "20",
            "30"});
            this.cmbMinuteSpan.Location = new System.Drawing.Point(147, 48);
            this.cmbMinuteSpan.Name = "cmbMinuteSpan";
            this.cmbMinuteSpan.Size = new System.Drawing.Size(73, 20);
            this.cmbMinuteSpan.TabIndex = 1;
            // 
            // label4
            // 
            this.label4.AutoSize = true;
            this.label4.Location = new System.Drawing.Point(88, 52);
            this.label4.Name = "label4";
            this.label4.Size = new System.Drawing.Size(53, 12);
            this.label4.TabIndex = 12;
            this.label4.Text = "上报间隔";
            // 
            // chkMinute
            // 
            this.chkMinute.AutoSize = true;
            this.chkMinute.Location = new System.Drawing.Point(16, 23);
            this.chkMinute.Name = "chkMinute";
            this.chkMinute.Size = new System.Drawing.Size(108, 16);
            this.chkMinute.TabIndex = 0;
            this.chkMinute.Text = "上报分钟统计值";
            this.chkMinute.UseVisualStyleBackColor = true;
            // 
            // FPISZHWRYConfigUC
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(499, 415);
            this.Controls.Add(this.gbReport);
            this.Controls.Add(this.gbBase);
            this.Name = "FPISZHWRYConfigUC";
            this.gbBase.ResumeLayout(false);
            this.gbBase.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.nuFlag)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nuCmdInterval)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nuReCount)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.nuOverTime)).EndInit();
            this.gbReport.ResumeLayout(false);
            this.gbReport.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.GroupBox gbBase;
        private System.Windows.Forms.Label label1;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.ComboBox cmbST;
        private System.Windows.Forms.NumericUpDown nuReCount;
        private System.Windows.Forms.Label label9;
        private System.Windows.Forms.NumericUpDown nuOverTime;
        private System.Windows.Forms.Label label5;
        private System.Windows.Forms.TextBox txtPwd;
        private System.Windows.Forms.CheckBox chkNeedQN;
        private System.Windows.Forms.CheckBox chkNeedFlag;
        private System.Windows.Forms.GroupBox gbReport;
        private System.Windows.Forms.Label label7;
        private System.Windows.Forms.CheckBox chkHour;
        private System.Windows.Forms.TextBox txtMN;
        private System.Windows.Forms.Label label10;
        private System.Windows.Forms.Button btnPolNodeConfig;
        private System.Windows.Forms.CheckBox chkState;
        private System.Windows.Forms.CheckBox chkFromDb;
        private System.Windows.Forms.Label label14;
        private System.Windows.Forms.Label label11;
        private System.Windows.Forms.NumericUpDown nuCmdInterval;
        private System.Windows.Forms.CheckBox chkAutoReportLost;
        private System.Windows.Forms.Button btnSystemParamConfig;
        private System.Windows.Forms.Button btnDeviceLog;
        private System.Windows.Forms.Button btnStateConfig;
        private System.Windows.Forms.CheckBox chkUploadStateNode;
        private System.Windows.Forms.Label label3;
        private System.Windows.Forms.NumericUpDown nuFlag;
        private System.Windows.Forms.CheckBox chkUpHeart;
        private System.Windows.Forms.CheckBox chkDataTimeNotCut;
        private System.Windows.Forms.ToolTip toolTip1;
        private System.Windows.Forms.CheckBox chkDay;
        private System.Windows.Forms.ComboBox cmbMinuteSpan;
        private System.Windows.Forms.Label label4;
        private System.Windows.Forms.CheckBox chkMinute;
    }
}
