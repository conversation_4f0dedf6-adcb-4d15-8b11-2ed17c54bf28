﻿using System;
using System.Drawing;
using System.Windows.Forms;
using Fpi.UI.Common.PC;
using Fpi.WMS3000.Voice.Config;
using Fpi.WMS3000.Voice.Interface;
using Sunny.UI;

namespace Fpi.WMS3000.Voice.UI
{
    public partial class FrmVoiceTemplateParaEdit : UIForm
    {
        #region 字段属性

        private IVoiceConfig view = null;

        private VoiceControlCmd _voice;

        public VoiceControlCmd Voice => _voice;

        #endregion

        #region 构造

        public FrmVoiceTemplateParaEdit(IVoiceConfig view)
        {
            InitializeComponent();
            this.view = view;
        }

        #endregion

        #region 事件

        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                this.view.Check();
                _voice = this.view.Save();

                if(FpiMessageBox.ShowQuestion("退出并保存 ？") == DialogResult.No)
                {
                    this.DialogResult = DialogResult.None;
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
                this.DialogResult = DialogResult.None;
            }
        }

        private void FrmVoiceTemplateParaEdit_Load(object sender, EventArgs e)
        {
            InitUI();
        }

        #endregion

        #region 私有方法

        private void InitUI()
        {
            Control ctr = this.view as Control;
            this.pnlMain.Controls.Add(ctr);
            this.Width = ctr.Width + 20;
            this.Height = ctr.Height + this.pnlFunc.Height + 30;
            ctr.Dock = DockStyle.Fill;
            this.Location = new Point((Screen.PrimaryScreen.Bounds.Width - this.Width) / 2,
               (Screen.PrimaryScreen.Bounds.Height - this.Height) / 2);
        }

        #endregion
    }
}