﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Forms;
using Fpi.UI.Common.PC;
using Fpi.WMS3000.Inspection;
using Fpi.WMS3000.SystemConfig.SmartPatrol.Config;
using Fpi.WMS3000.SystemConfig.SmartPatrol.Helper;
using Sunny.UI;

namespace Fpi.WMS3000.SystemConfig
{
    public partial class FrmPatrolResultShow : UIForm
    {
        #region 字段属性

        /// <summary>
        /// WPF报表显示控件
        /// </summary>
        private ReportUserControl _reportUserControl = new ReportUserControl();

        /// <summary>
        /// 对应智能巡检结果
        /// </summary>
        private SmartPatrolResult _smartPatrolResult;

        /// <summary>
        /// 报表控件对应数据结构
        /// </summary>
        private InspectionHierarchy _inspectionHierarchyData;

        #endregion

        #region 构造

        public FrmPatrolResultShow()
        {
            InitializeComponent();
            elementHost.Child = _reportUserControl;
        }

        public FrmPatrolResultShow(SmartPatrolResult smartPatrolResult) : this()
        {
            _smartPatrolResult = smartPatrolResult;
        }

        #endregion

        #region 事件

        private void FrmPatrolResultShow_Load(object sender, EventArgs e)
        {
            try
            {
                if(_smartPatrolResult != null)
                {
                    _inspectionHierarchyData = InspectionStructureBuilder.BuildPatrolReportData(_smartPatrolResult);

                    lblStartTime.Text = _smartPatrolResult.StartTime.ToString("yyyy-MM-dd HH:mm:ss");
                    lblTriggerType.Text = _smartPatrolResult.PatrolTriggerType.ToString();

                    // 加载表格列头和内容数据
                    var columns = LoadColumns();
                    var checkUnits = LoadCheckUnits();

                    // 渲染表格
                    _reportUserControl.Render(columns, checkUnits);
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError($"加载出错：{ex.Message}");
            }
        }

        /// <summary>
        /// 异常数据展示
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnErrorShow_Click(object sender, EventArgs e)
        {
            new FrmPatrolErrorResultShow(_smartPatrolResult).ShowDialog();
        }

        /// <summary>
        /// PDF格式报告导出
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnExport_Click(object sender, EventArgs e)
        {
            try
            {
                var saveFileDialog = new SaveFileDialog();

                // 导出时弹窗提示选取导出目录，及文件名称
                string filePath = System.Windows.Forms.Application.StartupPath + "\\巡检报告\\";

                if(!Directory.Exists(filePath))
                {
                    Directory.CreateDirectory(filePath);
                }

                // 第一次调用设置初始文件
                if(string.IsNullOrEmpty(saveFileDialog.FileName))
                {
                    saveFileDialog.InitialDirectory = filePath;
                }

                saveFileDialog.FileName = $"巡检报告{_smartPatrolResult.StartTime:yyyy-MM-dd HH-mm-ss}.pdf";

                if(saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    PatroReportPdfExport.GenerateInspectionPdf(saveFileDialog.FileName, _inspectionHierarchyData);

                    if(FpiMessageBox.ShowQuestion("导出成功！是否定位到文件所在位置？") == DialogResult.Yes)
                    {
                        var psi = new ProcessStartInfo("Explorer.exe")
                        {
                            Arguments = "/e,/select," + saveFileDialog.FileName
                        };
                        // 打开导出文件所在位置
                        Process.Start(psi);
                    }
                }
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError($"数据生成出错：{ex.Message}");
            }
        }

        #endregion

        #region 私有方法

        #region 加载数据    

        /// <summary>
        /// 初始化
        /// </summary>
        /// <returns></returns>
        private List<Column> LoadColumns()
        {
            return new List<Column>()
            {
                new Column(){ Name = "序号", Width = new GridLength(50) },
                new Column(){ Name = "巡查单元", Width = new GridLength(180) },
                new Column(){ Name = "子项", Width = new GridLength(180) },
                new Column(){ Name = "状态", Width = new GridLength(80) },
                new Column(){ Name = "巡查项", Width = new GridLength(260) },
                new Column(){ Name = "巡查结果", Width = new GridLength(1,GridUnitType.Star) }
            };
        }

        /// <summary>
        /// 一级节点
        /// </summary>
        /// <returns></returns>
        private List<CheckUnit> LoadCheckUnits()
        {
            var checkUnits = new List<CheckUnit>();

            int i = 1;
            foreach(var item in _inspectionHierarchyData.FirstLayer)
            {
                var checkUnit = new CheckUnit();

                checkUnit.SeqNo = (i++).ToString();
                checkUnit.Name = item.UnitName;
                checkUnit.CheckSubUnits = LoadCheckSubUnits(item.SecondLayer);
                checkUnit.CheckItemsCount = checkUnit.CheckSubUnits.Select(p => p.CheckItemsCount).Aggregate((c1, c2) => c1 + c2);
                checkUnits.Add(checkUnit);
            }

            return checkUnits;
        }

        /// <summary>
        /// 二级节点
        /// </summary>
        /// <param name="node"></param>
        /// <returns></returns>
        private List<CheckSubUnit> LoadCheckSubUnits(List<TwoPropertyNode> node)
        {
            var checkSubUnits = new List<CheckSubUnit>();

            foreach(var item in node)
            {
                var checkSubUnit = new CheckSubUnit
                {
                    Name = $"{item.PropertyName}",
                    State = $"{item.StatusDescription}",
                    CheckItems = LoadCheckItems(item.LeafProperties)
                };
                checkSubUnit.CheckItemsCount = checkSubUnit.CheckItems.Count;
                checkSubUnits.Add(checkSubUnit);
            }

            return checkSubUnits;
        }

        /// <summary>
        /// 三级节点
        /// </summary>
        /// <param name="node"></param>
        /// <returns></returns>
        private List<CheckItem> LoadCheckItems(List<ThreeLeafProperty> node)
        {
            var checkItems = new List<CheckItem>();

            if(node.Count == 0)
            {
                checkItems.Add(new CheckItem());
            }
            else
            {
                foreach(var item in node)
                {
                    var checkItem = new CheckItem();

                    checkItem.Name = $"{item.Name}";
                    checkItem.Result = $"{item.Value}";

                    checkItems.Add(checkItem);
                }
            }
            return checkItems;
        }

        #endregion

        #endregion
    }
}