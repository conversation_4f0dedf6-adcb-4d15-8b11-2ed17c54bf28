﻿namespace Fpi.WMS3000.Equipment.UI
{
    partial class UC_WCS3900Param
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if(disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.tabMain = new Sunny.UI.UITabControl();
            this.tbpState = new System.Windows.Forms.TabPage();
            this.uc_DeviceAllAlarm = new Fpi.WMS3000.Equipment.Common.UI.UC_DeviceAllAlarm();
            this.uc_WCS3900DeviceState = new Fpi.WMS3000.Equipment.UI.UC_WCS3900DeviceState();
            this.tabInfo = new System.Windows.Forms.TabPage();
            this.uiTabControl1 = new Sunny.UI.UITabControl();
            this.tabPage1 = new System.Windows.Forms.TabPage();
            this.uC_PHParam = new Fpi.WMS3000.Equipment.UI.UC_OneWCS3900NodeParam();
            this.tabPage2 = new System.Windows.Forms.TabPage();
            this.uC_ElectricParam = new Fpi.WMS3000.Equipment.UI.UC_OneWCS3900NodeParam();
            this.tabPage3 = new System.Windows.Forms.TabPage();
            this.uC_OxygenParam = new Fpi.WMS3000.Equipment.UI.UC_OneWCS3900NodeParam();
            this.tabPage4 = new System.Windows.Forms.TabPage();
            this.uC_TurbidityParam = new Fpi.WMS3000.Equipment.UI.UC_OneWCS3900NodeParam();
            this.tbpMaintain = new System.Windows.Forms.TabPage();
            this.uc_WCS3900Maintain = new Fpi.WMS3000.Equipment.UI.UC_WCS3900Maintain();
            this.tabMeasureData = new System.Windows.Forms.TabPage();
            this.uc_WCS3900MeasureData = new Fpi.WMS3000.Equipment.UI.UC_WCS3900AllMeasureData();
            this.tbpRunLog = new System.Windows.Forms.TabPage();
            this.uc_WCS3900CurrentLog = new Fpi.WMS3000.Equipment.UI.UC_DeviceCurrentLogQuery();
            this.tabLogQuery = new System.Windows.Forms.TabPage();
            this.uc_WCS3900HistoryLog = new Fpi.WMS3000.Equipment.UI.UC_DeviceHistoryLogQuery();
            this.tabMain.SuspendLayout();
            this.tbpState.SuspendLayout();
            this.tabInfo.SuspendLayout();
            this.uiTabControl1.SuspendLayout();
            this.tabPage1.SuspendLayout();
            this.tabPage2.SuspendLayout();
            this.tabPage3.SuspendLayout();
            this.tabPage4.SuspendLayout();
            this.tbpMaintain.SuspendLayout();
            this.tabMeasureData.SuspendLayout();
            this.tbpRunLog.SuspendLayout();
            this.tabLogQuery.SuspendLayout();
            this.SuspendLayout();
            // 
            // tabMain
            // 
            this.tabMain.Controls.Add(this.tbpState);
            this.tabMain.Controls.Add(this.tabInfo);
            this.tabMain.Controls.Add(this.tbpMaintain);
            this.tabMain.Controls.Add(this.tabMeasureData);
            this.tabMain.Controls.Add(this.tbpRunLog);
            this.tabMain.Controls.Add(this.tabLogQuery);
            this.tabMain.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabMain.DrawMode = System.Windows.Forms.TabDrawMode.OwnerDrawFixed;
            this.tabMain.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.tabMain.ItemSize = new System.Drawing.Size(150, 40);
            this.tabMain.Location = new System.Drawing.Point(0, 0);
            this.tabMain.MainPage = "";
            this.tabMain.MenuStyle = Sunny.UI.UIMenuStyle.White;
            this.tabMain.Name = "tabMain";
            this.tabMain.RightToLeft = System.Windows.Forms.RightToLeft.No;
            this.tabMain.SelectedIndex = 0;
            this.tabMain.Size = new System.Drawing.Size(1616, 936);
            this.tabMain.SizeMode = System.Windows.Forms.TabSizeMode.Fixed;
            this.tabMain.TabBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(240)))), ((int)(((byte)(240)))));
            this.tabMain.TabIndex = 0;
            this.tabMain.TabPageTextAlignment = System.Windows.Forms.HorizontalAlignment.Center;
            this.tabMain.TabSelectedColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(250)))), ((int)(((byte)(250)))));
            this.tabMain.TabSelectedHighColorSize = 0;
            this.tabMain.TabUnSelectedColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(240)))), ((int)(((byte)(240)))));
            this.tabMain.TabUnSelectedForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.tabMain.TipsFont = new System.Drawing.Font("微软雅黑", 12F);
            // 
            // tbpState
            // 
            this.tbpState.AutoScroll = true;
            this.tbpState.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(243)))), ((int)(((byte)(249)))), ((int)(((byte)(255)))));
            this.tbpState.Controls.Add(this.uc_DeviceAllAlarm);
            this.tbpState.Controls.Add(this.uc_WCS3900DeviceState);
            this.tbpState.Location = new System.Drawing.Point(0, 40);
            this.tbpState.Name = "tbpState";
            this.tbpState.Size = new System.Drawing.Size(1616, 896);
            this.tbpState.TabIndex = 0;
            this.tbpState.Text = "系统状态";
            // 
            // uc_DeviceAllAlarm
            // 
            this.uc_DeviceAllAlarm.CanRefresh = true;
            this.uc_DeviceAllAlarm.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uc_DeviceAllAlarm.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uc_DeviceAllAlarm.Location = new System.Drawing.Point(335, 0);
            this.uc_DeviceAllAlarm.MinimumSize = new System.Drawing.Size(1, 1);
            this.uc_DeviceAllAlarm.Name = "uc_DeviceAllAlarm";
            this.uc_DeviceAllAlarm.Padding = new System.Windows.Forms.Padding(1);
            this.uc_DeviceAllAlarm.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.uc_DeviceAllAlarm.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.uc_DeviceAllAlarm.Size = new System.Drawing.Size(1281, 896);
            this.uc_DeviceAllAlarm.TabIndex = 1;
            this.uc_DeviceAllAlarm.Text = "uc_DeviceAllAlarm";
            this.uc_DeviceAllAlarm.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // uc_WCS3900DeviceState
            // 
            this.uc_WCS3900DeviceState.Dock = System.Windows.Forms.DockStyle.Left;
            this.uc_WCS3900DeviceState.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uc_WCS3900DeviceState.Location = new System.Drawing.Point(0, 0);
            this.uc_WCS3900DeviceState.MinimumSize = new System.Drawing.Size(1, 1);
            this.uc_WCS3900DeviceState.Name = "uc_WCS3900DeviceState";
            this.uc_WCS3900DeviceState.Padding = new System.Windows.Forms.Padding(1);
            this.uc_WCS3900DeviceState.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.uc_WCS3900DeviceState.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.uc_WCS3900DeviceState.Size = new System.Drawing.Size(335, 896);
            this.uc_WCS3900DeviceState.TabIndex = 0;
            this.uc_WCS3900DeviceState.Text = null;
            this.uc_WCS3900DeviceState.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // tabInfo
            // 
            this.tabInfo.Controls.Add(this.uiTabControl1);
            this.tabInfo.Location = new System.Drawing.Point(0, 40);
            this.tabInfo.Name = "tabInfo";
            this.tabInfo.Size = new System.Drawing.Size(1616, 896);
            this.tabInfo.TabIndex = 5;
            this.tabInfo.Text = "信息统计";
            this.tabInfo.UseVisualStyleBackColor = true;
            // 
            // uiTabControl1
            // 
            this.uiTabControl1.Controls.Add(this.tabPage1);
            this.uiTabControl1.Controls.Add(this.tabPage2);
            this.uiTabControl1.Controls.Add(this.tabPage3);
            this.uiTabControl1.Controls.Add(this.tabPage4);
            this.uiTabControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uiTabControl1.DrawMode = System.Windows.Forms.TabDrawMode.OwnerDrawFixed;
            this.uiTabControl1.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiTabControl1.ItemSize = new System.Drawing.Size(150, 40);
            this.uiTabControl1.Location = new System.Drawing.Point(0, 0);
            this.uiTabControl1.MainPage = "";
            this.uiTabControl1.Name = "uiTabControl1";
            this.uiTabControl1.SelectedIndex = 0;
            this.uiTabControl1.Size = new System.Drawing.Size(1616, 896);
            this.uiTabControl1.SizeMode = System.Windows.Forms.TabSizeMode.Fixed;
            this.uiTabControl1.TabIndex = 1;
            this.uiTabControl1.TabPageTextAlignment = System.Windows.Forms.HorizontalAlignment.Center;
            this.uiTabControl1.TipsFont = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            // 
            // tabPage1
            // 
            this.tabPage1.Controls.Add(this.uC_PHParam);
            this.tabPage1.Location = new System.Drawing.Point(0, 40);
            this.tabPage1.Name = "tabPage1";
            this.tabPage1.Size = new System.Drawing.Size(1616, 856);
            this.tabPage1.TabIndex = 0;
            this.tabPage1.Text = "PH";
            this.tabPage1.UseVisualStyleBackColor = true;
            // 
            // uC_PHParam
            // 
            this.uC_PHParam.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uC_PHParam.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uC_PHParam.Location = new System.Drawing.Point(0, 0);
            this.uC_PHParam.MinimumSize = new System.Drawing.Size(1, 1);
            this.uC_PHParam.Name = "uC_PHParam";
            this.uC_PHParam.Padding = new System.Windows.Forms.Padding(3);
            this.uC_PHParam.Size = new System.Drawing.Size(1616, 856);
            this.uC_PHParam.TabIndex = 0;
            this.uC_PHParam.Text = null;
            this.uC_PHParam.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // tabPage2
            // 
            this.tabPage2.Controls.Add(this.uC_ElectricParam);
            this.tabPage2.Location = new System.Drawing.Point(0, 40);
            this.tabPage2.Name = "tabPage2";
            this.tabPage2.Size = new System.Drawing.Size(200, 60);
            this.tabPage2.TabIndex = 1;
            this.tabPage2.Text = "电导率";
            this.tabPage2.UseVisualStyleBackColor = true;
            // 
            // uC_ElectricParam
            // 
            this.uC_ElectricParam.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uC_ElectricParam.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uC_ElectricParam.Location = new System.Drawing.Point(0, 0);
            this.uC_ElectricParam.MinimumSize = new System.Drawing.Size(1, 1);
            this.uC_ElectricParam.Name = "uC_ElectricParam";
            this.uC_ElectricParam.Padding = new System.Windows.Forms.Padding(3);
            this.uC_ElectricParam.Size = new System.Drawing.Size(200, 60);
            this.uC_ElectricParam.TabIndex = 1;
            this.uC_ElectricParam.Text = null;
            this.uC_ElectricParam.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // tabPage3
            // 
            this.tabPage3.Controls.Add(this.uC_OxygenParam);
            this.tabPage3.Location = new System.Drawing.Point(0, 40);
            this.tabPage3.Name = "tabPage3";
            this.tabPage3.Size = new System.Drawing.Size(200, 60);
            this.tabPage3.TabIndex = 2;
            this.tabPage3.Text = "溶解氧";
            this.tabPage3.UseVisualStyleBackColor = true;
            // 
            // uC_OxygenParam
            // 
            this.uC_OxygenParam.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uC_OxygenParam.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uC_OxygenParam.Location = new System.Drawing.Point(0, 0);
            this.uC_OxygenParam.MinimumSize = new System.Drawing.Size(1, 1);
            this.uC_OxygenParam.Name = "uC_OxygenParam";
            this.uC_OxygenParam.Padding = new System.Windows.Forms.Padding(3);
            this.uC_OxygenParam.Size = new System.Drawing.Size(200, 60);
            this.uC_OxygenParam.TabIndex = 1;
            this.uC_OxygenParam.Text = null;
            this.uC_OxygenParam.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // tabPage4
            // 
            this.tabPage4.Controls.Add(this.uC_TurbidityParam);
            this.tabPage4.Location = new System.Drawing.Point(0, 40);
            this.tabPage4.Name = "tabPage4";
            this.tabPage4.Size = new System.Drawing.Size(200, 60);
            this.tabPage4.TabIndex = 3;
            this.tabPage4.Text = "浊度";
            this.tabPage4.UseVisualStyleBackColor = true;
            // 
            // uC_TurbidityParam
            // 
            this.uC_TurbidityParam.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uC_TurbidityParam.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uC_TurbidityParam.Location = new System.Drawing.Point(0, 0);
            this.uC_TurbidityParam.MinimumSize = new System.Drawing.Size(1, 1);
            this.uC_TurbidityParam.Name = "uC_TurbidityParam";
            this.uC_TurbidityParam.Padding = new System.Windows.Forms.Padding(3);
            this.uC_TurbidityParam.Size = new System.Drawing.Size(200, 60);
            this.uC_TurbidityParam.TabIndex = 1;
            this.uC_TurbidityParam.Text = null;
            this.uC_TurbidityParam.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // tbpMaintain
            // 
            this.tbpMaintain.AutoScroll = true;
            this.tbpMaintain.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(243)))), ((int)(((byte)(249)))), ((int)(((byte)(255)))));
            this.tbpMaintain.Controls.Add(this.uc_WCS3900Maintain);
            this.tbpMaintain.Location = new System.Drawing.Point(0, 40);
            this.tbpMaintain.Name = "tbpMaintain";
            this.tbpMaintain.Size = new System.Drawing.Size(200, 60);
            this.tbpMaintain.TabIndex = 2;
            this.tbpMaintain.Text = "设备维护";
            // 
            // uc_WCS3900Maintain
            // 
            this.uc_WCS3900Maintain.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uc_WCS3900Maintain.Location = new System.Drawing.Point(0, 0);
            this.uc_WCS3900Maintain.MinimumSize = new System.Drawing.Size(1, 1);
            this.uc_WCS3900Maintain.Name = "uc_WCS3900Maintain";
            this.uc_WCS3900Maintain.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.uc_WCS3900Maintain.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.uc_WCS3900Maintain.Size = new System.Drawing.Size(1615, 896);
            this.uc_WCS3900Maintain.TabIndex = 0;
            this.uc_WCS3900Maintain.Text = null;
            this.uc_WCS3900Maintain.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // tabMeasureData
            // 
            this.tabMeasureData.AutoScroll = true;
            this.tabMeasureData.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(243)))), ((int)(((byte)(249)))), ((int)(((byte)(255)))));
            this.tabMeasureData.Controls.Add(this.uc_WCS3900MeasureData);
            this.tabMeasureData.Location = new System.Drawing.Point(0, 40);
            this.tabMeasureData.Name = "tabMeasureData";
            this.tabMeasureData.Size = new System.Drawing.Size(200, 60);
            this.tabMeasureData.TabIndex = 3;
            this.tabMeasureData.Text = "测量数据";
            // 
            // uc_WCS3900MeasureData
            // 
            this.uc_WCS3900MeasureData.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uc_WCS3900MeasureData.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uc_WCS3900MeasureData.Location = new System.Drawing.Point(0, 0);
            this.uc_WCS3900MeasureData.MinimumSize = new System.Drawing.Size(1, 1);
            this.uc_WCS3900MeasureData.Name = "uc_WCS3900MeasureData";
            this.uc_WCS3900MeasureData.Padding = new System.Windows.Forms.Padding(1);
            this.uc_WCS3900MeasureData.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.uc_WCS3900MeasureData.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.uc_WCS3900MeasureData.Size = new System.Drawing.Size(200, 60);
            this.uc_WCS3900MeasureData.TabIndex = 0;
            this.uc_WCS3900MeasureData.Text = null;
            this.uc_WCS3900MeasureData.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // tbpRunLog
            // 
            this.tbpRunLog.AutoScroll = true;
            this.tbpRunLog.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(243)))), ((int)(((byte)(249)))), ((int)(((byte)(255)))));
            this.tbpRunLog.Controls.Add(this.uc_WCS3900CurrentLog);
            this.tbpRunLog.Location = new System.Drawing.Point(0, 40);
            this.tbpRunLog.Name = "tbpRunLog";
            this.tbpRunLog.Size = new System.Drawing.Size(200, 60);
            this.tbpRunLog.TabIndex = 1;
            this.tbpRunLog.Text = "运行日志";
            // 
            // uc_WCS3900CurrentLog
            // 
            this.uc_WCS3900CurrentLog.CanRefresh = true;
            this.uc_WCS3900CurrentLog.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uc_WCS3900CurrentLog.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uc_WCS3900CurrentLog.Location = new System.Drawing.Point(0, 0);
            this.uc_WCS3900CurrentLog.MinimumSize = new System.Drawing.Size(1, 1);
            this.uc_WCS3900CurrentLog.Name = "uc_WCS3900CurrentLog";
            this.uc_WCS3900CurrentLog.Padding = new System.Windows.Forms.Padding(1);
            this.uc_WCS3900CurrentLog.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.uc_WCS3900CurrentLog.RectColor = System.Drawing.Color.Transparent;
            this.uc_WCS3900CurrentLog.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.uc_WCS3900CurrentLog.Size = new System.Drawing.Size(200, 60);
            this.uc_WCS3900CurrentLog.TabIndex = 1;
            this.uc_WCS3900CurrentLog.Text = "uC_DeviceLog1";
            this.uc_WCS3900CurrentLog.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // tabLogQuery
            // 
            this.tabLogQuery.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(243)))), ((int)(((byte)(249)))), ((int)(((byte)(255)))));
            this.tabLogQuery.Controls.Add(this.uc_WCS3900HistoryLog);
            this.tabLogQuery.Location = new System.Drawing.Point(0, 40);
            this.tabLogQuery.Name = "tabLogQuery";
            this.tabLogQuery.Size = new System.Drawing.Size(200, 60);
            this.tabLogQuery.TabIndex = 4;
            this.tabLogQuery.Text = "历史日志";
            // 
            // uc_WCS3900HistoryLog
            // 
            this.uc_WCS3900HistoryLog.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uc_WCS3900HistoryLog.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uc_WCS3900HistoryLog.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(15)))), ((int)(((byte)(68)))), ((int)(((byte)(134)))));
            this.uc_WCS3900HistoryLog.Location = new System.Drawing.Point(0, 0);
            this.uc_WCS3900HistoryLog.MinimumSize = new System.Drawing.Size(1, 1);
            this.uc_WCS3900HistoryLog.Name = "uc_WCS3900HistoryLog";
            this.uc_WCS3900HistoryLog.Padding = new System.Windows.Forms.Padding(1);
            this.uc_WCS3900HistoryLog.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.uc_WCS3900HistoryLog.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.uc_WCS3900HistoryLog.Size = new System.Drawing.Size(200, 60);
            this.uc_WCS3900HistoryLog.TabIndex = 0;
            this.uc_WCS3900HistoryLog.Text = "uC_QueryDeviceHistoryLog1";
            this.uc_WCS3900HistoryLog.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // UC_WCS3900Param
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(243)))), ((int)(((byte)(249)))), ((int)(((byte)(255)))));
            this.Controls.Add(this.tabMain);
            this.Name = "UC_WCS3900Param";
            this.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.Size = new System.Drawing.Size(1616, 936);
            this.Load += new System.EventHandler(this.UC_QCD3900Params_Load);
            this.tabMain.ResumeLayout(false);
            this.tbpState.ResumeLayout(false);
            this.tabInfo.ResumeLayout(false);
            this.uiTabControl1.ResumeLayout(false);
            this.tabPage1.ResumeLayout(false);
            this.tabPage2.ResumeLayout(false);
            this.tabPage3.ResumeLayout(false);
            this.tabPage4.ResumeLayout(false);
            this.tbpMaintain.ResumeLayout(false);
            this.tabMeasureData.ResumeLayout(false);
            this.tbpRunLog.ResumeLayout(false);
            this.tabLogQuery.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private Sunny.UI.UITabControl tabMain;
        private System.Windows.Forms.TabPage tbpState;
        private System.Windows.Forms.TabPage tbpRunLog;
        private System.Windows.Forms.TabPage tbpMaintain;
        private Common.UI.UC_DeviceAllAlarm uc_DeviceAllAlarm;
        private UC_WCS3900DeviceState uc_WCS3900DeviceState;
        private System.Windows.Forms.TabPage tabMeasureData;
        private UC_WCS3900Maintain uc_WCS3900Maintain;
        private System.Windows.Forms.TabPage tabLogQuery;
        private UC_DeviceCurrentLogQuery uc_WCS3900CurrentLog;
        private UC_WCS3900AllMeasureData uc_WCS3900MeasureData;
        private UC_DeviceHistoryLogQuery uc_WCS3900HistoryLog;
        private System.Windows.Forms.TabPage tabInfo;
        private Sunny.UI.UITabControl uiTabControl1;
        private System.Windows.Forms.TabPage tabPage1;
        private UI.UC_OneWCS3900NodeParam uC_PHParam;
        private System.Windows.Forms.TabPage tabPage2;
        private UI.UC_OneWCS3900NodeParam uC_ElectricParam;
        private System.Windows.Forms.TabPage tabPage3;
        private UI.UC_OneWCS3900NodeParam uC_OxygenParam;
        private System.Windows.Forms.TabPage tabPage4;
        private UI.UC_OneWCS3900NodeParam uC_TurbidityParam;
    }
}
