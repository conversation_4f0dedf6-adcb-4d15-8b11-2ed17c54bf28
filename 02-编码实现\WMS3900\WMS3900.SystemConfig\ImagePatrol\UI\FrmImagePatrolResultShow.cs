﻿using Fpi.WMS3000.SystemConfig.ImagePatrol.Config;
using Sunny.UI;

namespace Fpi.WMS3000.SystemConfig
{
    /// <summary>
    /// 单条图像巡检结果查看界面
    /// </summary>
    public partial class FrmImagePatrolResultShow : UIForm
    {
        #region 构造

        public FrmImagePatrolResultShow()
        {
            InitializeComponent();
        }

        public FrmImagePatrolResultShow(OldImagePatrolResult smartPatrolResult) : this()
        {
            uc_ImagePatrolResult.SetTragetPatrolResult(smartPatrolResult);
        }

        #endregion
    }
}