﻿using System;
using System.Windows.Forms;
using Fpi.UI.Common.PC;
using Fpi.WMS3000.Voice.Config;
using Fpi.WMS3000.Voice.Interface;
using Fpi.WMS3000.Voice.UI.UC;
using Sunny.UI;

namespace Fpi.WMS3000.Voice.UI
{
    public partial class FrmEditVoiceCmd : UIForm
    {
        #region 属性字段

        private readonly VoiceControlCmdManager _manager;

        private VoiceControlCmd _voiceCmd;

        private IVoiceConfigView _view;

        /// <summary>
        /// 修改模式
        /// </summary>
        private readonly eEditType _editType;

        public VoiceControlCmd VoiceOp => _voiceCmd;

        #endregion

        #region 构造

        public FrmEditVoiceCmd()
        {
            InitializeComponent();
        }

        public FrmEditVoiceCmd(VoiceControlCmdManager manager, VoiceControlCmd voiceCmd, eEditType editType) : this()
        {
            _manager = manager;
            this._voiceCmd = voiceCmd;
            _editType = editType;
            if(voiceCmd is not null)
            {
                _view = new UC_UserVoiceControl(_manager, voiceCmd, _editType);
                if(_view != null)
                {
                    var ctr = (Control)_view;
                    this.Width = ctr.Width;
                    this.Height = ctr.Height + this.pnlFunc.Height;
                    ctr.Parent = this.pnlMain;
                    ctr.Dock = DockStyle.Fill;
                }
            }
        }

        #endregion

        #region 事件

        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if(_view != null)
                {
                    this._voiceCmd = _view.Save();

                    if(_editType is eEditType.新增 or eEditType.复制)
                    {
                        if(_manager.GetCmdById(this._voiceCmd.id) is VoiceControlCmd op)
                        {
                            throw new Exception(string.Format("已存在编号为 {0} 的命令,请重命名该命令", op.id));
                        }
                    }
                }
            }
            catch(Exception ex)
            {
                this.DialogResult = DialogResult.None;
                FpiMessageBox.ShowError(ex.Message);
            }
        }

        #endregion
    }
}