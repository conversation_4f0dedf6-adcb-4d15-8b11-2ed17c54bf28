﻿using Fpi.Communication.Protocols;
using Fpi.WMS3000.Remote.GJDBS;

namespace Fpi.WMS3000.Remote.FPISZH
{
    /// <summary>
    /// 谱育科技数智化水站数据传输协议
    /// 基于国家地表水自动监测系统通信协议技术要求拓展
    /// 定制内容：详见README.md文件
    /// </summary>
    public class FPISZHProtocol : GJDBSProtocol
    {
        /// <summary>
        /// 协议名称
        /// </summary>
        public override string FriendlyName => "谱育科技数智化水站数据传输协议";

        /// <summary>
        /// 发送器
        /// </summary>
        /// <returns></returns>
        protected override Sender ConstructSender()
        {
            return new FPISZHSender();
        }

        /// <summary>
        /// 接收器
        /// </summary>
        /// <returns></returns>
        protected override Receiver ConstructReceiver()
        {
            return new FPISZHReceiver();
        }
    }
}
