﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading;
using Fpi.Communication;
using Fpi.Communication.Interfaces;
using Fpi.Communication.Manager;
using Fpi.Communication.Protocols;
using Fpi.Devices;
using Fpi.HB.Business.Protocols.Helper;
using Fpi.HB.Business.Protocols.Interface;
using Fpi.Operations.Config;
using Fpi.WMS3000.Equipment;
using Fpi.WMS3000.Equipment.Interface;
using Fpi.WMS3000.Equipment.WCS3900;
using Fpi.WMS3000.Remote.GJDBS.Config;
using Fpi.WMS3000.Remote.GJDBS.GJDBSDataFrame;
using Fpi.WMS3000.SystemConfig;
using Fpi.Xml;

namespace Fpi.WMS3000.Remote.GJDBS
{
    public class GJDBSSender : Sender, IDataUpload, IGetSuppleData
    {
        #region 字段属性

        /// <summary>
        /// 当前协议描述
        /// </summary>
        protected GJDBSProtocolDesc _desc;

        /// <summary>
        /// 获取计划触发时间锁
        /// </summary>
        protected readonly object _reportLockObj = new object();

        /// <summary>
        /// 统计数据上传定时器
        /// </summary>
        private Timer _timerReportCount;

        /// <summary>
        ///上传软件启动时间定时器
        /// </summary>
        private Timer _timerSendSoftStartTime;

        #endregion

        #region 方法重写

        /// <summary>
        /// 管道生效事件方法
        /// </summary>
        /// <param name="pip"></param>
        protected override void ActionPipe(Pipe pip)
        {
            base.ActionPipe(pip);
            _desc = pip.Protocol.ProtocolDesc as GJDBSProtocolDesc;
            Receiver receiver = pip.Protocol.Receiver;
            if(receiver != null)
            {
                receiver.OnDisConnected -= OnProcessDisConnect;
                receiver.OnDisConnected += OnProcessDisConnect;
            }
            //  初始化定时器配置
            _timerReportCount = new Timer(ReportCountFunc, null, 1000, 3000);
            _timerSendSoftStartTime = new Timer(SendSoftStartTime, null, 10000, Timeout.Infinite);
        }

        private void OnProcessDisConnect(string obj)
        {
            //修改平台断开重连后发送重复数据
            foreach(IdNameNode devNode in _desc.GjdbsSingleCfg.DevNodes)
            {
                if(DeviceManager.GetInstance().GetDeviceById(devNode.id) is IDeviceNotify dev)
                {
                    dev.OnStateChanged -= DeviceState_Changed;
                }
            }
            // 系统日志
            OperationManager.GetInstance().OnStateChanged -= UpdateSysLogInfo;
        }

        protected override void pipe_PipeValidChangedEvent(bool valid)
        {
            base.pipe_PipeValidChangedEvent(valid);
            // 通道有效，且主动上传状态、参数、日志信息
            if(valid && _desc.GjdbsSingleCfg.ReportEquipState)
            {
                // 设备日志
                foreach(IdNameNode devNode in _desc.GjdbsSingleCfg.DevNodes)
                {
                    if(DeviceManager.GetInstance().GetDeviceById(devNode.id) is IDeviceNotify dev)
                    {
                        dev.OnStateChanged += DeviceState_Changed;
                    }
                }
                // 系统日志
                OperationManager.GetInstance().OnStateChanged += UpdateSysLogInfo;
            }
            else
            {
                // 设备日志
                foreach(IdNameNode devNode in _desc.GjdbsSingleCfg.DevNodes)
                {
                    if(DeviceManager.GetInstance().GetDeviceById(devNode.id) is IDeviceNotify dev)
                    {
                        dev.OnStateChanged -= DeviceState_Changed;
                    }
                }
                // 系统日志
                OperationManager.GetInstance().OnStateChanged -= UpdateSysLogInfo;
            }
        }

        /// <summary>
        /// 重写 上传实时数据
        /// </summary>
        /// <param name="currentPipe"></param>
        public override void SendData(Pipe currentPipe)
        {
            // 链接断开时，不传输
            if(!currentPipe.Connected)
            {
                return;
            }

            // 先传输心跳
            UpdateHeartBeatData();
            if(!_desc.GjdbsSingleCfg.OnlyUpdatHeart)
            {
                // 再发送实时数据
                UploadData((int)eUploadDataType.实时数据);
            }

            // 发送状态信息
            if(_desc.GjdbsSingleCfg.ReportEquipState)
            {
                UpdateAllStateInfo();
            }
        }

        /// <summary>
        /// //上传统计数据
        /// </summary>
        /// <param name="obj"></param>
        protected virtual void ReportCountFunc(object obj)
        {
            // 通道关闭，不传输
            if(CurrentPipe != null && !CurrentPipe.valid)
            {
                return;
            }
            // 链接断开时，仍运行，数据进入数据补传表

            #region 参数定义

            DateTime now = DateTime.Now;
            bool hourTrigger = false;
            bool stateTrigger = false;

            #endregion

            #region 获取各类型数据上传触发时间点

            // 获取各类型数据上传触发时间点
            try
            {
                lock(_reportLockObj)
                {
                    DateTime beginHour, endHour;
                    hourTrigger = _desc.GjdbsSingleCfg.IsTriggerHourPLan(now, out beginHour, out endHour);
                    stateTrigger = _desc.GjdbsSingleCfg.IsTriggerStatePLan(now);
                }
            }
            catch(Exception e)
            {
                ProtocolLogHelper.ShowMsg("获取数据自动上传计划出错:" + e.Message);
            }

            #endregion

            #region 根据触发情况上传数据

            // 小时数据 2061
            if(hourTrigger)
            {
                UploadData((int)eUploadDataType.小时数据);

            }
            // 设备参数 3020
            if(stateTrigger)
            {
                //UpdateInspection();
            }

            #endregion
        }

        #endregion

        #region IDisposable 成员

        public override void Dispose()
        {
            base.Dispose();
            if(_timerReportCount != null)
            {
                _timerReportCount.Change(Timeout.Infinite, Timeout.Infinite);
                _timerReportCount.Dispose();
                _timerReportCount = null;
            }
            if(_timerSendSoftStartTime != null)
            {
                _timerSendSoftStartTime.Change(Timeout.Infinite, Timeout.Infinite);
                _timerSendSoftStartTime.Dispose();
                _timerSendSoftStartTime = null;
            }
        }

        #endregion

        #region IDataUpload

        /// <summary>
        /// 上传数据类型
        /// </summary>
        public Type UploadDataType { get => typeof(eUploadDataType); }

        public virtual void UploadData(int type)
        {
            if(_desc != null)
            {
                try
                {
                    if(!Enum.IsDefined(UploadDataType, type))
                    {
                        throw new Exception($"上传类型数值{type}不在本协议支持的类型{UploadDataType.Name}的定义范围内");
                    }
                    eUploadDataType uploadType = (eUploadDataType)type;

                    // 待发送数据表
                    var cmdList = new List<GJDBSCommand>();
                    switch(uploadType)
                    {
                        case eUploadDataType.实时数据:
                            cmdList.Add(GJDBSHelper.BuildRealTimeData(_desc));
                            // 开关量因子数据
                            if(_desc.GjdbsSingleCfg.UploadStateNode)
                            {
                                Thread.Sleep(50);
                                cmdList.Add(GJDBSHelper.BuildStateNodeData(_desc));
                            }
                            break;

                        case eUploadDataType.五参数单独测量数据:
                            cmdList.AddRange(GJDBSHelper.BuildHistoryData(_desc, DateTime.Now.AddDays(-1), DateTime.Now, eGetDataCount.最新一条数据));
                            break;

                        case eUploadDataType.小时数据:
                            if(_desc.GjdbsSingleCfg.ReportEquipState)
                            {
                                cmdList.AddRange(GJDBSHelper.GetAllDevCurrentParamCmdList(_desc, DateTime.Now.AddDays(-1), DateTime.Now, eUploadDataType.小时数据));
                                //防止生成同一个QN，要睡眠一下
                                Thread.Sleep(50);
                            }
                            cmdList.AddRange(GJDBSHelper.BuildHistoryData(_desc, DateTime.Now.AddDays(-1), DateTime.Now, eGetDataCount.最新一条数据));
                            break;

                        case eUploadDataType.加标回收数据:
                            if(_desc.GjdbsSingleCfg.ReportEquipState)
                            {
                                cmdList.AddRange(GJDBSHelper.GetAllDevCurrentParamCmdList(_desc, DateTime.Now.AddDays(-1), DateTime.Now, eUploadDataType.加标回收数据));
                                //防止生成同一个QN，要睡眠一下
                                Thread.Sleep(50);
                            }
                            cmdList.AddRange(GJDBSHelper.BuildAddData(_desc, DateTime.Now.AddDays(-1), DateTime.Now, eGetDataCount.最新一条数据));
                            break;

                        case eUploadDataType.平行样数据:
                            if(_desc.GjdbsSingleCfg.ReportEquipState)
                            {
                                cmdList.AddRange(GJDBSHelper.GetAllDevCurrentParamCmdList(_desc, DateTime.Now.AddDays(-1), DateTime.Now, eUploadDataType.平行样数据));
                                //防止生成同一个QN，要睡眠一下
                                Thread.Sleep(50);
                            }
                            cmdList.AddRange(GJDBSHelper.BuildParallelData(_desc, DateTime.Now.AddDays(-1), DateTime.Now, eGetDataCount.最新一条数据));
                            break;

                        case eUploadDataType.标样核查数据:
                            if(_desc.GjdbsSingleCfg.ReportEquipState)
                            {
                                cmdList.AddRange(GJDBSHelper.GetAllDevCurrentParamCmdList(_desc, DateTime.Now.AddDays(-1), DateTime.Now, eUploadDataType.标样核查数据));
                                //防止生成同一个QN，要睡眠一下
                                Thread.Sleep(50);
                            }
                            cmdList.AddRange(GJDBSHelper.BuildSampleCheckData(_desc, DateTime.Now.AddDays(-1), DateTime.Now, eGetDataCount.最新一条数据));
                            break;

                        case eUploadDataType.零点核查数据:
                            if(_desc.GjdbsSingleCfg.ReportEquipState)
                            {
                                cmdList.AddRange(GJDBSHelper.GetAllDevCurrentParamCmdList(_desc, DateTime.Now.AddDays(-1), DateTime.Now, eUploadDataType.零点核查数据));
                                //防止生成同一个QN，要睡眠一下
                                Thread.Sleep(50);
                            }
                            cmdList.AddRange(GJDBSHelper.BuildBlankCheckData(_desc, DateTime.Now.AddDays(-1), DateTime.Now, eGetDataCount.最新一条数据));
                            break;

                        case eUploadDataType.跨度核查数据:
                            if(_desc.GjdbsSingleCfg.ReportEquipState)
                            {
                                cmdList.AddRange(GJDBSHelper.GetAllDevCurrentParamCmdList(_desc, DateTime.Now.AddDays(-1), DateTime.Now, eUploadDataType.跨度核查数据));
                                //防止生成同一个QN，要睡眠一下
                                Thread.Sleep(50);
                            }
                            cmdList.AddRange(GJDBSHelper.BuildRangeCheckData(_desc, DateTime.Now.AddDays(-1), DateTime.Now, eGetDataCount.最新一条数据));
                            break;

                        case eUploadDataType.超标留样数据:
                            GJDBSCommand cmd = GJDBSHelper.BuildSampleData(_desc);
                            if(cmd != null)
                            {
                                cmdList.Add(cmd);
                            }
                            break;

                        case eUploadDataType.五参数核查数据:
                            cmdList.AddRange(GJDBSHelper.BuildFiveParamCheckData(_desc, DateTime.Now.AddDays(-1), DateTime.Now, eWCS3900DetailDataType.核查数据, eGetDataCount.最新一条数据));
                            break;

                        case eUploadDataType.五参数水样比对数据:
                            cmdList.AddRange(GJDBSHelper.BuildFiveParamCheckData(_desc, DateTime.Now.AddDays(-1), DateTime.Now, eWCS3900DetailDataType.水样比对数据, eGetDataCount.最新一条数据));
                            break;

                        default:
                            throw new ArgumentOutOfRangeException("type", uploadType, null);
                    }

                    // 发送数据
                    foreach(GJDBSCommand cmd in cmdList)
                    {
                        if(cmd != null)
                        {
                            if(uploadType == eUploadDataType.实时数据)
                            {
                                _desc.SendDataFrame(cmd);
                            }
                            else
                            {
                                _desc.SendDataWithAddendum(cmd);
                            }
                        }
                    }
                }
                catch(Exception e)
                {
                    ProtocolLogHelper.ShowMsg($"上传{(eUploadDataType)type}类型数据出错:{e.Message}");
                }
            }
            else
            {
                //throw new Exception("协议描述器为空！");
            }
        }

        #endregion

        #region IGetSuppleData

        public virtual List<IByteStream> GetSuppleData(DateTime startTime, DateTime endTime, int type)
        {
            if(!Enum.IsDefined(UploadDataType, type))
            {
                throw new Exception($"上传类型数值{type}不在本协议支持的类型{UploadDataType.Name}的定义范围内");
            }
            eUploadDataType uploadType = (eUploadDataType)type;

            List<GJDBSCommand> cmdLists;
            switch(uploadType)
            {
                case eUploadDataType.小时数据:
                    cmdLists = GJDBSHelper.BuildHistoryData(_desc, startTime, endTime, eGetDataCount.所有数据);
                    break;
                case eUploadDataType.标样核查数据:
                    cmdLists = GJDBSHelper.BuildSampleCheckData(_desc, startTime, endTime, eGetDataCount.所有数据);
                    break;
                case eUploadDataType.零点核查数据:
                    cmdLists = GJDBSHelper.BuildBlankCheckData(_desc, startTime, endTime, eGetDataCount.所有数据);
                    break;
                case eUploadDataType.跨度核查数据:
                    cmdLists = GJDBSHelper.BuildRangeCheckData(_desc, startTime, endTime, eGetDataCount.所有数据);
                    break;
                case eUploadDataType.加标回收数据:
                    cmdLists = GJDBSHelper.BuildAddData(_desc, startTime, endTime, eGetDataCount.所有数据);
                    break;
                case eUploadDataType.日志状态数据:
                    cmdLists = GJDBSHelper.BuildRuningLogInfo(_desc, startTime, endTime);
                    break;
                case eUploadDataType.五参数核查数据:
                    cmdLists = GJDBSHelper.BuildFiveParamCheckData(_desc, startTime, endTime, eWCS3900DetailDataType.核查数据, eGetDataCount.所有数据);
                    break;
                case eUploadDataType.五参数水样比对数据:
                    cmdLists = GJDBSHelper.BuildFiveParamCheckData(_desc, startTime, endTime, eWCS3900DetailDataType.水样比对数据, eGetDataCount.所有数据);
                    break;
                default:
                    throw new Exception("当前协议不支持补传 " + uploadType.ToString() + " 类型数据！");
            }

            var resultLists = new List<IByteStream>();
            foreach(GJDBSCommand cmd in cmdLists)
            {
                // 日志转码
                if(uploadType == eUploadDataType.日志状态数据)
                {
                    Encoding gb2312 = Encoding.GetEncoding(936);
                    var newCmd = ByteArrayWrap.Build(gb2312.GetBytes(cmd.CombineData()));

                    resultLists.Add(newCmd);
                }
                else
                {
                    resultLists.Add(cmd);
                }
            }
            return resultLists;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 发送心跳数据
        /// </summary>
        private void UpdateHeartBeatData()
        {
            try
            {
                // 发送数据
                GJDBSCommand cmd = GJDBSHelper.BuildHeartBeatData(_desc);
                if(cmd != null)
                {
                    _desc.SendDataFrame(cmd);
                }
            }
            catch(Exception e)
            {
                ProtocolLogHelper.ShowMsg("上传心跳信息出错:" + e.Message);
            }
        }

        /// <summary>
        /// 设备状态变化事件
        /// </summary>
        private void DeviceState_Changed(Device dev, eStateChangedType stateChangedType, string state)
        {
            switch(stateChangedType)
            {
                case eStateChangedType.FlowState:
                    UpdateDevLogInfo(dev, state);
                    break;
            }
        }

        /// <summary>
        /// 发送设备日志信息
        /// </summary>
        private void UpdateDevLogInfo(Device dev, string state)
        {
            try
            {
                if(dev != null)
                {
                    var cmd = GJDBSHelper.GetDevLogInfo(_desc, dev, state);
                    if(cmd != null)
                    {
                        if(_desc.GjdbsSingleCfg.AutoReportLost)
                        {
                            // 保存数据，主动数据补遗用
                            GJDBSProtocolDesc.SaveSendCmd(cmd, CurrentPipe.id);
                        }
                        Encoding gb2312 = Encoding.GetEncoding(936);
                        _desc.SendBytes(gb2312.GetBytes(cmd.CombineData()));

                    }
                }
            }
            catch(Exception e)
            {
                ProtocolLogHelper.ShowMsg("上传设备日志信息出错:" + e.Message);
            }
        }

        /// <summary>
        /// 发送系统日志信息
        /// </summary>
        private void UpdateSysLogInfo(string state)
        {
            try
            {
                var cmd = GJDBSHelper.GetSysLogInfo(_desc, state);
                if(cmd != null)
                {
                    if(_desc.GjdbsSingleCfg.AutoReportLost)
                    {
                        // 保存数据，主动数据补遗用
                        GJDBSProtocolDesc.SaveSendCmd(cmd, CurrentPipe.id);
                    }
                    Encoding gb2312 = Encoding.GetEncoding(936);
                    _desc.SendBytes(gb2312.GetBytes(cmd.CombineData()));
                }
            }
            catch(Exception e)
            {
                ProtocolLogHelper.ShowMsg("上传系统日志信息出错:" + e.Message);
            }
        }

        /// <summary>
        /// 发送设备、系统状态信息
        /// </summary>
        private void UpdateAllStateInfo()
        {
            try
            {
                List<GJDBSCommand> cmdList = GJDBSHelper.GetAllStateCurrentCmdList(_desc);
                foreach(GJDBSCommand cmd in cmdList)
                {
                    try
                    {
                        if(cmd != null)
                        {
                            _desc.SendDataFrame(cmd);
                        }
                    }
                    catch(Exception e)
                    {
                        ProtocolLogHelper.ShowMsg("上传设备状态信息出错:" + e.Message);
                    }
                }
            }
            catch(Exception e)
            {
                ProtocolLogHelper.ShowMsg("上传设备状态信息出错:" + e.Message);
            }
        }

        /// <summary>
        /// 发送软件启动时间
        /// </summary>
        private void SendSoftStartTime(object o)
        {
            // 连接未建立，不传输
            if(!CurrentPipe.Connected)
            {
                return;
            }

            try
            {
                // 发送数据
                var cmd = new GJDBSCommand
                {
                    QN = DateTime.Now.ToString(GJDBSProtocolDesc.QNDateTimeFormat),
                    ST = _desc.ST,
                    CN = "2081",
                    PW = _desc.PW,
                    MN = _desc.MN,
                    Flag = _desc.GjdbsSingleCfg.Flag,
                    CP = new GJDBSCommandParameter
                    {
                        DataTime = SystemHelper.SoftStartTime.ToString(GJDBSProtocolDesc.DateTimeFormat)
                    }
                };

                _desc.SendDataWithAddendum(cmd);
                if(_timerSendSoftStartTime != null)
                {
                    _timerSendSoftStartTime.Change(Timeout.Infinite, Timeout.Infinite);
                    _timerSendSoftStartTime.Dispose();
                    _timerSendSoftStartTime = null;
                }
            }
            catch(Exception e)
            {
                //ProtocolLogHelper.ShowMsg("上传软件启动时间出错:" + e.Message);
            }
        }

        #endregion
    }
}