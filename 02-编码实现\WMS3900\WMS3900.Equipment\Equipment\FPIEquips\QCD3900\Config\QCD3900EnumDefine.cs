﻿using System.ComponentModel;

namespace Fpi.WMS3000.Equipment.QCD3900
{
    /// <summary>
    /// QCD3900O仪器工作状态
    /// </summary>
    public enum eQCD3900DeviceState
    {
        /// <summary>
        /// 系统正常
        /// </summary>
        [Description("系统正常")]
        系统正常 = 0,

        /// <summary>
        /// 系统故障
        /// </summary>
        [Description("系统故障")]
        系统故障 = 1,

        /// <summary>
        /// 系统报警
        /// </summary>
        [Description("系统报警")]
        系统报警 = 2
    }

    /// <summary>
    /// QCD3900O仪器工作状态
    /// </summary>
    public enum eQCD3900FlowState
    {
        /// <summary>
        /// 空闲
        /// </summary>
        [Description("空闲")]
        空闲 = 0,

        /// <summary>
        /// 试剂导入
        /// </summary>
        [Description("试剂导入")]
        试剂导入 = 1,

        /// <summary>
        /// 储液环清洗
        /// </summary>
        [Description("储液环清洗")]
        储液环清洗 = 2,

        /// <summary>
        /// 管路排空
        /// </summary>
        [Description("管路排空")]
        管路排空 = 3,

        /// <summary>
        /// 纯水定容
        /// </summary>
        [Description("纯水定容")]
        纯水定容 = 4,

        /// <summary>
        /// 母液计量
        /// </summary>
        [Description("母液计量")]
        母液计量 = 5,

        /// <summary>
        /// 零点核查
        /// </summary>
        [Description("零点液核查")]
        零点核查 = 6,

        /// <summary>
        /// 跨度核查
        /// </summary>
        [Description("跨度液核查")]
        跨度核查 = 7,

        /// <summary>
        /// 空白核查
        /// </summary>
        [Description("空白核查")]
        空白核查 = 8,

        /// <summary>
        /// 标样核查
        /// </summary>
        [Description("标样核查")]
        标样核查 = 9,

        /// <summary>
        /// 原水样测试
        /// </summary>
        [Description("原水样测试")]
        原水样测试 = 10,

        /// <summary>
        /// 水样杯排空
        /// </summary>
        [Description("水样杯排空")]
        水样杯排空 = 11,

        /// <summary>
        /// 水样杯清洗排空
        /// </summary>
        [Description("水样杯清洗排空")]
        水样杯清洗排空 = 12,

        /// <summary>
        /// 标样杯排空
        /// </summary>
        [Description("标样杯排空")]
        标样杯排空 = 13,

        /// <summary>
        /// 标样杯清洗排空
        /// </summary>
        [Description("标样杯清洗排空")]
        标样杯清洗排空 = 14,

        /// <summary>
        /// 复位清洗排空
        /// </summary>
        [Description("复位清洗排空")]
        复位清洗排空 = 15,

        /// <summary>
        /// 静态加标回收
        /// </summary>
        [Description("静态加标回收")]
        静态加标回收 = 16,

        /// <summary>
        /// 动态加标回收
        /// </summary>
        [Description("动态加标回收")]
        动态加标回收 = 17,

        /// <summary>
        /// 多点线性核查A
        /// </summary>
        [Description("多点线性核查A")]
        多点线性核查A = 18,

        /// <summary>
        /// 多点线性核查B
        /// </summary>
        [Description("多点线性核查B")]
        多点线性核查B = 19,

        /// <summary>
        /// 多点线性核查C
        /// </summary>
        [Description("多点线性核查C")]
        多点线性核查C = 20,

        /// <summary>
        /// 多点线性核查D
        /// </summary>
        [Description("多点线性核查D")]
        多点线性核查D = 21,

        /// <summary>
        /// 终端自检
        /// </summary>
        [Description("终端自检")]
        终端自检 = 22,

        /// <summary>
        /// 盲样测试
        /// </summary>
        [Description("盲样测试")]
        盲样测试 = 23,

        /// <summary>
        /// 水样杯鼓气
        /// </summary>
        [Description("水样杯鼓气")]
        水样杯鼓气 = 24
    }

    /// <summary>
    /// QCD3900O器件类型
    /// </summary>
    public enum eQCD3900ElementType
    {
        /// <summary>
        /// 纯水泵PU1
        /// </summary>
        [Description("纯水泵PU1")]
        纯水泵PU1 = 1,

        /// <summary>
        /// 定容泵PU2
        /// </summary>
        [Description("定容泵PU2")]
        定容泵PU2 = 2,

        /// <summary>
        /// 样品杯气泵PU3
        /// </summary>
        [Description("样品杯气泵PU3")]
        样品杯气泵PU3 = 3,

        /// <summary>
        /// 标样杯气泵PU4
        /// </summary>
        [Description("标样杯气泵PU4")]
        标样杯气泵PU4 = 4,

        /// <summary>
        /// 原水夹管阀SV1
        /// </summary>
        [Description("原水夹管阀SV1")]
        原水夹管阀SV1 = 5,

        /// <summary>
        /// 水样杯夹管阀SV2
        /// </summary>
        [Description("水样杯夹管阀SV2")]
        水样杯夹管阀SV2 = 6,

        /// <summary>
        /// 三通电磁阀SV3
        /// </summary>
        [Description("三通电磁阀SV3")]
        三通电磁阀SV3 = 7,

        /// <summary>
        /// 三通电磁阀SV4
        /// </summary>
        [Description("三通电磁阀SV4")]
        三通电磁阀SV4 = 8,

        /// <summary>
        /// 标样杯夹管阀SV5
        /// </summary>
        [Description("标样杯夹管阀SV5")]
        标样杯夹管阀SV5 = 9,

        /// <summary>
        /// 电磁阀组1DO21
        /// </summary>
        [Description("电磁阀组1DO21")]
        电磁阀组1DO21 = 10,

        /// <summary>
        /// 电磁阀组1DO22
        /// </summary>
        [Description("电磁阀组1DO22")]
        电磁阀组1DO22 = 11,

        /// <summary>
        /// 电磁阀组2DO23
        /// </summary>
        [Description("电磁阀组2DO23")]
        电磁阀组2DO23 = 12,

        /// <summary>
        /// 电磁阀组2DO24
        /// </summary>
        [Description("电磁阀组2DO24")]
        电磁阀组2DO24 = 13,

        /// <summary>
        /// 对外24V输出
        /// </summary>
        [Description("对外24V输出")]
        对外24V输出 = 14,
    }

    /// <summary>
    /// QCD3900O器件类型（用于方便解析协议）
    /// </summary>
    public enum eQCD3900ElementToAnsysisType
    {
        /// <summary>
        /// 电磁阀组1DO21
        /// </summary>
        [Description("电磁阀组1DO21")]
        电磁阀组1DO21 = 0x51,

        /// <summary>
        /// 电磁阀组2DO22
        /// </summary>
        [Description("电磁阀组2DO22")]
        电磁阀组2DO22 = 0x53,

        /// <summary>
        /// 纯水泵PU1
        /// </summary>
        [Description("纯水泵PU1")]
        纯水泵PU1 = 0x55,

        /// <summary>
        /// 定容泵PU2
        /// </summary>
        [Description("定容泵PU2")]
        定容泵PU2 = 0x57,

        /// <summary>
        /// 样品杯气泵PU3
        /// </summary>
        [Description("样品杯气泵PU3")]
        样品杯气泵PU3 = 0x59,

        /// <summary>
        /// 标样杯气泵PU4
        /// </summary>
        [Description("标样杯气泵PU4")]
        标样杯气泵PU4 = 0x5B,

        /// <summary>
        /// 原水夹管阀SV1
        /// </summary>
        [Description("原水夹管阀SV1")]
        原水夹管阀SV1 = 0x5D,

        /// <summary>
        /// 水样杯夹管阀SV2
        /// </summary>
        [Description("水样杯夹管阀SV2")]
        水样杯夹管阀SV2 = 0x5F,

        /// <summary>
        /// 三通电磁阀SV3
        /// </summary>
        [Description("三通电磁阀SV3")]
        三通电磁阀SV3 = 0x61,

        /// <summary>
        /// 三通电磁阀SV4
        /// </summary>
        [Description("三通电磁阀SV4")]
        三通电磁阀SV4 = 0x63,

        /// <summary>
        /// 标样杯夹管阀SV5
        /// </summary>
        [Description("标样杯夹管阀SV5")]
        标样杯夹管阀SV5 = 0x65,

        /// <summary>
        /// 柱塞泵
        /// </summary>
        [Description("柱塞泵")]
        柱塞泵 = 0x67
    }

    /// <summary>
    /// QCD3900O试剂类型（用于方便解析协议）
    /// </summary>
    public enum eQCD3900LiquidToAnsysisType
    {
        ///// <summary>
        ///// 零点液体
        ///// </summary>
        //[Description("零点液体")]
        //零点液体 = 0x28,

        ///// <summary>
        ///// 核查液体
        ///// </summary>
        //[Description("核查液体")]
        //核查液体 = 0x2A,

        /// <summary>
        /// 加标母液
        /// </summary>
        [Description("加标母液")]
        加标母液 = 0x2C,

        /// <summary>
        /// 纯水
        /// </summary>
        [Description("纯水")]
        纯水 = 0x2E,

        /// <summary>
        /// 废液
        /// </summary>
        [Description("废液")]
        废液 = 0x30
    }

    /// <summary>
    /// 日志内容
    /// </summary>
    public enum eQCD3900LogContent
    {
        /// <summary>
        /// 启动流程:试剂导入
        /// </summary>
        [Description("启动流程:试剂导入")]
        试剂导入 = 1,

        /// <summary>
        /// 启动流程:储液环清洗
        /// </summary>
        [Description("启动流程:储液环清洗")]
        储液环清洗 = 2,

        /// <summary>
        /// 启动流程:出厂清洗（管路排空）
        /// </summary>
        [Description("启动流程:出厂清洗（管路排空）")]
        出厂清洗 = 3,

        /// <summary>
        /// 启动流程:纯水定容
        /// </summary>
        [Description("启动流程:纯水定容")]
        纯水定容 = 4,

        /// <summary>
        /// 启动流程:母液计量
        /// </summary>
        [Description("启动流程:母液计量")]
        母液计量 = 5,

        /// <summary>
        /// 启动流程:零点液核查
        /// </summary>
        [Description("启动流程:零点液核查")]
        零点液核查 = 6,

        /// <summary>
        /// 启动流程:跨度液核查
        /// </summary>
        [Description("启动流程:跨度液核查")]
        跨度液核查 = 7,

        /// <summary>
        /// 启动流程:因子空白核查
        /// </summary>
        [Description("启动流程:因子空白核查")]
        因子空白核查 = 8,

        /// <summary>
        /// 启动流程:因子标样核查
        /// </summary>
        [Description("启动流程:因子标样核查")]
        因子标样核查 = 9,

        /// <summary>
        /// 启动流程:原水样测试
        /// </summary>
        [Description("启动流程:原水样测试")]
        原水样测试 = 10,

        /// <summary>
        /// 启动流程:水样杯排空
        /// </summary>
        [Description("启动流程:水样杯排空")]
        水样杯排空 = 11,

        /// <summary>
        /// 启动流程:水样杯清洗排空
        /// </summary>
        [Description("启动流程:水样杯清洗排空")]
        水样杯清洗排空 = 12,

        /// <summary>
        /// 启动流程:标样杯排空
        /// </summary>
        [Description("启动流程:标样杯排空")]
        标样杯排空 = 13,

        /// <summary>
        /// 启动流程:标样杯清洗排空
        /// </summary>
        [Description("启动流程:标样杯清洗排空")]
        标样杯清洗排空 = 14,

        /// <summary>
        /// 启动流程:复位清洗排空
        /// </summary>
        [Description("启动流程:复位清洗排空")]
        复位清洗排空 = 15,

        /// <summary>
        /// 启动流程:静态加标回收前的浓度（FP32）
        /// </summary>
        [Description("启动流程:静态加标回收前的浓度（FP32）")]
        静态加标回收前的浓度 = 16,

        /// <summary>
        /// 启动流程:动态加标回收
        /// </summary>
        [Description("启动流程:动态加标回收")]
        动态加标回收 = 17,

        /// <summary>
        /// 启动流程:多点线性核查A
        /// </summary>
        [Description("启动流程:多点线性核查A")]
        多点线性核查A = 18,

        /// <summary>
        /// 启动流程:多点线性核查B
        /// </summary>
        [Description("启动流程:多点线性核查B")]
        多点线性核查B = 19,

        /// <summary>
        /// 启动流程:多点线性核查C
        /// </summary>
        [Description("启动流程:多点线性核查C")]
        多点线性核查C = 20,

        /// <summary>
        /// 启动流程:多点线性核查D
        /// </summary>
        [Description("启动流程:多点线性核查D")]
        多点线性核查D = 21,

        /// <summary>
        /// 启动流程:终端自检
        /// </summary>
        [Description("启动流程:终端自检")]
        终端自检 = 22,

        /// <summary>
        /// 启动流程:盲样核查
        /// </summary>
        [Description("启动流程:盲样核查")]
        盲样核查 = 23,

        /// <summary>
        /// 水样杯鼓气
        /// </summary>
        [Description("水样杯鼓气")]
        水样杯鼓气 = 24,

        /// <summary>
        /// 系统上电
        /// </summary>
        [Description("系统上电")]
        系统上电 = 25,

        /// <summary>
        /// 修改门禁密码
        /// </summary>
        [Description("修改门禁密码")]
        修改门禁密码 = 26,

        /// <summary>
        /// 零点液更换
        /// </summary>
        [Description("零点液更换")]
        零点液更换 = 27,

        /// <summary>
        /// 核查液更换
        /// </summary>
        [Description("核查液更换")]
        核查液更换 = 28,

        /// <summary>
        /// 母液更换
        /// </summary>
        [Description("母液更换")]
        母液更换 = 29,

        /// <summary>
        /// 柱塞泵溢出
        /// </summary>
        [Description("柱塞泵溢出")]
        柱塞泵溢出 = 30,

        /// <summary>
        /// 柱塞泵通信异常
        /// </summary>
        [Description("柱塞泵通信异常")]
        柱塞泵通信异常 = 31,

        /// <summary>
        /// 柱塞泵移动返回报文异常
        /// </summary>
        [Description("柱塞泵移动返回报文异常")]
        柱塞泵移动返回报文异常 = 32,

        /// <summary>
        /// 柱塞泵上限位移动警告
        /// </summary>
        [Description("柱塞泵上限位移动警告")]
        柱塞泵上限位移动警告 = 33,

        /// <summary>
        /// 柱塞泵校验失败
        /// </summary>
        [Description("柱塞泵校验失败")]
        柱塞泵校验失败 = 34,

        /// <summary>
        /// 温度检测异常
        /// </summary>
        [Description("温度检测异常")]
        温度检测异常 = 35,

        /// <summary>
        /// 流程操作无效（无流路程序）
        /// </summary>
        [Description("流程操作无效（无流路程序）")]
        流程操作无效 = 36,

        /// <summary>
        /// 流程命令无效（流路错误）
        /// </summary>
        [Description("流程命令无效（流路错误）")]
        流程命令无效 = 37
    }
}
