﻿using Fpi.WMS3000.Pollution.Remote.FPISZHWRY.ConfigUI;
using Fpi.WMS3000.Remote.GJDBS;

namespace Fpi.WMS3000.Pollution.Remote.FPISZHWRY
{
    /// <summary>
    /// 谱育科技数智化污染源数据传输协议-协议描述器
    /// </summary>
    public class FPISZHWRYProtocolDesc : GJDBSProtocolDesc
    {
        #region 初始化

        protected override void InitConfig()
        {
            base.InitConfig();
            impProtocolEdit = typeof(FPISZHWRYConfigUC);
        }

        #endregion
    }
}