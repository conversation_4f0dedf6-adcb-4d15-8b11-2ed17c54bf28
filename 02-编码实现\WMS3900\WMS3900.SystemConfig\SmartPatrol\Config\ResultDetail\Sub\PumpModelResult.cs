﻿using System.ComponentModel;
using System.Text;
using Fpi.Util.Extensions;
using Fpi.WMS3000.Equipment.Config;
using Fpi.WMS3000.SystemConfig.SmartPatrol.Helper;

namespace Fpi.WMS3000.SystemConfig.SmartPatrol.Config
{
    /// <summary>
    /// 采水泵模块巡检结果
    /// </summary>
    public class PumpModelResult : SubModelResultBase
    {
        #region 字段属性

        /// <summary>
        /// 1#采水泵状态
        /// </summary>
        [Description("1#采水泵状态")]
        public eWaterPumpState Pump1State { get; set; }

        /// <summary>
        /// 2#采水泵状态
        /// </summary>
        [Description("2#采水泵状态")]
        public eWaterPumpState Pump2State { get; set; }

        /// <summary>
        /// 采水状态
        /// </summary>
        [Description("采水状态")]
        public eWaterCollectionState CollectionState { get; set; }

        /// <summary>
        /// 采水管状态
        /// </summary>
        [Description("采水管状态")]
        public eWaterPipeState WaterPipeState { get; set; }

        /// <summary>
        /// 采水压力状态
        /// </summary>
        [Description("采水压力状态")]
        public eModuleWorkingState WaterPressState { get; set; }

        /// <summary>
        /// 1#采水泵累计工作时长
        /// </summary>
        [Description("1#采水泵累计工作时长")]
        public double Pump1TotalWorkTime { get; set; }

        /// <summary>
        /// 1#采水泵累计使用天数
        /// </summary>
        [Description("1#采水泵累计使用天数")]
        public int Pump1TotalUsedDays { get; set; }

        /// <summary>
        /// 2#采水泵累计工作时长
        /// </summary>
        [Description("2#采水泵累计工作时长")]
        public double Pump2TotalWorkTime { get; set; }

        /// <summary>
        /// 2#采水泵累计使用天数
        /// </summary>
        [Description("2#采水泵累计使用天数")]
        public int Pump2TotalUsedDays { get; set; }

        #endregion

        #region 构造

        public PumpModelResult()
        {
            ModelName = "采水泵";
        }

        #endregion

        #region 方法重写

        /// <summary>
        /// 打印巡检结果
        /// </summary>
        /// <returns></returns>
        public override string GetResultStr()
        {
            StringBuilder resultStr = new StringBuilder();

            resultStr.AppendLine(ModelName)
                .AppendLine($"巡检结果：{PatrolResult}")
                .AppendLine($"采水状态：{CollectionState}")
                .AppendLine($"1#采水泵状态：{Pump1State}")
                .AppendLine($"2#采水泵状态：{Pump2State}")
                .AppendLine($"采水管状态：{WaterPipeState}")
                .AppendLine($"采水压力状态：{WaterPressState}")
                .AppendLine($"1#采水泵累计工作时长：{Pump1TotalWorkTime.ToDisplayFormat(PatrolDataDisplayHelper.FloatFormat)}")
                .AppendLine($"1#采水泵累计使用天数：{Pump1TotalUsedDays.ToDisplayFormat()}")
                .AppendLine($"2#采水泵累计工作时长：{Pump2TotalWorkTime.ToDisplayFormat(PatrolDataDisplayHelper.FloatFormat)}")
                .AppendLine($"2#采水泵累计使用天数：{Pump2TotalUsedDays.ToDisplayFormat()}");

            return resultStr.ToString();
        }

        #endregion
    }
}