﻿using System;
using Fpi.Devices;
using Fpi.Operations;
using Fpi.Operations.Interfaces;
using Fpi.WMS3000.Equipment;
using Fpi.WMS3000.Equipment.Config;
using Fpi.WMS3000.Equipment.Interface;
using Fpi.WMS3000.SystemConfig;

namespace Fpi.WMS3000.Pollution.SystemOperation.OperationTemplate
{
    /// <summary>
    /// 混采采样器供样
    /// </summary>
    public class Op_SampleSupply : CustomOperation
    {
        #region 字段属性

        private bool _isInited;

        /// <summary>
        /// 系统所用采样器
        /// </summary>
        private Device _sampleEquip;

        #endregion

        #region 公共方法（重写）

        public override string ToString()
        {
            return "混采采样器供样";
        }

        public override object CustomDo(string instrumentId, bool manual, object inputData, IOperationListener operationListener)
        {
            try
            {
                if(!_isInited)
                {
                    InitProperty();
                }

                // 获取当前小时应使用的供样桶
                var _currentSupplyBucket = ExterEquipConfigManager.GetInstance().WaterCollectionConfigInfo.CurrentSupplyBucketSet;
                eMixedSampleDeviceOperType operType = _currentSupplyBucket == eSamplingBucket.A桶 ? eMixedSampleDeviceOperType.A桶供样 : eMixedSampleDeviceOperType.B桶供样;

                SystemLogHelper.WriteOperationLog($"触发采样器从{_currentSupplyBucket}供样");

                // 触发采样器从指定桶供样
                ((IMixedSampleDeviceOperation)_sampleEquip).StartOper(operType);

                SystemLogHelper.WriteOperationLog("[" + GetOperationName() + "]操作完成！");
            }
            catch(Exception ex)
            {
                SystemLogHelper.WriteSystemErrorLog("[" + GetOperationName() + "]操作异常: " + ex.Message);
                SystemLogHelper.WriteOperationLog("[" + GetOperationName() + "]操作异常: " + ex.Message);
            }

            return null;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化参数
        /// </summary>
        private void InitProperty()
        {
            if(ExterEquipConfigManager.GetInstance().DeviceSelect.SampleDevice is not IMixedSampleDeviceOperation)
            {
                throw new Exception($"请配置标准混采采样器！");
            }

            _sampleEquip = ExterEquipConfigManager.GetInstance().DeviceSelect.SampleDevice;

            _isInited = true;
        }

        #endregion
    }
}