﻿using System;
using System.Collections.Generic;
using System.Windows.Forms;
using Fpi.Alarm;
using Fpi.Communication;
using Fpi.Communication.Converter;
using Fpi.Communication.Interfaces;
using Fpi.Data.Config;
using Fpi.Devices;
using Fpi.Devices.Channel;
using Fpi.Devices.DeviceProtocols;
using Fpi.Util.EnumRelated;
using Fpi.WMS3000.Equipment.Interface;
using Fpi.WMS3000.Equipment.SIA3900;
using Fpi.WMS3000.Equipment.UI;

namespace Fpi.WMS3000.Equipment
{
    /// <summary>
    /// 谱育科技SUPEC5120智能水质在线监测仪
    /// </summary>
    public class SIA3900Equipment : Device, IMeasureDeviceOperation, ISIA3900DeviceParamTransfer,
        IDeviceNotify, IQCDataGet, IDeviceRangeSet, IDeviceKeyParams
    {
        #region 字段属性

        /// <summary>
        /// 仪表关键参数
        /// </summary>
        public SIA3900DeviceKeyParams DeviceKeyParams { get; } = new SIA3900DeviceKeyParams();

        /// <summary>
        /// 测量数据
        /// </summary>
        public SIA3900MeasureParam MeasureParams { get; } = new SIA3900MeasureParam();

        /// <summary>
        /// 状态告警数据
        /// </summary>
        public SIA3900StateAlarm StateAlarm { get; } = new SIA3900StateAlarm();

        /// <summary>
        /// 主页状态数据
        /// </summary>
        public SIA3900HomePageStatus HomePageStatus { get; } = new SIA3900HomePageStatus();

        /// <summary>
        /// 试剂维护数据
        /// </summary>
        public SIA3900ReagentMaintenance ReagentMaintenance { get; } = new SIA3900ReagentMaintenance();

        /// <summary>
        /// 历史数据区 
        /// </summary>
        public SIA3900HistoryDate HistoryDate { get; } = new SIA3900HistoryDate();

        /// <summary>
        /// 标定系数区 
        /// </summary>
        public SIA3900CalibrateCoefficient CalibrateCoefficient { get; } = new SIA3900CalibrateCoefficient();

        /// <summary>
        /// 设备日志区
        /// </summary>
        public SIA3900LogArea LogArea { get; } = new SIA3900LogArea();

        /// <summary>
        /// 寿命监测区
        /// </summary>
        public SIA3900LifeMonitor LifeMonitors { get; } = new SIA3900LifeMonitor();

        /// <summary>
        /// 诊断记录
        /// </summary>
        public SIA3900DiagnosisRecords DiagnosisRecords { get; } = new SIA3900DiagnosisRecords();

        #endregion

        #region 构造

        public SIA3900Equipment()
        {
            DeviceType = eDeviceType.WMS;

            if(string.IsNullOrEmpty(Description))
            {
                Description = "谱育科技SUPEC5120智能水质在线监测仪";
            }

            if(id.Contains("DefaultID"))
            {
                id = "SIA3900_Equip";
            }

            if(name.Contains("DefaultName"))
            {
                name = "谱育科技SUPEC5120智能水质在线监测仪";
            }

            if(string.IsNullOrEmpty(AlarmGroupId))
            {
                AlarmGroupId = "SIA3900_Equip";
            }

            if(string.IsNullOrEmpty(AlarmSourceId))
            {
                AlarmSourceId = "SIA3900_Equip";
            }

            if(string.IsNullOrEmpty(this.ProtocolImp))
            {
                this.ProtocolImp = typeof(ModbusTCPProtocol).FullName;
            }
        }

        #endregion

        #region 公共（重写）方法

        public override string ToString()
        {
            return string.IsNullOrEmpty(name) || name.Contains("DefaultName") ? "谱育科技SUPEC5120智能水质在线监测仪" : name;
        }

        public override void SetDeviceAlarmList()
        {
            this.DeviceAlarmList.Clear();
            this.DeviceAlarmList.Add(ComErrorAlarmCodeId, "设备与PC通信异常");
            this.DeviceAlarmList.Add("1", "泵速度或位置设置错误");
            this.DeviceAlarmList.Add("2", "泵马达使能关闭或其他异常");
            this.DeviceAlarmList.Add("3", "泵加减速参数错误");
            this.DeviceAlarmList.Add("4", "泵加速曲线内存分配失败");
            this.DeviceAlarmList.Add("5", "泵减速曲线内存分配失败");
            this.DeviceAlarmList.Add("6", "泵在不允许的状态下进行写操作");
            this.DeviceAlarmList.Add("7", "泵返回上限位移动报警");
            this.DeviceAlarmList.Add("8", "选向阀未知错误");
            this.DeviceAlarmList.Add("9", "选向阀任务挂起");
            this.DeviceAlarmList.Add("10", "选向阀未知位置");
            this.DeviceAlarmList.Add("11", "选向阀电机堵转");
            this.DeviceAlarmList.Add("12", "选向阀电机忙错误");
            this.DeviceAlarmList.Add("13", "选向阀光耦错误");
            this.DeviceAlarmList.Add("14", "选向阀参数错误");
            this.DeviceAlarmList.Add("15", "选向阀通讯帧错误");
            this.DeviceAlarmList.Add("16", "选向阀通讯异常");
            this.DeviceAlarmList.Add("17", "EEPROM自检故障");
            this.DeviceAlarmList.Add("18", "RTC故障");
            this.DeviceAlarmList.Add("19", "外部FLASH故障");
            this.DeviceAlarmList.Add("20", "柱塞泵溢出");
            this.DeviceAlarmList.Add("21", "柱塞泵通信异常");
            this.DeviceAlarmList.Add("22", "柱塞泵返回帧异常");
            this.DeviceAlarmList.Add("23", "光路自检异常");
            this.DeviceAlarmList.Add("25", "参考光路自检异常");
            this.DeviceAlarmList.Add("26", "温度传感器异常");
            this.DeviceAlarmList.Add("27", "板载温度传感器异常");
            this.DeviceAlarmList.Add("28", "流程命令操作无效");
            this.DeviceAlarmList.Add("29", "漏液报警");
            this.DeviceAlarmList.Add("30", "试剂超期");
            this.DeviceAlarmList.Add("31", "柱塞泵校验失败");
            this.DeviceAlarmList.Add("32", "选向阀校验失败");
            this.DeviceAlarmList.Add("33", "柱塞泵电机忙");
            this.DeviceAlarmList.Add("34", "总氮信号板异常");
            this.DeviceAlarmList.Add("40", "加热超时");
            this.DeviceAlarmList.Add("41", "降温超时");
            this.DeviceAlarmList.Add("42", "温度波动过大");
            this.DeviceAlarmList.Add("43", "恒温温度过低");
            this.DeviceAlarmList.Add("44", "检测室温度异常");
            this.DeviceAlarmList.Add("45", "机箱温度异常");
            this.DeviceAlarmList.Add("46", "滴定数过少");
            this.DeviceAlarmList.Add("47", "滴定数超标");
            this.DeviceAlarmList.Add("48", "试剂不足");
            this.DeviceAlarmList.Add("49", "主光路信号过强");
            this.DeviceAlarmList.Add("50", "主光路信号不足");
            this.DeviceAlarmList.Add("51", "单脉冲信号过强");
            this.DeviceAlarmList.Add("52", "单脉冲信号不足");
            this.DeviceAlarmList.Add("53", "参考光路信号过强");
            this.DeviceAlarmList.Add("54", "参考光路信号不足");
            this.DeviceAlarmList.Add("57", "液体无液检测报警");
            this.DeviceAlarmList.Add("58", "采样错误");
            this.DeviceAlarmList.Add("59", "总氮吸光度异常");
            this.DeviceAlarmList.Add("60", "载流液未抽到试剂");
            this.DeviceAlarmList.Add("61", "一号管路未抽到试剂");
            this.DeviceAlarmList.Add("62", "未抽到载流液/2号管路未抽到试剂");
            this.DeviceAlarmList.Add("63", "未抽到载流液/3号管路未抽到试剂");
            this.DeviceAlarmList.Add("64", "四号管路未抽到试剂");
            this.DeviceAlarmList.Add("65", "五号管路未抽到试剂");
            this.DeviceAlarmList.Add("66", "六号管路未抽到试剂");
            this.DeviceAlarmList.Add("67", "七号管路未抽到试剂");
            this.DeviceAlarmList.Add("68", "八号管路未抽到试剂");
            this.DeviceAlarmList.Add("69", "九号管路未抽到试剂");
            this.DeviceAlarmList.Add("70", "液体检测器1校准异常");
            this.DeviceAlarmList.Add("71", "液体检测器2校准异常");
            this.DeviceAlarmList.Add("72", "总氮电机未转到位");
            this.DeviceAlarmList.Add("73", "流程地址无效");
            this.DeviceAlarmList.Add("74", "柱塞泵上限位移动报警");
            this.DeviceAlarmList.Add("75", "预处理无水报警");
            this.DeviceAlarmList.Add("76", "四号管无水样");
            this.DeviceAlarmList.Add("77", "总氮光路1自检异常");
            this.DeviceAlarmList.Add("78", "总氮光路2自检异常 ");
            this.DeviceAlarmList.Add("79", "超下限报警");
            this.DeviceAlarmList.Add("80", "计算未标定");
            this.DeviceAlarmList.Add("81", "超上限报警");
            this.DeviceAlarmList.Add("82", "超量程报警");
            this.DeviceAlarmList.Add("83", "量程切换无效");
            this.DeviceAlarmList.Add("84", "标定异常");
            this.DeviceAlarmList.Add("85", "测量异常");
            this.DeviceAlarmList.Add("90", "负漂");
            this.DeviceAlarmList.Add("91", "多峰报警");
            this.DeviceAlarmList.Add("92", "样品空白无峰报警");
            this.DeviceAlarmList.Add("93", "样品无峰报警");
            this.DeviceAlarmList.Add("94", "标液空白无峰报警");
            this.DeviceAlarmList.Add("95", "标液无峰报警");
            this.DeviceAlarmList.Add("96", "采样饱和");
            this.DeviceAlarmList.Add("97", "七号阀位试剂不足");
            this.DeviceAlarmList.Add("98", "八号阀位试剂不足");
            this.DeviceAlarmList.Add("99", "九号阀位试剂不足");
            this.DeviceAlarmList.Add("100", "十号阀位试剂不足");
            this.DeviceAlarmList.Add("101", "通信异常");
            this.DeviceAlarmList.Add("102", "吸光度异常");
            this.DeviceAlarmList.Add("103", "拟合优度欠佳");
            this.DeviceAlarmList.Add("104", "核查异常");
            this.DeviceAlarmList.Add("105", "负漂下限");
            this.DeviceAlarmList.Add("106", "即标异常");
        }

        /// <summary>
        /// 设备状态参数查看界面
        /// </summary>
        public override UserControl GetDeviceParamUC()
        {
            return new UC_SIA3900Param(this);
        }

        /// <summary>
        /// 解析测量数据
        /// </summary>
        public override void GetDeviceData()
        {
            try
            {
                byte[] dataSend = { byte.Parse(Addr), 0x03, 0x10, 0x00, 0x00, 0x68 };
                IByteStream bs = new ByteArrayWrap(dataSend);
                IByteStream bsRecv = SendToDevice(id, bs);
                if(bsRecv == null)
                {
                    throw new Exception("无数据回应!");
                }
                byte[] dataReceive = bsRecv.GetBytes();

                // 出错功能码
                if(dataReceive.Length > 2 && dataReceive[1] == 0x83)
                {
                    throw new Exception($"设备回应错误类型：{(eControlErrorType)dataReceive[2]}!");
                }
                if(dataReceive.Length < 211)
                {
                    throw new Exception("回应数据长度不合法！");
                }

                MeasureParams.UpdateMeasureData(dataReceive, 3);

                if(this.InValueChannels.GetCount() > 0 && ((InValueChannel)InValueChannels[0]).ValueNode != null)
                {
                    ((InValueChannel)InValueChannels[0]).ChannelValue = MeasureParams.WatetrValue;
                    ((InValueChannel)InValueChannels[0]).ValueNode.RealDatatime = MeasureParams.WaterDataTime;
                    ((InValueChannel)InValueChannels[0]).ValueNode.State = (int)MeasureParams.WaterFlag;
                }

                //消除通信异常报警
                AlarmManager.GetInstance().RemoveAlarm(AlarmSourceId, ComErrorAlarmCodeId);
                _communicationErrorCount = 0;
            }
            catch(Exception ex)
            {
                if(_communicationErrorCount++ >= 3)
                {
                    foreach(InValueChannel inValueChl in InValueChannels)
                    {
                        if(inValueChl.ValueNode != null)
                        {
                            inValueChl.ValueNode.State = (int)eValueNodeState.F;
                        }

                        inValueChl.ChannelValue = float.NaN;
                    }

                    //增加通信异常报警
                    AlarmManager.GetInstance().AddAlarm(AlarmSourceId, ComErrorAlarmCodeId);
                }
                throw new Exception($"通讯异常，{name}读取数据失败：{ex.Message}");
            }
            finally
            {
                SetRealDataToNode();
            }
        }

        /// <summary>
        /// 获取设备状态及参数信息
        /// </summary>
        public override void GetDeviceState()
        {
            try
            {
                #region 关键参数

                #region 关键参数1

                byte[] dataSend = new byte[] { byte.Parse(Addr), 0x03, 0x10, 0xA0, 0x00, 0x54 };
                IByteStream bs = new ByteArrayWrap(dataSend);
                IByteStream bsRecv = SendToDevice(id, bs) ?? throw new Exception("读取关键参数1无数据回应!");

                byte[] dataReceive = bsRecv.GetBytes();

                if(dataReceive.Length > 2 && dataReceive[1] == 0x83)
                {
                    throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                }
                if(dataReceive.Length < 171)
                {
                    throw new Exception("读取关键参数1回应数据长度不足！");
                }

                // 更新关键参数
                DeviceKeyParams.UpdateKeyParam1(dataReceive, 3);

                #endregion

                #region 关键参数2

                dataSend = new byte[] { byte.Parse(Addr), 0x03, 0x11, 0x00, 0x00, 0x14 };
                bs = new ByteArrayWrap(dataSend);
                bsRecv = SendToDevice(id, bs) ?? throw new Exception("读取关键参数2无数据回应!");

                dataReceive = bsRecv.GetBytes();

                if(dataReceive.Length > 2 && dataReceive[1] == 0x83)
                {
                    throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                }
                if(dataReceive.Length < 43)
                {
                    throw new Exception("读取关键参数2回应数据长度不足！");
                }

                // 更新关键参数
                DeviceKeyParams.UpdateKeyParam2(dataReceive, 3);

                #endregion

                #region 关键参数3

                dataSend = new byte[] { byte.Parse(Addr), 0x03, 0x20, 0x00, 0x00, 0x03 };
                bs = new ByteArrayWrap(dataSend);
                bsRecv = SendToDevice(id, bs) ?? throw new Exception("读取关键参数3无数据回应!");

                dataReceive = bsRecv.GetBytes();

                if(dataReceive.Length > 2 && dataReceive[1] == 0x83)
                {
                    throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                }
                if(dataReceive.Length < 9)
                {
                    throw new Exception("读取关键参数3回应数据长度不足！");
                }

                // 更新关键参数
                DeviceKeyParams.UpdateKeyParam3(dataReceive, 3);

                #endregion

                #endregion

                #region 主页状态区

                dataSend = new byte[] { byte.Parse(Addr), 0x03, 0x13, 0x00, 0x00, 0x42 };
                bs = new ByteArrayWrap(dataSend);
                bsRecv = SendToDevice(id, bs) ?? throw new Exception("读取主页状态区无数据回应!");

                dataReceive = bsRecv.GetBytes();

                if(dataReceive.Length > 2 && dataReceive[1] == 0x83)
                {
                    throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                }
                if(dataReceive.Length < 135)
                {
                    throw new Exception("读取主页状态区1回应数据长度不足！");
                }

                HomePageStatus.UpdateHomePageStatus1(dataReceive, 3);

                dataSend = new byte[] { byte.Parse(Addr), 0x03, 0x13, 0x42, 0x00, 0x42 };
                bs = new ByteArrayWrap(dataSend);
                bsRecv = SendToDevice(id, bs) ?? throw new Exception("读取主页状态区无数据回应!");

                dataReceive = bsRecv.GetBytes();

                if(dataReceive.Length > 2 && dataReceive[1] == 0x83)
                {
                    throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                }
                if(dataReceive.Length < 135)
                {
                    throw new Exception("读取主页状态区2回应数据长度不足！");
                }

                HomePageStatus.UpdateHomePageStatus2(dataReceive, 3);

                #endregion

                #region 试剂维护

                dataSend = new byte[] { byte.Parse(Addr), 0x03, 0x13, 0xB0, 0x00, 0x4A };
                bs = new ByteArrayWrap(dataSend);
                bsRecv = SendToDevice(id, bs) ?? throw new Exception("读取试剂维护区无数据回应!");

                dataReceive = bsRecv.GetBytes();

                if(dataReceive.Length > 2 && dataReceive[1] == 0x83)
                {
                    throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                }
                if(dataReceive.Length < 151)
                {
                    throw new Exception("读取试剂维护区回应数据长度不足！");
                }

                ReagentMaintenance.UpdataValue(dataReceive, 3);

                #endregion

                #region 历史数据

                dataSend = new byte[] { byte.Parse(Addr), 0x03, 0x14, 0x60, 0x00, 0x19 };
                bs = new ByteArrayWrap(dataSend);
                bsRecv = SendToDevice(id, bs) ?? throw new Exception("读取历史数据区无数据回应!");

                dataReceive = bsRecv.GetBytes();

                if(dataReceive.Length > 2 && dataReceive[1] == 0x83)
                {
                    throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                }
                if(dataReceive.Length < 53)
                {
                    throw new Exception("读取历史数据区回应数据长度不足！");
                }

                HistoryDate.UpdataValue(dataReceive, 3);

                #endregion

                #region 标定系数详细区（目前支持四点标定）

                // 不为高指才解析四点标定区
                byte[] dataReceiveDetail = new byte[] { };
                if(!this.TypeDesc.Equals(eDeviceMeasureType.CODMn.ToString()))
                {
                    dataSend = new byte[] { byte.Parse(Addr), 0x03, 0x15, 0x40, 0x00, 0x7C };
                    bs = new ByteArrayWrap(dataSend);
                    bsRecv = SendToDevice(id, bs) ?? throw new Exception("读取标定系数详细区无数据回应!");

                    dataReceiveDetail = bsRecv.GetBytes();

                    if(dataReceiveDetail.Length > 2 && dataReceiveDetail[1] == 0x83)
                    {
                        throw new Exception($"{(eControlErrorType)dataReceiveDetail[2]}!");
                    }
                    if(dataReceiveDetail.Length < 251)
                    {
                        throw new Exception("读取标定系数详细区回应数据长度不足！");
                    }
                }

                #endregion

                #region 标定系数区

                dataSend = new byte[] { byte.Parse(Addr), 0x03, 0x15, 0x00, 0x00, 0x26 };
                bs = new ByteArrayWrap(dataSend);
                bsRecv = SendToDevice(id, bs) ?? throw new Exception("读取标定系数区无数据回应!");

                dataReceive = bsRecv.GetBytes();

                if(dataReceive.Length > 2 && dataReceive[1] == 0x83)
                {
                    throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                }
                if(dataReceive.Length < 79)
                {
                    throw new Exception("读取标定系数区回应数据长度不足！");
                }

                CalibrateCoefficient.UpdataValue(dataReceive, 3, this, dataReceiveDetail);

                #endregion

                #region 寿命监测区

                dataSend = new byte[] { byte.Parse(Addr), 0x03, 0x17, 0x04, 0x00, 0x72 };
                bs = new ByteArrayWrap(dataSend);
                bsRecv = SendToDevice(id, bs) ?? throw new Exception("读取寿命监测区无数据回应!");

                dataReceive = bsRecv.GetBytes();

                if(dataReceive.Length > 2 && dataReceive[1] == 0x83)
                {
                    throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                }
                if(dataReceive.Length < 223)
                {
                    throw new Exception("读取寿命监测区回应数据长度不足！");
                }

                LifeMonitors.UpdataValue(dataReceive, 3);

                #endregion

                #region 最新诊断记录

                dataSend = new byte[] { byte.Parse(Addr), 0x03, 0x17, 0x00, 0x00, 0x04 };
                bs = new ByteArrayWrap(dataSend);
                bsRecv = SendToDevice(id, bs) ?? throw new Exception("读取最新诊断记录无数据回应!");

                dataReceive = bsRecv.GetBytes();

                if(dataReceive.Length > 2 && dataReceive[1] == 0x83)
                {
                    throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                }
                if(dataReceive.Length < 11)
                {
                    throw new Exception("读取最新诊断记录回应数据长度不足！");
                }

                DiagnosisRecords.UpdataValue(dataReceive, 3, this.id);

                #endregion
            }
            catch(Exception e)
            {
                throw new Exception($"{name}读取设备状态及参数信息失败:{e.Message}");
            }
        }

        /// <summary>
        /// 解析设备报警
        /// </summary>
        public override void GetDeviceAlarm()
        {
            try
            {
                #region 状态告警区

                byte[] dataSend = { byte.Parse(Addr), 0x03, 0x10, 0x80, 0x00, 0x13 };
                IByteStream bs = new ByteArrayWrap(dataSend);
                IByteStream bsRecv = SendToDevice(id, bs) ?? throw new Exception("读取状态告警区无数据回应!");
                byte[] dataReceive = bsRecv.GetBytes();

                if(dataReceive.Length > 2 && dataReceive[1] == 0x83)
                {
                    throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                }
                if(dataReceive.Length < 41)
                {
                    throw new Exception("读取状态告警区回应数据长度不合法！");
                }

                // 更新状态告警区关键参数
                StateAlarm.UpdateStateAlarm(dataReceive, 3);

                // 更新设备状态
                DeviceState = (int)StateAlarm.WorkState;

                // 往关键参数区赋值
                DeviceKeyParams.MeasureSpan = StateAlarm.MeasureSpan;

                #endregion

                #region 日志区和报警区

                dataSend = new byte[] { byte.Parse(Addr), 0x03, 0x16, 0x00, 0x00, 0x60 };
                bs = new ByteArrayWrap(dataSend);
                bsRecv = SendToDevice(id, bs) ?? throw new Exception("读取日志区和报警区无数据回应!");

                dataReceive = bsRecv.GetBytes();

                if(dataReceive.Length > 2 && dataReceive[1] == 0x83)
                {
                    throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                }
                if(dataReceive.Length < 195)
                {
                    throw new Exception("读取日志区和报警区回应数据长度不足！");
                }

                LogArea.UpdataValue(dataReceive, 3, this.id);

                ParseDeviceAlarm(dataReceive, 163);

                #endregion
            }
            catch(Exception e)
            {
                throw new Exception($"{name}获取报警数据失败:{e.Message}");
            }
        }

        #endregion

        #region 公共方法

        #region 控制命令

        /// <summary>
        /// 校时
        /// </summary>
        /// <param name="time"></param>
        public void SetDeviceTime(DateTime time)
        {
            try
            {
                byte[] dataSend =
                  { byte.Parse(Addr), 0x10, 0x12, 0x00, 0x00, 0x03, 0x06,
                    (byte) DataConvertHelper.ConvertBcdToHex(time.Year - 2000),
                    (byte) DataConvertHelper.ConvertBcdToHex(time.Month),
                    (byte) DataConvertHelper.ConvertBcdToHex(time.Day),
                    (byte) DataConvertHelper.ConvertBcdToHex(time.Hour),
                    (byte) DataConvertHelper.ConvertBcdToHex(time.Minute),
                    (byte) DataConvertHelper.ConvertBcdToHex(time.Second)
                };
                IByteStream bs = new ByteArrayWrap(dataSend);
                IByteStream bsRecv = SendToDevice(id, bs);
                if(bsRecv == null)
                {
                    throw new Exception("无数据回应!");
                }
                byte[] dataReceive = bsRecv.GetBytes();
                if(dataReceive.Length > 2 && dataReceive[1] == 0x90)
                {
                    throw new Exception(string.Format("{0}!", (eControlErrorType)dataReceive[2]));
                }
                if(dataReceive.Length < 6)
                {
                    throw new Exception("回应数据长度不合法！");
                }
            }
            catch(Exception ex)
            {
                throw new Exception(string.Format("[{0}]校准时间失败：{1}", name, ex.Message));
            }
        }

        /// <summary>
        /// 模式设置
        /// </summary>
        /// <param name="eGbDeviceMeasureMode"></param>
        public void SetDeviceMode(eGbDeviceMeasureMode eGbDeviceMeasureMode)
        {
            try
            {
                List<byte> dataSend = new List<byte>();
                dataSend.AddRange(new byte[] { byte.Parse(Addr), 0x10, 0x12, 0x00, 0x00, 0x02, 0x04, 0x00, 0x0E });
                dataSend.AddRange(DataConverter.GetInstance().GetBytes((int)eGbDeviceMeasureMode));
                IByteStream bs = new ByteArrayWrap(dataSend.ToArray());
                IByteStream bsRecv = SendToDevice(id, bs);

                if(bsRecv == null)
                {
                    throw new Exception("无数据回应!");
                }
                byte[] dataReceive = bsRecv.GetBytes();
                if(dataReceive.Length > 2 && dataReceive[1] == 0x90)
                {
                    throw new Exception(string.Format("{0}!", (eControlErrorType)dataReceive[2]));
                }
                if(dataReceive.Length < 6)
                {
                    throw new Exception("回应数据长度不合法！");
                }
            }
            catch(Exception ex)
            {
                throw new Exception(string.Format("[{0}]模式设置失败：{1}", name, ex.Message));
            }
        }

        /// <summary>
        /// 测量间隔设置
        /// </summary>
        /// <param name="stamp">间隔</param>
        public void SetMeasureStamp(int stamp)
        {
            try
            {
                List<byte> dataSend = new List<byte>();
                dataSend.AddRange(new byte[] { byte.Parse(Addr), 0x10, 0x12, 0x00, 0x00, 0x02, 0x04, 0x00, 0x0F });
                dataSend.AddRange(DataConverter.GetInstance().GetBytes(stamp));
                IByteStream bs = new ByteArrayWrap(dataSend.ToArray());
                IByteStream bsRecv = SendToDevice(id, bs);

                if(bsRecv == null)
                {
                    throw new Exception("无数据回应!");
                }
                byte[] dataReceive = bsRecv.GetBytes();
                if(dataReceive.Length > 2 && dataReceive[1] == 0x90)
                {
                    throw new Exception(string.Format("{0}!", (eControlErrorType)dataReceive[2]));
                }
                if(dataReceive.Length < 6)
                {
                    throw new Exception("回应数据长度不合法！");
                }
            }
            catch(Exception ex)
            {
                throw new Exception(string.Format("[{0}]测量间隔设置失败：{1}", name, ex.Message));
            }
        }

        /// <summary>
        /// 零点核查间隔设置
        /// </summary>
        /// <param name="stamp">间隔</param>
        public void SetZeroCheckStamp(int stamp)
        {
            try
            {
                List<byte> dataSend = new List<byte>();
                dataSend.AddRange(new byte[] { byte.Parse(Addr), 0x10, 0x12, 0x00, 0x00, 0x02, 0x04, 0x00, 0x10 });
                dataSend.AddRange(DataConverter.GetInstance().GetBytes(stamp));
                IByteStream bs = new ByteArrayWrap(dataSend.ToArray());
                IByteStream bsRecv = SendToDevice(id, bs);

                if(bsRecv == null)
                {
                    throw new Exception("无数据回应!");
                }
                byte[] dataReceive = bsRecv.GetBytes();
                if(dataReceive.Length > 2 && dataReceive[1] == 0x90)
                {
                    throw new Exception(string.Format("{0}!", (eControlErrorType)dataReceive[2]));
                }
                if(dataReceive.Length < 6)
                {
                    throw new Exception("回应数据长度不合法！");
                }
            }
            catch(Exception ex)
            {
                throw new Exception(string.Format("[{0}]零点核查间隔设置：{1}", name, ex.Message));
            }
        }

        /// <summary>
        /// 跨度核查间隔设置
        /// </summary>
        /// <param name="stamp">间隔</param>
        public void SetRangeCheckStamp(int stamp)
        {
            try
            {
                List<byte> dataSend = new List<byte>();
                dataSend.AddRange(new byte[] { byte.Parse(Addr), 0x10, 0x12, 0x00, 0x00, 0x02, 0x04, 0x00, 0x11 });
                dataSend.AddRange(DataConverter.GetInstance().GetBytes(stamp));
                IByteStream bs = new ByteArrayWrap(dataSend.ToArray());
                IByteStream bsRecv = SendToDevice(id, bs);

                if(bsRecv == null)
                {
                    throw new Exception("无数据回应!");
                }
                byte[] dataReceive = bsRecv.GetBytes();
                if(dataReceive.Length > 2 && dataReceive[1] == 0x90)
                {
                    throw new Exception(string.Format("{0}!", (eControlErrorType)dataReceive[2]));
                }
                if(dataReceive.Length < 6)
                {
                    throw new Exception("回应数据长度不合法！");
                }
            }
            catch(Exception ex)
            {
                throw new Exception(string.Format("[{0}]跨度核查间隔设置：{1}", name, ex.Message));
            }
        }

        /// <summary>
        /// 标样核查间隔设置
        /// </summary>
        /// <param name="stamp">间隔</param>
        public void SetGuidSampleCheckStamp(int stamp)
        {
            try
            {
                List<byte> dataSend = new List<byte>();
                dataSend.AddRange(new byte[] { byte.Parse(Addr), 0x10, 0x12, 0x00, 0x00, 0x02, 0x04, 0x00, 0x12 });
                dataSend.AddRange(DataConverter.GetInstance().GetBytes(stamp));
                IByteStream bs = new ByteArrayWrap(dataSend.ToArray());
                IByteStream bsRecv = SendToDevice(id, bs);

                if(bsRecv == null)
                {
                    throw new Exception("无数据回应!");
                }
                byte[] dataReceive = bsRecv.GetBytes();
                if(dataReceive.Length > 2 && dataReceive[1] == 0x90)
                {
                    throw new Exception(string.Format("{0}!", (eControlErrorType)dataReceive[2]));
                }
                if(dataReceive.Length < 6)
                {
                    throw new Exception("回应数据长度不合法！");
                }
            }
            catch(Exception ex)
            {
                throw new Exception(string.Format("[{0}]标样核查间隔设置：{1}", name, ex.Message));
            }
        }

        /// <summary>
        /// 标定间隔设置
        /// </summary>
        /// <param name="stamp">间隔</param>
        public void SetCalibrateStamp(int stamp)
        {
            try
            {
                List<byte> dataSend = new List<byte>();
                dataSend.AddRange(new byte[] { byte.Parse(Addr), 0x10, 0x12, 0x00, 0x00, 0x02, 0x04, 0x00, 0x17 });
                dataSend.AddRange(DataConverter.GetInstance().GetBytes(stamp));
                IByteStream bs = new ByteArrayWrap(dataSend.ToArray());
                IByteStream bsRecv = SendToDevice(id, bs);

                if(bsRecv == null)
                {
                    throw new Exception("无数据回应!");
                }
                byte[] dataReceive = bsRecv.GetBytes();
                if(dataReceive.Length > 2 && dataReceive[1] == 0x90)
                {
                    throw new Exception(string.Format("{0}!", (eControlErrorType)dataReceive[2]));
                }
                if(dataReceive.Length < 6)
                {
                    throw new Exception("回应数据长度不合法！");
                }
            }
            catch(Exception ex)
            {
                throw new Exception(string.Format("[{0}]标定间隔设置：{1}", name, ex.Message));
            }
        }

        /// <summary>
        /// 消解温度设置
        /// </summary>
        /// <param name="temp">温度</param>
        public void SetDigestTemp(int temp)
        {
            try
            {
                List<byte> dataSend = new List<byte>();
                dataSend.AddRange(new byte[] { byte.Parse(Addr), 0x10, 0x12, 0x00, 0x00, 0x02, 0x04, 0x00, 0x1A });
                dataSend.AddRange(DataConverter.GetInstance().GetBytes(temp));
                IByteStream bs = new ByteArrayWrap(dataSend.ToArray());
                IByteStream bsRecv = SendToDevice(id, bs);

                if(bsRecv == null)
                {
                    throw new Exception("无数据回应!");
                }
                byte[] dataReceive = bsRecv.GetBytes();
                if(dataReceive.Length > 2 && dataReceive[1] == 0x90)
                {
                    throw new Exception(string.Format("{0}!", (eControlErrorType)dataReceive[2]));
                }
                if(dataReceive.Length < 6)
                {
                    throw new Exception("回应数据长度不合法！");
                }
            }
            catch(Exception ex)
            {
                throw new Exception(string.Format("[{0}]消解温度设置：{1}", name, ex.Message));
            }
        }

        /// <summary>
        /// 消解时长设置
        /// </summary>
        /// <param name="time">时长</param>
        public void SetDigestTime(int time)
        {
            try
            {
                List<byte> dataSend = new List<byte>();
                dataSend.AddRange(new byte[] { byte.Parse(Addr), 0x10, 0x12, 0x00, 0x00, 0x02, 0x04, 0x00, 0x1B });
                dataSend.AddRange(DataConverter.GetInstance().GetBytes(time));
                IByteStream bs = new ByteArrayWrap(dataSend.ToArray());
                IByteStream bsRecv = SendToDevice(id, bs);

                if(bsRecv == null)
                {
                    throw new Exception("无数据回应!");
                }
                byte[] dataReceive = bsRecv.GetBytes();
                if(dataReceive.Length > 2 && dataReceive[1] == 0x90)
                {
                    throw new Exception(string.Format("{0}!", (eControlErrorType)dataReceive[2]));
                }
                if(dataReceive.Length < 6)
                {
                    throw new Exception("回应数据长度不合法！");
                }
            }
            catch(Exception ex)
            {
                throw new Exception(string.Format("[{0}]消解时长设置：{1}", name, ex.Message));
            }
        }

        /// <summary>
        /// 修改当前量程
        /// </summary>
        /// <param name="eRange">量程</param>
        public void SetCurrentRange(eDeviceRangeModel eRange)
        {
            try
            {
                List<byte> dataSend = new List<byte>();
                dataSend.AddRange(new byte[] { byte.Parse(Addr), 0x10, 0x12, 0x00, 0x00, 0x02, 0x04, 0x00, 0x1E });
                dataSend.AddRange(DataConverter.GetInstance().GetBytes((int)eRange));
                IByteStream bs = new ByteArrayWrap(dataSend.ToArray());
                IByteStream bsRecv = SendToDevice(id, bs);

                if(bsRecv == null)
                {
                    throw new Exception("无数据回应!");
                }
                byte[] dataReceive = bsRecv.GetBytes();
                if(dataReceive.Length > 2 && dataReceive[1] == 0x90)
                {
                    throw new Exception(string.Format("{0}!", (eControlErrorType)dataReceive[2]));
                }
                if(dataReceive.Length < 6)
                {
                    throw new Exception("回应数据长度不合法！");
                }
            }
            catch(Exception ex)
            {
                throw new Exception(string.Format("[{0}]修改当前量程：{1}", name, ex.Message));
            }
        }

        /// <summary>
        /// 特殊水样测量流程
        /// </summary>
        /// <param name="specialSampleParam">测量参数</param>
        public void SpecialOperSet(int specialSampleParam)
        {
            try
            {
                List<byte> dataSend = new List<byte>();
                dataSend.AddRange(new byte[] { byte.Parse(Addr), 0x10, 0x12, 0x00, 0x00, 0x02, 0x04, 0x00, 0x1F });
                dataSend.AddRange(DataConverter.GetInstance().GetBytes(specialSampleParam));
                IByteStream bs = new ByteArrayWrap(dataSend.ToArray());
                IByteStream bsRecv = SendToDevice(id, bs);

                if(bsRecv == null)
                {
                    throw new Exception("无数据回应!");
                }
                byte[] dataReceive = bsRecv.GetBytes();
                if(dataReceive.Length > 2 && dataReceive[1] == 0x90)
                {
                    throw new Exception(string.Format("{0}!", (eControlErrorType)dataReceive[2]));
                }
                if(dataReceive.Length < 6)
                {
                    throw new Exception("回应数据长度不合法！");
                }
            }
            catch(Exception ex)
            {
                throw new Exception(string.Format("[{0}]修改当前量程：{1}", name, ex.Message));
            }
        }

        /// <summary>
        /// 点位控制
        /// </summary>
        /// <param name="startIndex">起始地址</param>
        /// <param name="param">参数</param>
        public void WriteSwitchValueToDevice(short startIndex, short param)
        {
            List<byte> dataSend = new List<byte>();
            dataSend.AddRange(new byte[] { byte.Parse(Addr), 0x10, 0x12, 0x00, 0x00, 0x02, 0x04, 0x00, (byte)startIndex, 0x00, (byte)param });
            IByteStream bs = new ByteArrayWrap(dataSend.ToArray());
            IByteStream bsRecv = SendToDevice(this.id, bs);
            if(bsRecv == null)
            {
                throw new Exception("无数据回应!");
            }
            byte[] dataReceive = bsRecv.GetBytes();

            if(dataReceive.Length > 2 && dataReceive[1] == 0x90)
            {
                throw new Exception(string.Format("{0}!", (eControlErrorType)dataReceive[2]));
            }
            if(dataReceive.Length < 6)
            {
                throw new Exception("回应数据长度不合法！");
            }
        }

        /// <summary>
        /// 器件自检
        /// </summary>
        /// <param name="eSIA3900DeviceCheckType"></param>
        public void WriteDeviceCheck(eSIA3900DeviceCheckType eSIA3900DeviceCheckType)
        {
            try
            {
                List<byte> dataSend = new List<byte>();
                dataSend.AddRange(new byte[] { byte.Parse(Addr), 0x10, 0x12, 0x00, 0x00, 0x02, 0x04, 0x00, 0x2C });
                dataSend.AddRange(DataConverter.GetInstance().GetBytes((int)eSIA3900DeviceCheckType));
                IByteStream bs = new ByteArrayWrap(dataSend.ToArray());
                IByteStream bsRecv = SendToDevice(id, bs);

                if(bsRecv == null)
                {
                    throw new Exception("无数据回应!");
                }
                byte[] dataReceive = bsRecv.GetBytes();
                if(dataReceive.Length > 2 && dataReceive[1] == 0x90)
                {
                    throw new Exception(string.Format("{0}!", (eControlErrorType)dataReceive[2]));
                }
                if(dataReceive.Length < 6)
                {
                    throw new Exception("回应数据长度不合法！");
                }
            }
            catch(Exception ex)
            {
                throw new Exception(string.Format("[{0}]器件自检：{1}", name, ex.Message));
            }
        }

        #endregion

        #region 不定参数读写

        #region 写

        /// <summary>
        /// 下发Float类型参数到设备
        /// </summary>
        /// <param name="param"></param>
        public void WriteFloatParamToDevice(short startIndex, float param)
        {
            List<byte> dataSend = new List<byte> { byte.Parse(this.Addr), 0x10, 0x14, (byte)startIndex, 0x00, 0x02, 0x04 };
            byte[] bytes = DataConverter.GetInstance().GetBytes(param);
            byte[] newBytes = new byte[] { bytes[2], bytes[3], bytes[0], bytes[1] };
            dataSend.AddRange(newBytes);
            IByteStream bs = new ByteArrayWrap(dataSend.ToArray());
            IByteStream bsRecv = SendToDevice(id, bs);
            if(bsRecv == null)
            {
                throw new Exception("无数据回应!");
            }
            byte[] dataReceive = bsRecv.GetBytes();

            if(dataReceive.Length > 2 && dataReceive[1] == 0x90)
            {
                throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
            }
            if(dataReceive.Length < 6)
            {
                throw new Exception("回应数据长度不合法！");
            }
        }

        /// <summary>
        /// 下发Int16类型参数到设备
        /// </summary>
        /// <param name="param"></param>
        public void WriteInt16ParamToDevice(short startIndex, short param)
        {
            List<byte> dataSend = new List<byte> { byte.Parse(this.Addr), 0x10, 0x14, (byte)startIndex, 0x00, 0x01, 0x02 };
            dataSend.AddRange(DataConverter.GetInstance().GetBytes(param));
            IByteStream bs = new ByteArrayWrap(dataSend.ToArray());
            IByteStream bsRecv = SendToDevice(id, bs);
            if(bsRecv == null)
            {
                throw new Exception("无数据回应!");
            }
            byte[] dataReceive = bsRecv.GetBytes();

            if(dataReceive.Length > 2 && dataReceive[1] == 0x90)
            {
                throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
            }
            if(dataReceive.Length < 6)
            {
                throw new Exception("回应数据长度不合法！");
            }
        }

        #endregion

        #region 读

        /// <summary>
        /// 从设备上读取Float类型参数
        /// </summary>
        /// <param name="param"></param>
        public float ReadFloatParamFromDevice(short startIndex)
        {
            byte[] dataSend = { byte.Parse(this.Addr), 0x03, 0x14, (byte)startIndex, 0x00, 0x02 };
            IByteStream bs = new ByteArrayWrap(dataSend);
            IByteStream bsRecv = SendToDevice(id, bs);
            if(bsRecv == null)
            {
                throw new Exception("无数据回应!");
            }
            byte[] dataReceive = bsRecv.GetBytes();

            if(dataReceive.Length > 2 && dataReceive[1] == 0x83)
            {
                throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
            }
            if(dataReceive.Length < 5)
            {
                throw new Exception("回应数据长度不合法！");
            }
            if(dataReceive[2] != 0x04)
            {
                throw new Exception("回应数据错位！");
            }

            //   return (float)((float)((float)DataConverter.GetInstance().ToSingle(dataReceive, 3)));
            return DataConvertHelper.ParseFloatValue2143(dataReceive, 3);
        }

        /// <summary>
        /// 从设备上读取Int16类型参数
        /// </summary>
        /// <param name="param"></param>
        public int ReadInt16ParamFromDevice(short startIndex)
        {
            byte[] dataSend = { byte.Parse(this.Addr), 0x03, 0x14, (byte)startIndex, 0x00, 0x01 };
            IByteStream bs = new ByteArrayWrap(dataSend);
            IByteStream bsRecv = SendToDevice(id, bs);
            if(bsRecv == null)
            {
                throw new Exception("无数据回应!");
            }
            byte[] dataReceive = bsRecv.GetBytes();

            if(dataReceive.Length > 2 && dataReceive[1] == 0x83)
            {
                throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
            }
            if(dataReceive.Length < 5)
            {
                throw new Exception("回应数据长度不合法！");
            }

            if(dataReceive[2] != 0x02)
            {
                throw new Exception("回应数据错位！");
            }

            return DataConverter.GetInstance().ToInt32(dataReceive, 3);
        }

        #endregion

        #endregion

        #endregion

        #region IMeasureDeviceOperation

        public void StartOper(eMeasureDeviceOperType opType)
        {
            try
            {
                List<byte> dataSend = new List<byte>();
                dataSend.AddRange(new byte[] { byte.Parse(Addr), 0x10, 0x12, 0x00 });
                switch(opType)
                {
                    case eMeasureDeviceOperType.Measure:
                        dataSend.AddRange(new byte[] { 0x00, 0x01, 0x02, 0x00, 0x01 });
                        break;

                    case eMeasureDeviceOperType.Check:
                        dataSend.AddRange(new byte[] { 0x00, 0x01, 0x02, 0x00, 0x02 });
                        break;

                    case eMeasureDeviceOperType.BlankCheck:
                        dataSend.AddRange(new byte[] { 0x00, 0x01, 0x02, 0x00, 0x03 });
                        break;

                    case eMeasureDeviceOperType.RangeCheck:
                        dataSend.AddRange(new byte[] { 0x00, 0x01, 0x02, 0x00, 0x04 });
                        break;

                    case eMeasureDeviceOperType.BlankTest:
                        dataSend.AddRange(new byte[] { 0x00, 0x01, 0x02, 0x00, 0x05 });
                        break;

                    case eMeasureDeviceOperType.Parallel:
                        dataSend.AddRange(new byte[] { 0x00, 0x01, 0x02, 0x00, 0x06 });
                        break;

                    case eMeasureDeviceOperType.Add:
                        dataSend.AddRange(new byte[] { 0x00, 0x01, 0x02, 0x00, 0x07 });
                        break;

                    case eMeasureDeviceOperType.BlankCalibration:
                        dataSend.AddRange(new byte[] { 0x00, 0x01, 0x02, 0x00, 0x08 });
                        break;

                    case eMeasureDeviceOperType.SampleCalibration:
                        dataSend.AddRange(new byte[] { 0x00, 0x01, 0x02, 0x00, 0x09 });
                        break;

                    case eMeasureDeviceOperType.Initialize:
                        dataSend.AddRange(new byte[] { 0x00, 0x01, 0x02, 0x00, 0x0A });
                        break;

                    case eMeasureDeviceOperType.StopMeasture:
                        dataSend.AddRange(new byte[] { 0x00, 0x01, 0x02, 0x00, 0x0B });
                        break;

                    case eMeasureDeviceOperType.ReStart:
                        dataSend.AddRange(new byte[] { 0x00, 0x01, 0x02, 0x00, 0x0C });
                        break;

                    case eMeasureDeviceOperType.Demarcate:
                        dataSend.AddRange(new byte[] { 0x00, 0x01, 0x02, 0x00, 0x13 });
                        break;

                    case eMeasureDeviceOperType.SignalTransformate:
                        dataSend.AddRange(new byte[] { 0x00, 0x01, 0x02, 0x00, 0x14 });
                        break;

                    case eMeasureDeviceOperType.LineFlush:
                        dataSend.AddRange(new byte[] { 0x00, 0x01, 0x02, 0x00, 0x15 });
                        break;

                    case eMeasureDeviceOperType.ReagentIntroduct:
                        dataSend.AddRange(new byte[] { 0x00, 0x01, 0x02, 0x00, 0x16 });
                        break;

                    case eMeasureDeviceOperType.EquipCalibrate:
                        dataSend.AddRange(new byte[] { 0x00, 0x01, 0x02, 0x00, 0x18 });
                        break;

                    case eMeasureDeviceOperType.RangeCalibration:
                        dataSend.AddRange(new byte[] { 0x00, 0x01, 0x02, 0x00, 0x19 });
                        break;

                    case eMeasureDeviceOperType.SelfCheck:
                        dataSend.AddRange(new byte[] { 0x00, 0x01, 0x02, 0x00, 0x1C });
                        break;

                    case eMeasureDeviceOperType.TraceSolution:
                        dataSend.AddRange(new byte[] { 0x00, 0x01, 0x02, 0x00, 0x1D });
                        break;

                    case eMeasureDeviceOperType.Empty:
                        dataSend.AddRange(new byte[] { 0x00, 0x01, 0x02, 0x00, 0x25 });
                        break;

                    case eMeasureDeviceOperType.Wash:
                        dataSend.AddRange(new byte[] { 0x00, 0x01, 0x02, 0x00, 0x26 });
                        break;

                    case eMeasureDeviceOperType.Maintain:
                        dataSend.AddRange(new byte[] { 0x00, 0x01, 0x02, 0x00, 0x27 });
                        break;

                    case eMeasureDeviceOperType.Linearity:
                        dataSend.AddRange(new byte[] { 0x00, 0x01, 0x02, 0x00, 0x28 });
                        break;

                    case eMeasureDeviceOperType.ArbitraryConCheck:
                        dataSend.AddRange(new byte[] { 0x00, 0x01, 0x02, 0x00, 0x2B });
                        break;

                    default:
                        throw new Exception($"[{name}]被控操作失败，操作类型[{EnumOperate.GetEnumDesc(opType)}未实现！]");
                }

                IByteStream bs = new ByteArrayWrap(dataSend.ToArray());
                IByteStream bsRecv = SendToDevice(id, bs);
                if(bsRecv == null)
                {
                    throw new Exception("无数据回应！");
                }

                byte[] dataRecv = bsRecv.GetBytes();
                if(dataRecv.Length > 2 && dataRecv[1] == 0x90)
                {
                    throw new Exception($"{(eControlErrorType)dataRecv[2]}!");
                }

                if(dataRecv.Length < 6)
                {
                    throw new Exception("回应数据长度不合法！");
                }
            }
            catch(Exception e)
            {
                throw new Exception($"触发{this.name}执行{opType}流程出错：{e.Message}");
            }
        }

        #endregion

        #region IDeviceNotify

        /// <summary>
        /// 设备状态变化事件
        /// </summary>
        public event EventHandlerDefine.StateChangedEventHandler OnStateChanged;


        #endregion

        #region IDeviceRangeSet

        /// <summary>
        /// 设置量程
        /// </summary>
        /// <param name="range"></param>
        public void SetDeviceRange(eDeviceRangeModel deviceRangeModel)
        {
            WriteInt16ParamToDevice(0x1A, (byte)deviceRangeModel);
        }

        #endregion

        #region ISIA3900DeviceParamTransfer 成员

        public SIA3900DeviceKeyParams GetDeviceKeyParame()
        {
            return DeviceKeyParams;
        }

        public void SetDigestionTime(int digestionTime)
        {
            SetDigestTime(digestionTime);
        }

        public void SetDigestionTemp(int digestionTemp)
        {
            SetDigestTemp(digestionTemp);
        }

        /// <summary>
        /// 设置仪表测量间隔
        /// </summary>
        /// <returns></returns>
        public void SetLiquidMeasureInterval(int measureInterval)
        {
            SetMeasureStamp(measureInterval);
        }

        #endregion

        #region IQCDataGet

        public double GetDevQCData(eMeasureDeviceOperType dataType)
        {
            float dataStr = float.NaN;
            switch(dataType)
            {
                case eMeasureDeviceOperType.BlankCheck:
                    dataStr = MeasureParams.ZeroCheckValue;
                    break;
                case eMeasureDeviceOperType.RangeCheck:
                    dataStr = MeasureParams.SpanCheckValue;
                    break;
                case eMeasureDeviceOperType.Check:
                    dataStr = MeasureParams.GuideSampleValue;
                    break;
                case eMeasureDeviceOperType.Parallel:
                    dataStr = MeasureParams.DuplicateSampleValue;
                    break;
                case eMeasureDeviceOperType.Add:
                    dataStr = MeasureParams.MarkRecoveryValue;
                    break;
                case eMeasureDeviceOperType.BlankTest:
                    dataStr = MeasureParams.BlankSampleValue;
                    break;
                case eMeasureDeviceOperType.Linearity:
                    dataStr = MeasureParams.LinearityCheckSampleValue;
                    break;
                case eMeasureDeviceOperType.ArbitraryConCheck:
                    dataStr = MeasureParams.ArbitraryConCheckSampleValue;
                    break;
            }

            return dataStr;
        }

        public DateTime GetDevQCTime(eMeasureDeviceOperType dataType)
        {
            var time = DateTime.MinValue;
            switch(dataType)
            {

                case eMeasureDeviceOperType.BlankCheck:
                    time = MeasureParams.ZeroCheckDataTime;
                    break;
                case eMeasureDeviceOperType.RangeCheck:
                    time = MeasureParams.SpanCheckDataTime;
                    break;
                case eMeasureDeviceOperType.Check:
                    time = MeasureParams.GuideSampleDataTime;
                    break;
                case eMeasureDeviceOperType.Parallel:
                    time = MeasureParams.DuplicateSampleDataTime;
                    break;
                case eMeasureDeviceOperType.Add:
                    time = MeasureParams.MarkRecoveryDataTime;
                    break;
                case eMeasureDeviceOperType.BlankTest:
                    time = MeasureParams.BlankSampleDataTime;
                    break;
                case eMeasureDeviceOperType.Linearity:
                    time = MeasureParams.LinearityCheckDataTime;
                    break;
                case eMeasureDeviceOperType.ArbitraryConCheck:
                    time = MeasureParams.ArbitraryConCheckDataTime;
                    break;
            }

            return time;
        }

        #endregion

        #region IDeviceKeyParams

        public void SaveKeyParamsToDb(DateTime dataTime)
        {
            DeviceKeyParams.SaveToDb(GetDevTargetPolNode().id, dataTime);
        }

        public void ShowSelectParams(string sourceId, DateTime dataTime)
        {
            var param = SIA3900DeviceKeyParams.ReadSIA3900KeyParamsFormDb(sourceId, dataTime);
            if(param != null)
            {
                new FrmSIA3900KeyParams(param, this, dataTime).ShowDialog();
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 解析报警
        /// </summary>
        /// <param name="dataReceive"></param>
        private void ParseDeviceAlarm(byte[] dataReceive, int startIndex)
        {
            if(dataReceive.Length < 32)
            {
                throw new Exception("读取设备报警数据不完整！");
            }

            ///遍历索引Index开始的32个字节，16个寄存器
            for(int i = 0; i < 16; i++)
            {
                int alarmInfo = DataConverter.GetInstance().ToInt32(dataReceive, startIndex + 2 * i);

                for(int j = 0; j <= 16; j++)
                {
                    int alarmCode = j + i * 16;

                    if(alarmCode > 106)
                    {
                        return;
                    }

                    //  判断最低位是否为1
                    if((alarmInfo & 0x0001) == 0x0001)
                    {
                        AlarmManager.GetInstance().AddAlarm(AlarmSourceId, alarmCode.ToString());
                    }
                    else
                    {
                        AlarmManager.GetInstance().RemoveAlarm(AlarmSourceId, alarmCode.ToString());
                    }

                    // 移位
                    alarmInfo >>= 1;
                }
            }
        }

        #endregion
    }
}