{"RootPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\WMS3900.Equipment", "ProjectFileName": "Fpi.WMS3900.Equipment.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "Common\\CommonFunctionHelper.cs"}, {"SourceFile": "Common\\DataConvertHelper.cs"}, {"SourceFile": "Common\\CustomAttribute\\VisibleAttribute.cs"}, {"SourceFile": "Common\\Enums.cs"}, {"SourceFile": "Common\\DeviceLogInfo.cs"}, {"SourceFile": "Common\\UI\\UC\\UC_OneNodeValue.cs"}, {"SourceFile": "Common\\UI\\UC\\UC_OneNodeValue.Designer.cs"}, {"SourceFile": "Common\\UI\\UC_DeviceHistoryLogQuery.cs"}, {"SourceFile": "Common\\UI\\UC_DeviceHistoryLogQuery.Designer.cs"}, {"SourceFile": "Common\\UI\\UC\\UC_OneParamData.cs"}, {"SourceFile": "Common\\UI\\UC\\UC_OneParamData.Designer.cs"}, {"SourceFile": "Common\\UI\\UC\\UC_OneAlarmState.cs"}, {"SourceFile": "Common\\UI\\UC\\UC_OneAlarmState.Designer.cs"}, {"SourceFile": "Common\\UI\\UC_DeviceAllAlarm.cs"}, {"SourceFile": "Common\\UI\\UC_DeviceAllAlarm.Designer.cs"}, {"SourceFile": "Common\\UI\\UC_DeviceDataChannels.cs"}, {"SourceFile": "Common\\UI\\UC_DeviceDataChannels.Designer.cs"}, {"SourceFile": "Common\\UI\\UC_DeviceParamData.cs"}, {"SourceFile": "Common\\UI\\UC_DeviceParamData.Designer.cs"}, {"SourceFile": "Config\\Enums.cs"}, {"SourceFile": "Config\\ExterEquipConfig\\ExterEquipConfigManager.cs"}, {"SourceFile": "Config\\ExterEquipConfig\\SubModel\\CameraSelectConfig.cs"}, {"SourceFile": "Config\\ExterEquipConfig\\SubModel\\EntranceSelectConfig.cs"}, {"SourceFile": "Config\\ExterEquipConfig\\SubModel\\PollutionDataSaveConfig.cs"}, {"SourceFile": "Config\\ExterEquipConfig\\SubModel\\StationEnvConfig.cs"}, {"SourceFile": "Config\\ExterEquipConfig\\SubModel\\WaterDistributeConfig.cs"}, {"SourceFile": "Config\\GlobalData\\GlobalDataCache.cs"}, {"SourceFile": "Config\\GlobalData\\ModuleState\\StationModuleState.cs"}, {"SourceFile": "Config\\GlobalData\\ModuleState\\WaterPretreatmentModuleState.cs"}, {"SourceFile": "Config\\GlobalData\\ModuleState\\WaterDistributionModuleState.cs"}, {"SourceFile": "Config\\GlobalData\\ModuleState\\WaterCollectionModuleState.cs"}, {"SourceFile": "Config\\ExterEquipConfig\\SubModel\\AirConditioningConfig.cs"}, {"SourceFile": "Config\\ExterEquipConfig\\SubModel\\DeviceSelectConfig.cs"}, {"SourceFile": "Config\\ExterEquipConfig\\SubModel\\AirVentilatorConfig.cs"}, {"SourceFile": "Config\\ExterEquipConfig\\SubModel\\AlarmLimitConfig.cs"}, {"SourceFile": "Config\\ExterEquipConfig\\SubModel\\DehumidifierConfig.cs"}, {"SourceFile": "Config\\ExterEquipConfig\\SubModel\\WaterPretreatmentConfig.cs"}, {"SourceFile": "Config\\ExterEquipConfig\\SubModel\\WaterCollectionConfig.cs"}, {"SourceFile": "Config\\ExterEquipConfig\\SubModel\\WeightConfig.cs"}, {"SourceFile": "Config\\ExterEquipConfig\\SubModel\\PduConfig.cs"}, {"SourceFile": "Config\\GlobalData\\StationStateInfo.cs"}, {"SourceFile": "Config\\SystemAlarm\\SystemAlarmHelper.cs"}, {"SourceFile": "Equipment\\ExterEquips\\DLEquip\\UI\\UC\\UC_OneTempNodeValue.cs"}, {"SourceFile": "Equipment\\ExterEquips\\DLEquip\\UI\\UC\\UC_OneTempNodeValue.Designer.cs"}, {"SourceFile": "Equipment\\ExterEquips\\DLEquip\\UI\\UC_DL10BDeviceState.cs"}, {"SourceFile": "Equipment\\ExterEquips\\DLEquip\\UI\\UC_DL10BDeviceState.Designer.cs"}, {"SourceFile": "Equipment\\ExterEquips\\DSEquip\\DS6CNZDESEquipment.cs"}, {"SourceFile": "Equipment\\ExterEquips\\DSEquip\\Params\\DS6CNZDESParam.cs"}, {"SourceFile": "Equipment\\ExterEquips\\DSEquip\\UI\\UC_DS6CNZDESDeviceState.cs"}, {"SourceFile": "Equipment\\ExterEquips\\DSEquip\\UI\\UC_DS6CNZDESDeviceState.Designer.cs"}, {"SourceFile": "Equipment\\ExterEquips\\JDRKEquip\\JDRKRSEquip.cs"}, {"SourceFile": "Equipment\\ExterEquips\\JDRKEquip\\Params\\JDRKRSParam.cs"}, {"SourceFile": "Equipment\\ExterEquips\\JDRKEquip\\UI\\UC_JDRKRSDeviceState.cs"}, {"SourceFile": "Equipment\\ExterEquips\\JDRKEquip\\UI\\UC_JDRKRSDeviceState.Designer.cs"}, {"SourceFile": "Equipment\\ExterEquips\\JiuBoEquip\\JiuBoWL1A2Equipment.cs"}, {"SourceFile": "Equipment\\ExterEquips\\LJEquip\\UI\\UC\\UC_OneRoadWeightParam.cs"}, {"SourceFile": "Equipment\\ExterEquips\\LJEquip\\UI\\UC\\UC_OneRoadWeightParam.Designer.cs"}, {"SourceFile": "Equipment\\ExterEquips\\LXEquip\\LXAM125Equipment.cs"}, {"SourceFile": "Equipment\\ExterEquips\\LXEquip\\Params\\AM125Param.cs"}, {"SourceFile": "Equipment\\ExterEquips\\DLEquip\\DL10BEquipment.cs"}, {"SourceFile": "Equipment\\ExterEquips\\LJEquip\\LJWD200Equipment.cs"}, {"SourceFile": "Equipment\\ExterEquips\\LJEquip\\Params\\LJWD200WeightParam.cs"}, {"SourceFile": "Equipment\\ExterEquips\\LXEquip\\UI\\UC_LXAM125DeviceState.cs"}, {"SourceFile": "Equipment\\ExterEquips\\LXEquip\\UI\\UC_LXAM125DeviceState.Designer.cs"}, {"SourceFile": "Equipment\\ExterEquips\\SHEquip\\SHSWQ7300Equip.cs"}, {"SourceFile": "Equipment\\ExterEquips\\TPRSEquip\\UI\\UC\\UC_OneRoadPduParam.cs"}, {"SourceFile": "Equipment\\ExterEquips\\TPRSEquip\\UI\\UC\\UC_OneRoadPduParam.Designer.cs"}, {"SourceFile": "Equipment\\ExterEquips\\YstEquip\\Params\\YstEA900Param.cs"}, {"SourceFile": "Equipment\\ExterEquips\\YstEquip\\UI\\UC_YstEA900DeviceState.cs"}, {"SourceFile": "Equipment\\ExterEquips\\YstEquip\\UI\\UC_YstEA900DeviceState.Designer.cs"}, {"SourceFile": "Equipment\\ExterEquips\\ZTEquip\\Config\\NB2EnumDefine.cs"}, {"SourceFile": "Equipment\\ExterEquips\\ZTEquip\\UI\\UC_ZTNB2LEDeviceState.cs"}, {"SourceFile": "Equipment\\ExterEquips\\ZTEquip\\UI\\UC_ZTNB2LEDeviceState.Designer.cs"}, {"SourceFile": "Equipment\\ExterEquips\\ZTEquip\\ZTNB2LEEquipment.cs"}, {"SourceFile": "Equipment\\ExterEquips\\ZTEquip\\Params\\NB2Param.cs"}, {"SourceFile": "Equipment\\ExterEquips\\TPRSEquip\\Params\\PduElectricParam.cs"}, {"SourceFile": "Equipment\\ExterEquips\\TPRSEquip\\TPRSXC3007Equipment.cs"}, {"SourceFile": "Equipment\\ExterEquips\\SHEquip\\SHTur300Equip.cs"}, {"SourceFile": "Equipment\\ExterEquips\\ZZEquip\\ZZWL300Equip.cs"}, {"SourceFile": "Equipment\\FPIEquips\\QCD3900\\Config\\QCD3900EnumDefine.cs"}, {"SourceFile": "Equipment\\FPIEquips\\QCD3900\\Params\\QCD3900LogArea.cs"}, {"SourceFile": "Equipment\\FPIEquips\\QCD3900\\Params\\QCD3900MeasureParam.cs"}, {"SourceFile": "Equipment\\FPIEquips\\QCD3900\\Params\\QCD3900ElementLifeInfo.cs"}, {"SourceFile": "Equipment\\FPIEquips\\QCD3900\\UI\\UC\\UC_OneElementLifeInfo.cs"}, {"SourceFile": "Equipment\\FPIEquips\\QCD3900\\UI\\UC\\UC_OneElementLifeInfo.Designer.cs"}, {"SourceFile": "Equipment\\FPIEquips\\QCD3900\\UI\\UC\\UC_OneQCD3900ElementControl.cs"}, {"SourceFile": "Equipment\\FPIEquips\\QCD3900\\UI\\UC\\UC_OneQCD3900ElementControl.Designer.cs"}, {"SourceFile": "Equipment\\FPIEquips\\QCD3900\\UI\\UC\\UC_OneQCD3900LiquidInfo.cs"}, {"SourceFile": "Equipment\\FPIEquips\\QCD3900\\UI\\UC\\UC_OneQCD3900LiquidInfo.Designer.cs"}, {"SourceFile": "Equipment\\FPIEquips\\QCD3900\\UI\\UC_QCD3900CompInfo.cs"}, {"SourceFile": "Equipment\\FPIEquips\\QCD3900\\UI\\UC_QCD3900CompInfo.Designer.cs"}, {"SourceFile": "Equipment\\FPIEquips\\QCD3900\\UI\\UC_QCD3900FlowControl.cs"}, {"SourceFile": "Equipment\\FPIEquips\\QCD3900\\UI\\UC_QCD3900FlowControl.Designer.cs"}, {"SourceFile": "Equipment\\FPIEquips\\QCD3900\\UI\\UC_QCD3900Maintain.cs"}, {"SourceFile": "Equipment\\FPIEquips\\QCD3900\\UI\\UC_QCD3900Maintain.Designer.cs"}, {"SourceFile": "Equipment\\FPIEquips\\QCD3900\\UI\\UC_QCD3900ElementControl.cs"}, {"SourceFile": "Equipment\\FPIEquips\\QCD3900\\UI\\UC_QCD3900ElementControl.Designer.cs"}, {"SourceFile": "Equipment\\FPIEquips\\QCD3900\\UI\\UC_QCD3900MeasureParam.cs"}, {"SourceFile": "Equipment\\FPIEquips\\QCD3900\\UI\\UC_QCD3900MeasureParam.Designer.cs"}, {"SourceFile": "Equipment\\FPIEquips\\QCD3900\\UI\\UC_QCD3900LiquidInfo.cs"}, {"SourceFile": "Equipment\\FPIEquips\\QCD3900\\UI\\UC_QCD3900LiquidInfo.Designer.cs"}, {"SourceFile": "Equipment\\FPIEquips\\QCD3900\\UI\\UC_QCD3900Param.cs"}, {"SourceFile": "Equipment\\FPIEquips\\QCD3900\\UI\\UC_QCD3900Param.Designer.cs"}, {"SourceFile": "Equipment\\FPIEquips\\SIA3900\\Config\\SIA3900EnumDefine.cs"}, {"SourceFile": "Equipment\\FPIEquips\\SIA3900\\Params\\SIA3900CalibrateCoefficient.cs"}, {"SourceFile": "Equipment\\FPIEquips\\SIA3900\\Params\\SIA3900CalibrateCoefficientDetail.cs"}, {"SourceFile": "Equipment\\FPIEquips\\SIA3900\\Params\\SIA3900DeviceKeyParams.cs"}, {"SourceFile": "Equipment\\FPIEquips\\SIA3900\\Params\\SIA3900DiagnosisRecords.cs"}, {"SourceFile": "Equipment\\FPIEquips\\SIA3900\\Params\\SIA3900HistoryDate.cs"}, {"SourceFile": "Equipment\\FPIEquips\\SIA3900\\Params\\SIA3900HomePageStatus.cs"}, {"SourceFile": "Equipment\\FPIEquips\\SIA3900\\Params\\SIA3900LifeMonitor.cs"}, {"SourceFile": "Equipment\\FPIEquips\\SIA3900\\Params\\SIA3900LogArea.cs"}, {"SourceFile": "Equipment\\FPIEquips\\SIA3900\\Params\\SIA3900MeasureParam.cs"}, {"SourceFile": "Equipment\\FPIEquips\\SIA3900\\Params\\SIA3900ReagentMaintenance.cs"}, {"SourceFile": "Equipment\\FPIEquips\\SIA3900\\Params\\SIA3900StateAlarm.cs"}, {"SourceFile": "Equipment\\FPIEquips\\SIA3900\\SIA3900Equipment.cs"}, {"SourceFile": "Equipment\\FPIEquips\\SIA3900\\UI\\FrmSIA3900CalibrateCoefficientData.cs"}, {"SourceFile": "Equipment\\FPIEquips\\SIA3900\\UI\\FrmSIA3900CalibrateCoefficientData.Designer.cs"}, {"SourceFile": "Equipment\\FPIEquips\\SIA3900\\UI\\FrmSIA3900KeyParams.cs"}, {"SourceFile": "Equipment\\FPIEquips\\SIA3900\\UI\\FrmSIA3900KeyParams.Designer.cs"}, {"SourceFile": "Equipment\\FPIEquips\\SIA3900\\UI\\UC\\UC_OneSIA3900ReagentInfo.cs"}, {"SourceFile": "Equipment\\FPIEquips\\SIA3900\\UI\\UC\\UC_OneSIA3900ReagentInfo.Designer.cs"}, {"SourceFile": "Equipment\\FPIEquips\\SIA3900\\UI\\UC\\UC_SIA3900CheckFluidInfo.cs"}, {"SourceFile": "Equipment\\FPIEquips\\SIA3900\\UI\\UC\\UC_SIA3900CheckFluidInfo.Designer.cs"}, {"SourceFile": "Equipment\\FPIEquips\\SIA3900\\UI\\UC\\UC_SIA3900DiagnosisQueryData.cs"}, {"SourceFile": "Equipment\\FPIEquips\\SIA3900\\UI\\UC\\UC_SIA3900DiagnosisQueryData.Designer.cs"}, {"SourceFile": "Equipment\\FPIEquips\\SIA3900\\UI\\UC_SIA3900CalibrateCoefficientDetail.cs"}, {"SourceFile": "Equipment\\FPIEquips\\SIA3900\\UI\\UC_SIA3900CalibrateCoefficientDetail.Designer.cs"}, {"SourceFile": "Equipment\\FPIEquips\\SIA3900\\UI\\UC_SIA3900DeviceLifeMonitor.cs"}, {"SourceFile": "Equipment\\FPIEquips\\SIA3900\\UI\\UC_SIA3900DeviceLifeMonitor.Designer.cs"}, {"SourceFile": "Equipment\\FPIEquips\\SIA3900\\UI\\UC_SIA3900DeviceOperControl.cs"}, {"SourceFile": "Equipment\\FPIEquips\\SIA3900\\UI\\UC_SIA3900DeviceOperControl.Designer.cs"}, {"SourceFile": "Equipment\\FPIEquips\\SIA3900\\UI\\UC_SIA3900DeviceParamsSet.cs"}, {"SourceFile": "Equipment\\FPIEquips\\SIA3900\\UI\\UC_SIA3900DeviceParamsSet.Designer.cs"}, {"SourceFile": "Equipment\\FPIEquips\\SIA3900\\UI\\UC_SIA3900DeviceState.cs"}, {"SourceFile": "Equipment\\FPIEquips\\SIA3900\\UI\\UC_SIA3900DeviceState.Designer.cs"}, {"SourceFile": "Common\\UI\\UC_DeviceCurrentLogQuery.cs"}, {"SourceFile": "Common\\UI\\UC_DeviceCurrentLogQuery.Designer.cs"}, {"SourceFile": "Equipment\\FPIEquips\\SIA3900\\UI\\UC_SIA3900DiagnosisRecords.cs"}, {"SourceFile": "Equipment\\FPIEquips\\SIA3900\\UI\\UC_SIA3900DiagnosisRecords.Designer.cs"}, {"SourceFile": "Equipment\\FPIEquips\\SIA3900\\UI\\UC_SIA3900Param.cs"}, {"SourceFile": "Equipment\\FPIEquips\\SIA3900\\UI\\UC_SIA3900Param.Designer.cs"}, {"SourceFile": "Equipment\\FPIEquips\\SIA3900\\UI\\UC_SIA3900ReagentInfo.cs"}, {"SourceFile": "Equipment\\FPIEquips\\SIA3900\\UI\\UC_SIA3900ReagentInfo.Designer.cs"}, {"SourceFile": "Equipment\\FPIEquips\\WCS3900\\Config\\WCS3900EnumDefine.cs"}, {"SourceFile": "Equipment\\FPIEquips\\WCS3900\\Params\\MeasureParam\\WCS3900CommonParam.cs"}, {"SourceFile": "Equipment\\FPIEquips\\WCS3900\\Params\\MeasureParam\\WCS3900MeasureParamBase.cs"}, {"SourceFile": "Equipment\\FPIEquips\\WCS3900\\Params\\MeasureParam\\WCS3900TempMeasureParam.cs"}, {"SourceFile": "Equipment\\FPIEquips\\WCS3900\\Params\\MeasureParam\\WCS3900TurbMeasureParam.cs"}, {"SourceFile": "Equipment\\FPIEquips\\WCS3900\\Params\\MeasureParam\\WCS3900ConduMeasureParam.cs"}, {"SourceFile": "Equipment\\FPIEquips\\WCS3900\\Params\\MeasureParam\\WCS3900OxyMeasureParam.cs"}, {"SourceFile": "Equipment\\FPIEquips\\WCS3900\\Params\\MeasureParam\\WCS3900PHMeasureParam.cs"}, {"SourceFile": "Equipment\\FPIEquips\\WCS3900\\Params\\WCS3900ElementLifeInfo.cs"}, {"SourceFile": "Equipment\\FPIEquips\\WCS3900\\Params\\WCS3900LogArea.cs"}, {"SourceFile": "Equipment\\FPIEquips\\WCS3900\\Params\\WCS3900DeviceStateParam.cs"}, {"SourceFile": "Equipment\\FPIEquips\\WCS3900\\UI\\MeasureParam\\UC_WCS3900MeasureData.cs"}, {"SourceFile": "Equipment\\FPIEquips\\WCS3900\\UI\\MeasureParam\\UC_WCS3900MeasureData.Designer.cs"}, {"SourceFile": "Equipment\\FPIEquips\\WCS3900\\UI\\MeasureParam\\UC_WCS3900CheckData.cs"}, {"SourceFile": "Equipment\\FPIEquips\\WCS3900\\UI\\MeasureParam\\UC_WCS3900CheckData.Designer.cs"}, {"SourceFile": "Equipment\\FPIEquips\\WCS3900\\UI\\UC\\UC_OneWCS3900CheckParam.cs"}, {"SourceFile": "Equipment\\FPIEquips\\WCS3900\\UI\\UC\\UC_OneWCS3900CheckParam.Designer.cs"}, {"SourceFile": "Equipment\\FPIEquips\\WCS3900\\UI\\UC\\UC_OneWCS3900ElementState.cs"}, {"SourceFile": "Equipment\\FPIEquips\\WCS3900\\UI\\UC\\UC_OneWCS3900ElementState.Designer.cs"}, {"SourceFile": "Equipment\\FPIEquips\\WCS3900\\UI\\UC\\UC_OneWCS3900LiquidInfo.cs"}, {"SourceFile": "Equipment\\FPIEquips\\WCS3900\\UI\\UC\\UC_OneWCS3900LiquidInfo.Designer.cs"}, {"SourceFile": "Equipment\\FPIEquips\\WCS3900\\UI\\UC\\UC_OneWCS3900LevelState.cs"}, {"SourceFile": "Equipment\\FPIEquips\\WCS3900\\UI\\UC\\UC_OneWCS3900LevelState.Designer.cs"}, {"SourceFile": "Equipment\\FPIEquips\\WCS3900\\UI\\UC\\UC_OneWCS3900ElementControl.cs"}, {"SourceFile": "Equipment\\FPIEquips\\WCS3900\\UI\\UC\\UC_OneWCS3900ElementControl.Designer.cs"}, {"SourceFile": "Equipment\\FPIEquips\\WCS3900\\UI\\UC\\UC_OneWCS3900NodeParam.cs"}, {"SourceFile": "Equipment\\FPIEquips\\WCS3900\\UI\\UC\\UC_OneWCS3900NodeParam.Designer.cs"}, {"SourceFile": "Equipment\\FPIEquips\\WCS3900\\UI\\UC\\UC_OneWMS3900ReagentInfo.cs"}, {"SourceFile": "Equipment\\FPIEquips\\WCS3900\\UI\\UC\\UC_OneWMS3900ReagentInfo.Designer.cs"}, {"SourceFile": "Equipment\\FPIEquips\\WCS3900\\UI\\UC_WCS3900AllMeasureData.cs"}, {"SourceFile": "Equipment\\FPIEquips\\WCS3900\\UI\\UC_WCS3900AllMeasureData.Designer.cs"}, {"SourceFile": "Equipment\\FPIEquips\\WCS3900\\UI\\UC_WCS3900DeviceState.cs"}, {"SourceFile": "Equipment\\FPIEquips\\WCS3900\\UI\\UC_WCS3900DeviceState.Designer.cs"}, {"SourceFile": "Equipment\\FPIEquips\\WCS3900\\UI\\UC_WCS3900Maintain.cs"}, {"SourceFile": "Equipment\\FPIEquips\\WCS3900\\UI\\UC_WCS3900Maintain.Designer.cs"}, {"SourceFile": "Equipment\\FPIEquips\\WCS3900\\UI\\UC_WCS3900Param.cs"}, {"SourceFile": "Equipment\\FPIEquips\\WCS3900\\UI\\UC_WCS3900Param.Designer.cs"}, {"SourceFile": "Equipment\\FPIEquips\\WCS3900\\WCS3900Equip.cs"}, {"SourceFile": "Equipment\\HDSampleEquip\\ZSC-IXC\\HDZSC_IXCSampleEquip.cs"}, {"SourceFile": "Equipment\\HDSampleEquip\\ZSC-IXC\\Params\\SampleDoorLogHelper.cs"}, {"SourceFile": "Equipment\\HDSampleEquip\\ZSC-IXC\\Params\\HDZSCIXCParam.cs"}, {"SourceFile": "Equipment\\HDSampleEquip\\ZSC-IXC\\UI\\UC_SampleDoorLogQuery.cs"}, {"SourceFile": "Equipment\\HDSampleEquip\\ZSC-IXC\\UI\\UC_SampleDoorLogQuery.Designer.cs"}, {"SourceFile": "Equipment\\HDSampleEquip\\ZSC-IXC\\UI\\UC_HDZSCIXCDeviceOperControl.cs"}, {"SourceFile": "Equipment\\HDSampleEquip\\ZSC-IXC\\UI\\UC_HDZSCIXCDeviceOperControl.Designer.cs"}, {"SourceFile": "Equipment\\HDSampleEquip\\ZSC-IXC\\UI\\UC_HDZSCIXCParam.cs"}, {"SourceFile": "Equipment\\HDSampleEquip\\ZSC-IXC\\UI\\UC_HDZSCIXCParam.Designer.cs"}, {"SourceFile": "Equipment\\HolliasEquip\\PLC_Hollias_2012.cs"}, {"SourceFile": "Equipment\\HolliasEquip\\UI\\UC_Hollias2012Param.cs"}, {"SourceFile": "Equipment\\HolliasEquip\\UI\\UC_Hollias2012Param.designer.cs"}, {"SourceFile": "Interface\\IMeasureDeviceOperation.cs"}, {"SourceFile": "Equipment\\FPIEquips\\QCD3900\\Params\\QCD3900DeviceStateParam.cs"}, {"SourceFile": "Equipment\\FPIEquips\\QCD3900\\UI\\UC_QCD3900DeviceState.cs"}, {"SourceFile": "Equipment\\FPIEquips\\QCD3900\\UI\\UC_QCD3900DeviceState.Designer.cs"}, {"SourceFile": "Equipment\\GB2005RecvEquip\\GB212RecvEquipment.cs"}, {"SourceFile": "Equipment\\HDSampleEquip\\ZSC-VII\\HDZSC_VIISampleEquip.cs"}, {"SourceFile": "Equipment\\HDSampleEquip\\ZSC-VII\\UI\\UC_HDZSCVIIParam.cs"}, {"SourceFile": "Equipment\\HDSampleEquip\\ZSC-VII\\UI\\UC_HDZSCVIIParam.designer.cs"}, {"SourceFile": "Equipment\\FPIEquips\\QCD3900\\QCD3900Equip.cs"}, {"SourceFile": "Equipment\\ExterEquips\\YstEquip\\YstEA900UpsEquipment.cs"}, {"SourceFile": "Interface\\IAirConditionControl.cs"}, {"SourceFile": "Interface\\IRefreshUI.cs"}, {"SourceFile": "Interface\\IProbeWash.cs"}, {"SourceFile": "Common\\EventHandlerDefine.cs"}, {"SourceFile": "Interface\\IQCDataGet.cs"}, {"SourceFile": "Interface\\IDeviceRangeSet.cs"}, {"SourceFile": "Interface\\IDeviceNotify.cs"}, {"SourceFile": "Interface\\IMixedSampleDeviceOperation.cs"}, {"SourceFile": "Interface\\ISampleBottleNum.cs"}, {"SourceFile": "Interface\\ISIA3900DeviceParamTransfer.cs"}, {"SourceFile": "Interface\\IDeviceKeyParams.cs"}, {"SourceFile": "Interface\\IWCSDeviceOperation.cs"}, {"SourceFile": "Interface\\IQCDeviceOperation.cs"}, {"SourceFile": "Interface\\ISampleDeviceOperation.cs"}, {"SourceFile": "Properties\\AssemblyInfo.cs"}, {"SourceFile": "Properties\\Resources.Designer.cs"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.8.AssemblyAttributes.cs"}], "References": [{"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\FpiDLL\\DevComponents.DotNetBar2.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Alarm\\bin\\Debug\\Fpi.Alarm.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Alarm\\bin\\Debug\\Fpi.Alarm.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Camera\\bin\\Debug\\Fpi.Camera.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Camera\\bin\\Debug\\Fpi.Camera.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Communication\\bin\\Debug\\Fpi.Communication.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Communication\\bin\\Debug\\Fpi.Communication.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Data\\bin\\Debug\\Fpi.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Data\\bin\\Debug\\Fpi.Data.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.DB\\bin\\Debug\\Fpi.DB.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.DB\\bin\\Debug\\Fpi.DB.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Device\\bin\\Debug\\Fpi.Device.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Device\\bin\\Debug\\Fpi.Device.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Entrance\\bin\\Debug\\Fpi.Entrance.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Entrance\\bin\\Debug\\Fpi.Entrance.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.HB.Business\\bin\\Debug\\Fpi.HB.Business.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.HB.Business\\bin\\Debug\\Fpi.HB.Business.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Instrument\\bin\\Debug\\Fpi.Instrument.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Instrument\\bin\\Debug\\Fpi.Instrument.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Json\\bin\\Debug\\Fpi.Json.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Json\\bin\\Debug\\Fpi.Json.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Log\\bin\\Debug\\Fpi.Log.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Log\\bin\\Debug\\Fpi.Log.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Operation\\bin\\Debug\\Fpi.Operation.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Operation\\bin\\Debug\\Fpi.Operation.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Timer\\bin\\Debug\\Fpi.Timer.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Timer\\bin\\Debug\\Fpi.Timer.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.UI.Common\\bin\\Debug\\Fpi.UI.Common.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.UI.Common\\bin\\Debug\\Fpi.UI.Common.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.UI.PC\\bin\\Debug\\Fpi.UI.PC.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.UI.PC\\bin\\Debug\\Fpi.UI.PC.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.User\\bin\\Debug\\Fpi.User.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.User\\bin\\Debug\\Fpi.User.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Util\\bin\\Debug\\Fpi.Util.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Util\\bin\\Debug\\Fpi.Util.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\Product\\Debug\\bin\\Fpi.WMS3900.DB.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\Product\\Debug\\bin\\Fpi.WMS3900.DB.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Xml\\bin\\Debug\\Fpi.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\Fpi.Xml\\bin\\Debug\\Fpi.Xml.dll"}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\FpiDLL\\HZH_Controls.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\Microsoft.JScript.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\FpiDLL\\Newtonsoft.Json.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\FpiDLL\\SunnyUI.Common.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\WMS3900\\FpiDLL\\SunnyUI.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Design.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Drawing.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Windows.Forms.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "E:\\01-数采软件\\01-WMS3900\\02-编码实现\\Product\\Debug\\bin\\Fpi.WMS3900.Equipment.dll", "OutputItemRelativePath": "Fpi.WMS3900.Equipment.dll"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}