﻿using System;
using System.Drawing;
using System.Windows.Forms;
using Fpi.Util.Reflection;
using Fpi.WMS3000.SystemConfig.ImagePatrol.Config;
using Sunny.UI;

namespace Fpi.WMS3000.SystemConfig.UI
{
    /// <summary>
    /// 图像巡检参数配置
    /// </summary>
    public partial class FrmImagePatrolConfig : UIForm
    {
        #region 构造

        public FrmImagePatrolConfig()
        {
            InitializeComponent();
        }

        #endregion

        #region 事件

        private void FrmSmartPatrolConfig_Load(object sender, EventArgs e)
        {
            // 先按顺序加载已启用项
            foreach(var unitType in ImagePatrolManager.GetInstance().UsedPatrolUnitTypeList)
            {
                try
                {
                    ImageUnitSmartPatrolBase unit = (ImageUnitSmartPatrolBase)ReflectionHelper.CreateInstance(unitType);
                    var node = tvUnitList.Nodes.Add(unit.UnitName);
                    node.Tag = unit;
                    node.Checked = true;
                }
                catch
                {
                }
            }

            // 再加载未启用项
            // 反射加载所有巡检项
            Type[] types = ReflectionHelper.GetChildTypes(typeof(ImageUnitSmartPatrolBase));
            foreach(Type type in types)
            {
                // 跳过已加载项
                if(!ImagePatrolManager.GetInstance().UsedPatrolUnitTypeList.Contains(type.FullName))
                {
                    try
                    {
                        ImageUnitSmartPatrolBase unit = (ImageUnitSmartPatrolBase)ReflectionHelper.CreateInstance(type);
                        var node = tvUnitList.Nodes.Add(unit.UnitName);
                        node.Tag = unit;
                    }
                    catch
                    {
                    }
                }
            }
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            // 巡检项启用情况
            ImagePatrolManager.GetInstance().UsedPatrolUnitTypeList.Clear();
            foreach(TreeNode node in tvUnitList.Nodes)
            {
                if(node.Checked && node.Tag is ImageUnitSmartPatrolBase smartPatrolUnit)
                {
                    string typeName = smartPatrolUnit.GetType().FullName;
                    if(!ImagePatrolManager.GetInstance().UsedPatrolUnitTypeList.Contains(typeName))
                    {
                        ImagePatrolManager.GetInstance().UsedPatrolUnitTypeList.Add(typeName);
                    }
                }
            }

            ImagePatrolManager.GetInstance().Save();
            ImagePatrolManager.GetInstance().RebuildPatrolUnitList();
        }

        #region 拖动排序

        private void tvUnitList_ItemDrag(object sender, ItemDragEventArgs e)
        {
            DoDragDrop(e.Item, DragDropEffects.Move);
        }

        private void tvUnitList_DragEnter(object sender, DragEventArgs e)
        {
            if(e.Data.GetDataPresent(typeof(TreeNode)))
            {
                e.Effect = DragDropEffects.Move;
            }
        }

        private void tvUnitList_DragDrop(object sender, DragEventArgs e)
        {
            Point dropPoint = tvUnitList.PointToClient(new Point(e.X, e.Y));
            TreeNode draggedNode = (TreeNode)e.Data.GetData(typeof(TreeNode));
            TreeNode targetNode = tvUnitList.GetNodeAt(dropPoint);

            if(draggedNode != null && targetNode != null && draggedNode.Parent == null && targetNode.Parent == null)
            {
                int droppedIndex = targetNode.Index;
                tvUnitList.Nodes.Remove(draggedNode);
                tvUnitList.Nodes.Insert(droppedIndex, draggedNode);
            }
        }

        private void tvUnitList_DragOver(object sender, DragEventArgs e)
        {
            Point targetPoint = tvUnitList.PointToClient(new Point(e.X, e.Y));
            TreeNode targetNode = tvUnitList.GetNodeAt(targetPoint);

            if(targetNode != null && targetNode.Parent == null) // 只允许一级节点之间移动
            {
                e.Effect = DragDropEffects.Move;
            }
            else
            {
                e.Effect = DragDropEffects.None;
            }
        }

        #endregion

        #endregion
    }
}