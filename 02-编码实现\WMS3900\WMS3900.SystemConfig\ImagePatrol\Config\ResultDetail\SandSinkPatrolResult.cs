﻿using System.Collections.Generic;
using System.ComponentModel;
using Fpi.WMS3000.Equipment;

namespace Fpi.WMS3000.SystemConfig.ImagePatrol.Config
{
    /// <summary>
    /// 配水预处理沉砂池巡检结果
    /// </summary>
    public class SandSinkPatrolResult : ImageUnitPatrolResultBase
    {
        #region 字段属性

        /// <summary>
        /// 配水预处理沉砂池脏污状态
        /// </summary>
        [Description("配水预处理沉砂池脏污状态")]
        public eSmutState SandSinkSmutState { get; set; }

        #endregion

        #region 构造

        public SandSinkPatrolResult()
        {
            UnitId = "SandSink";
            UnitName = "配水预处理沉砂池";
        }

        #endregion

        #region 方法重写

        /// <summary>
        /// 打印巡检结果
        /// </summary>
        /// <returns></returns>
        public override List<string> GetResultStr()
        {
            return new List<string>
            {
                $"配水预处理沉砂池脏污状态：{SandSinkSmutState}"
            };
        }

        #endregion
    }
}