﻿using System;
using System.ComponentModel;
using Fpi.Communication.Converter;

namespace Fpi.WMS3000.Equipment.DS
{
    /// <summary>
    /// 浙江鼎硕6CNZDES智能电表参数
    /// </summary>
    public class DS6CNZDESParam
    {
        #region 字段属性

        /// <summary>
        /// A相电压 单位V
        /// </summary>
        [Description("A相电压(V)")]
        public double AVoltage { get; set; } = double.NaN;

        /// <summary>
        /// B相电压 单位V
        /// </summary>
        [Description("B相电压(V)")]
        public double BVoltage { get; set; } = double.NaN;

        /// <summary>
        /// C相电压 单位V
        /// </summary>
        [Description("C相电压(V)")]
        public double CVoltage { get; set; } = double.NaN;

        /// <summary>
        /// A相电流 单位A
        /// </summary>
        [Description("A相电流(A)")]
        public double ACurrent { get; set; } = double.NaN;

        /// <summary>
        /// B相电流 单位A
        /// </summary>
        [Description("B相电流(A)")]
        public double BCurrent { get; set; } = double.NaN;

        /// <summary>
        /// C相电流 单位A
        /// </summary>
        [Description("C相电流(A)")]
        public double CCurrent { get; set; } = double.NaN;

        /// <summary>
        /// 总功率 单位kW
        /// </summary>
        [Description("总功率(kW)")]
        public double TotalPower { get; set; } = double.NaN;

        /// <summary>
        /// A相功率 单位kW
        /// </summary>
        [Description("A相功率(kW)")]
        public double APower { get; set; } = double.NaN;

        /// <summary>
        /// B相功率 单位kW
        /// </summary>
        [Description("B相功率(kW)")]
        public double BPower { get; set; } = double.NaN;

        /// <summary>
        /// C相功率 单位kW
        /// </summary>
        [Description("C相功率(kW)")]
        public double CPower { get; set; } = double.NaN;

        /// <summary>
        /// 总电量 单位kWh
        /// </summary>
        [Description("总电量(kWh)")]
        public double TotalCharge { get; set; } = double.NaN;

        #endregion

        #region 构造

        public DS6CNZDESParam()
        {
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 更新电流电压功率
        /// </summary>
        /// <param name="data"></param>
        public void UpdateCurrentVoltagePowe(byte[] data, int startIndex)
        {
            if(data.Length < startIndex + 22)
            {
                throw new Exception("读取电流电压功率信息回应数据不完整！");
            }

            AVoltage = DataConverter.GetInstance().ToUInt32(data, startIndex) / 10f;
            BVoltage = DataConverter.GetInstance().ToUInt32(data, startIndex + 2) / 10f;
            CVoltage = DataConverter.GetInstance().ToUInt32(data, startIndex + 4) / 10f;
            ACurrent = DataConverter.GetInstance().ToUInt32(data, startIndex + 6) / 100f;
            BCurrent = DataConverter.GetInstance().ToUInt32(data, startIndex + 8) / 100f;
            CCurrent = DataConverter.GetInstance().ToUInt32(data, startIndex + 10) / 100f;
            TotalPower = DataConverter.GetInstance().ToUInt32(data, startIndex + 14) / 1000f;
            APower = DataConverter.GetInstance().ToUInt32(data, startIndex + 16) / 1000f;
            BPower = DataConverter.GetInstance().ToUInt32(data, startIndex + 18) / 1000f;
            CPower = DataConverter.GetInstance().ToUInt32(data, startIndex + 20) / 1000f;
        }

        /// <summary>
        /// 更新电量信息
        /// </summary>
        /// <param name="data"></param>
        public void UpdateCharge(byte[] data, int startIndex)
        {
            if(data.Length < startIndex + 4)
            {
                throw new Exception("读取电能信息回应数据不完整！");
            }

            TotalCharge = DataConverter.GetInstance().ToUInt64(data, startIndex) / 100f;
        }

        #endregion
    }
}