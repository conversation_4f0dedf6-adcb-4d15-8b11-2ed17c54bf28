﻿using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using Fpi.Util.Extensions;
using Fpi.WMS3000.SystemConfig.SmartPatrol.Helper;

namespace Fpi.WMS3000.SystemConfig.SmartPatrol.Config
{
    /// <summary>
    /// 试剂存储单元巡检结果
    /// </summary>
    public class SJUnitPatrolResult : SingleUnitPatrolResultBase
    {
        #region 字段属性

        /// <summary>
        /// 试剂使用信息列表
        /// </summary>
        public List<ReagentUsageState> ReagentList { get; set; } = new List<ReagentUsageState>();

        /// <summary>
        /// 高指试剂温度(℃)
        /// </summary>
        [Description("高指试剂温度(℃)")]
        public double? CodMnReagentTemp { get; set; }

        /// <summary>
        /// 氨氮试剂温度(℃)
        /// </summary>
        [Description("氨氮试剂温度(℃)")]
        public double? NH4ReagentTemp { get; set; }

        /// <summary>
        /// 总磷试剂温度(℃)
        /// </summary>
        [Description("总磷试剂温度(℃)")]
        public double? TPReagentTemp { get; set; }

        /// <summary>
        /// 总氮试剂温度(℃)
        /// </summary>
        [Description("总氮试剂温度(℃)")]
        public double? TNReagentTemp { get; set; }

        /// <summary>
        /// 五参数试剂温度(℃)
        /// </summary>
        [Description("五参数试剂温度(℃)")]
        public double? FiveParamReagentTempNode { get; set; }

        #endregion

        #region 构造

        public SJUnitPatrolResult()
        {
            UnitId = "ReagentStorage";
            UnitName = "试剂存储单元";
        }

        #endregion

        #region 方法重写

        /// <summary>
        /// 打印巡检结果
        /// </summary>
        /// <returns></returns>
        public override string GetResultStr()
        {
            StringBuilder resultStr = new StringBuilder();

            resultStr.AppendLine(UnitName)
                .AppendLine($"巡检结果：{PatrolResult}")
                .AppendLine($"巡检详情：")
                .AppendLine($"1.试剂寿命：");
            foreach(var reagent in ReagentList)
            {
                resultStr.Append(reagent.GetResultStr());
            }

            resultStr.AppendLine($"2.试剂温度：")
                .AppendLine($"高指试剂：{CodMnReagentTemp.ToDisplayFormat(PatrolDataDisplayHelper.FloatFormat, "℃")}")
                .AppendLine($"氨氮试剂：{NH4ReagentTemp.ToDisplayFormat(PatrolDataDisplayHelper.FloatFormat, "℃")}")
                .AppendLine($"总磷试剂：{TPReagentTemp.ToDisplayFormat(PatrolDataDisplayHelper.FloatFormat, "℃")}")
                .AppendLine($"总氮试剂：{TNReagentTemp.ToDisplayFormat(PatrolDataDisplayHelper.FloatFormat, "℃")}")
                .AppendLine($"五参数试剂：{FiveParamReagentTempNode.ToDisplayFormat(PatrolDataDisplayHelper.FloatFormat, "℃")}");

            return resultStr.ToString();
        }

        #endregion
    }
}