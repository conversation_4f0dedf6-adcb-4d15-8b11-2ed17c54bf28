﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using Fpi.Communication.Converter;

namespace Fpi.WMS3000.Equipment.SIA3900
{
    /// <summary>
    /// 试剂维护区
    /// </summary>
    public class SIA3900ReagentMaintenance
    {
        #region 字段属性

        /// <summary>
        /// 试剂信息
        /// </summary>
        [Description("试剂信息")]
        public List<SIA3900ReagentInfo> ReagentInfos { get; private set; } = new List<SIA3900ReagentInfo>();

        /// <summary>
        /// 核查液信息
        /// </summary>
        [Description("核查液信息")]
        public Dictionary<eSIA3900CheckFluidType, SIA3900CheckFluidInfo> CheckFluidInfos { get; private set; } =
        new Dictionary<eSIA3900CheckFluidType, SIA3900CheckFluidInfo>();

        #endregion

        #region 构造

        public SIA3900ReagentMaintenance()
        {
            for(int i = 0; i < 11; i++)
            {
                ReagentInfos.Add(new SIA3900ReagentInfo());
            }

            foreach(var info in Enum.GetValues(typeof(eSIA3900CheckFluidType)))
            {
                if(!CheckFluidInfos.ContainsKey((eSIA3900CheckFluidType)info))
                {
                    CheckFluidInfos.Add((eSIA3900CheckFluidType)info, new SIA3900CheckFluidInfo());
                }
            }
        }

        #endregion

        #region 公有方法

        /// <summary>
        /// 更新试剂维护区
        /// </summary>
        /// <param name="data"></param>
        public void UpdataValue(byte[] data, int startIndex)
        {
            if(data.Length < startIndex + 132)
            {
                throw new Exception("读取测量数据回应数据不完整！");
            }

            for(int i = 0; i < ReagentInfos.Count; i++)
            {
                ReagentInfos[i].UpdateValue(data, startIndex + i * 12);
            }

            for(int i = 1; i <= CheckFluidInfos.Count; i++)
            {
                if(CheckFluidInfos.ContainsKey((eSIA3900CheckFluidType)i))
                {
                    CheckFluidInfos[(eSIA3900CheckFluidType)i].UpdateValue(data, 127 + i * 8);
                }
            }
        }

        #endregion
    }

    /// <summary>
    /// 试剂信息
    /// </summary>
    public class SIA3900ReagentInfo
    {
        #region 字段属性

        /// <summary>
        /// 阀位号
        /// </summary>
        [Description("阀位号")]
        public int ValvePosition { get; private set; } = -1;

        /// <summary>
        /// 试剂名称
        /// </summary>
        [Description("试剂名称")]
        public eSIA3900ReagentType ReagentName { get; private set; } = eSIA3900ReagentType.无效;

        /// <summary>
        /// 余量
        /// </summary>
        [Description("余量")]
        public int LiquidResidual { get; private set; } = -1;

        /// <summary>
        /// 总量
        /// </summary>
        [Description("总量")]
        public int LiquidTotal { get; private set; } = -1;

        /// <summary>
        /// 剩余天数
        /// </summary>
        [Description("剩余天数")]
        public int RemainDay { get; private set; } = -1;

        /// <summary>
        /// 质保期
        /// </summary>
        [Description("质保期")]
        public int GuaranteePeriod { get; private set; } = -1;

        #endregion

        #region 公共方法

        /// <summary>
        /// 更新试剂信息
        /// </summary>
        /// <param name="data"></param>
        public void UpdateValue(byte[] data, int startIndex)
        {
            ValvePosition = DataConverter.GetInstance().ToInt32(data, startIndex);
            ReagentName = (eSIA3900ReagentType)DataConverter.GetInstance().ToInt32(data, startIndex + 2);
            LiquidResidual = DataConverter.GetInstance().ToInt32(data, startIndex + 4);
            LiquidTotal = DataConverter.GetInstance().ToInt32(data, startIndex + 6);
            RemainDay = DataConverter.GetInstance().ToInt32(data, startIndex + 8);
            GuaranteePeriod = DataConverter.GetInstance().ToInt32(data, startIndex + 10);
        }

        #endregion
    }

    public class SIA3900CheckFluidInfo
    {
        #region 字段属性

        /// <summary>
        /// 余量
        /// </summary>
        [Description("余量")]
        public int LiquidResidual { get; private set; } = -1;

        /// <summary>
        /// 总量
        /// </summary>
        [Description("总量")]
        public int LiquidTotal { get; private set; } = -1;

        /// <summary>
        /// 剩余天数
        /// </summary>
        [Description("剩余天数")]
        public int RemainDay { get; private set; } = -1;

        /// <summary>
        /// 质保期
        /// </summary>
        [Description("质保期")]
        public int GuaranteePeriod { get; private set; } = -1;

        #endregion

        #region 公共方法

        /// <summary>
        /// 更新试剂信息
        /// </summary>
        /// <param name="data"></param>
        public void UpdateValue(byte[] data, int startIndex)
        {
            LiquidResidual = DataConverter.GetInstance().ToInt32(data, startIndex);
            LiquidTotal = DataConverter.GetInstance().ToInt32(data, startIndex + 2);
            RemainDay = DataConverter.GetInstance().ToInt32(data, startIndex + 4);
            GuaranteePeriod = DataConverter.GetInstance().ToInt32(data, startIndex + 6);
        }

        #endregion
    }
}