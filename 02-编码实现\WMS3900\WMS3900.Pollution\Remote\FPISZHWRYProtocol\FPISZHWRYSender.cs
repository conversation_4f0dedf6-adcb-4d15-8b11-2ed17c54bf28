﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Fpi.Communication.Interfaces;
using Fpi.Communication.Protocols;
using Fpi.HB.Business.Protocols.Helper;
using Fpi.HB.Business.Protocols.Interface;
using Fpi.WMS3000.Remote.FPISZH;
using Fpi.WMS3000.Remote.GJDBS.Config;
using Fpi.WMS3000.Remote.GJDBS.GJDBSDataFrame;

namespace Fpi.WMS3000.Pollution.Remote.FPISZHWRY
{
    /// <summary>
    /// 谱育科技数智化污染源数据传输协议-数据发送器
    /// </summary>
    public class FPISZHWRYSender : FPISZHSender, IDataUpload, IGetSuppleData
    {
        #region 方法重写

        /// <summary>
        /// //上传统计数据
        /// </summary>
        /// <param name="obj"></param>
        protected override void ReportCountFunc(object obj)
        {
            // 通道关闭，不传输
            if(CurrentPipe != null && !CurrentPipe.valid)
            {
                return;
            }
            // 链接断开时，仍运行，数据进入数据补传表

            #region 参数定义

            DateTime now = DateTime.Now;
            bool minuteTrigger = false;
            bool hourTrigger = false;
            bool dayTrigger = false;
            bool stateTrigger = false;
            DateTime beginMinute = DateTime.MinValue;
            DateTime endMinute = DateTime.MinValue;
            DateTime beginHour = DateTime.MinValue;
            DateTime endHour = DateTime.MinValue;
            DateTime beginDay = DateTime.MinValue;
            DateTime endDay = DateTime.MinValue;

            #endregion

            #region 获取各类型数据上传触发时间点

            // 获取各类型数据上传触发时间点
            try
            {
                lock(_reportLockObj)
                {
                    hourTrigger = _desc.GjdbsSingleCfg.IsTriggerHourPLan(now, out beginHour, out endHour);
                    minuteTrigger = _desc.GjdbsSingleCfg.IsTriggerMinutePLan(now, out beginMinute, out endMinute);
                    dayTrigger = _desc.GjdbsSingleCfg.IsTriggerDayPLan(now, out beginDay, out endDay);
                    stateTrigger = _desc.GjdbsSingleCfg.IsTriggerStatePLan(now);
                }
            }
            catch(Exception e)
            {
                ProtocolLogHelper.ShowMsg("获取数据自动上传计划出错:" + e.Message);
            }

            #endregion

            #region 根据触发情况上传数据

            // 分钟数据 2051
            if(minuteTrigger)
            {
                UploadData((int)eUploadDataType.分钟数据);
            }

            // 日数据 2031
            if(dayTrigger)
            {
                Task.Run(() =>
                {
                    // 延时1分钟，确保数据已完成计算
                    Task.Delay(TimeSpan.FromMinutes(1)).Wait();
                    UploadData((int)eUploadDataType.日数据);
                });
            }

            // 小时数据 2061
            if(hourTrigger)
            {
                Task.Run(() =>
                {
                    // 延时1分钟，确保数据已完成计算
                    Task.Delay(TimeSpan.FromMinutes(1)).Wait();
                    UploadData((int)eUploadDataType.小时数据);
                });
            }

            // 设备参数 3020
            if(stateTrigger)
            {
                //UpdateInspection();
            }

            #endregion
        }

        #endregion

        #region IDataUpload

        /// <summary>
        /// 重写部分数据类型上传逻辑
        /// </summary>
        /// <param name="type"></param>
        public override void UploadData(int type)
        {
            if(!Enum.IsDefined(UploadDataType, type))
            {
                throw new Exception($"上传类型数值{type}不在本协议支持的类型{UploadDataType.Name}的定义范围内");
            }
            eUploadDataType uploadType = (eUploadDataType)type;

            switch(uploadType)
            {
                // 定制部分数据类型，走自定义协议处理；
                case eUploadDataType.实时数据:
                case eUploadDataType.分钟数据:
                case eUploadDataType.小时数据:
                case eUploadDataType.日数据:
                    CustomUploadData(uploadType);
                    break;

                // 非定制部分数据类型，走标准协议处理；
                default:
                    base.UploadData(type);
                    return;
            }
        }

        /// <summary>
        /// 定制数据上传部分
        /// </summary>
        /// <param name="type"></param>
        private void CustomUploadData(eUploadDataType type)
        {
            if(_desc != null)
            {
                try
                {
                    // 待发送数据表
                    var cmdList = new List<GJDBSCommand>();
                    switch(type)
                    {
                        case eUploadDataType.实时数据:
                            cmdList.Add(FPISZHWRYHelper.BuildRealTimeData(_desc));
                            // 开关量因子数据
                            if(_desc.GjdbsSingleCfg.UploadStateNode)
                            {
                                Thread.Sleep(50);
                                cmdList.Add(FPISZHWRYHelper.BuildStateNodeData(_desc));
                            }
                            break;
                        case eUploadDataType.分钟数据:
                        case eUploadDataType.小时数据:
                        case eUploadDataType.日数据:
                            cmdList.AddRange(FPISZHWRYHelper.BuildHistoryData(_desc, DateTime.Now.AddDays(-1), DateTime.Now, type, eGetDataCount.最新一条数据));
                            break;

                        // 非定制部分数据类型，走标准协议处理；
                        default:
                            throw new Exception($"类型{type}非定制部分数据类型，请走标准协议处理");
                    }

                    // 发送数据
                    foreach(GJDBSCommand cmd in cmdList)
                    {
                        _desc.SendDataWithAddendum(cmd);
                    }
                }
                catch(Exception e)
                {
                    ProtocolLogHelper.ShowMsg($"上传{(eFpiHttpUploadDataType)type}类型数据出错:{e.Message}");
                }
            }
            else
            {
                //throw new Exception("协议描述器为空！");
            }
        }

        #endregion

        #region IGetSuppleData

        /// <summary>
        /// 定制数据补传部分
        /// </summary>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        public override List<IByteStream> GetSuppleData(DateTime startTime, DateTime endTime, int type)
        {
            if(!Enum.IsDefined(UploadDataType, type))
            {
                throw new Exception($"上传类型数值{type}不在本协议支持的类型{UploadDataType.Name}的定义范围内");
            }
            eUploadDataType uploadType = (eUploadDataType)type;

            List<GJDBSCommand> cmdLists;
            switch(uploadType)
            {
                // 定制部分数据类型，走自定义协议处理；
                case eUploadDataType.实时数据:
                    cmdLists = FPISZHWRYHelper.BuildRealTimeData(_desc, startTime, endTime, eGetDataCount.所有数据);
                    break;
                case eUploadDataType.分钟数据:
                case eUploadDataType.小时数据:
                case eUploadDataType.日数据:
                    cmdLists = FPISZHWRYHelper.BuildHistoryData(_desc, startTime, endTime, uploadType, eGetDataCount.所有数据);
                    break;
                // 非定制部分数据类型，走标准协议处理；
                default:
                    return base.GetSuppleData(startTime, endTime, type);
            }

            return cmdLists?.Cast<IByteStream>().ToList();
        }

        #endregion
    }
}