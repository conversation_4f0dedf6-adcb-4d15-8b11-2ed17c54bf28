﻿using System;
using System.Collections.Generic;
using Fpi.Data.Config;
using Fpi.WMS3000.Voice.Config;
using Fpi.WMS3000.Voice.Interface;
using Sunny.UI;

namespace Fpi.WMS3000.Voice.VoicesTemplate
{
    public partial class UC_StateNodeControl : UIUserControl, IVoiceConfig
    {
        #region 字段属性

        private VoiceControlCmd _voiceCmd;

        #endregion

        #region 构造

        public UC_StateNodeControl()
        {
            InitializeComponent();

            cmbStateNode.Items.AddRange(GetFlagNodeList());
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 获取系统中已选中的设备列表
        /// </summary>
        /// <returns></returns>
        private StateNode[] GetFlagNodeList()
        {
            var snList = new List<StateNode>();
            foreach(StateNode sn in DataManager.GetInstance().GetAllStateNodes())
            {
                if(sn.NodeType is eStateNodeType.Pump or eStateNodeType.Valve or eStateNodeType.Controller)
                {
                    snList.Add(sn);
                }
            }
            return snList.ToArray();
        }

        #endregion

        #region IVoiceConfig接口

        public void Check()
        {
            if(cmbStateNode.SelectedItem is not StateNode _)
            {
                throw new Exception("请选择控制因子！");
            }

            if(!(rdbClose.Checked || rdbOpen.Checked))
            {
                throw new Exception("请选择控制类型！");
            }
        }

        public void LoadConfig(VoiceControlCmd cmd)
        {
            _voiceCmd = cmd;

            string tmp = _voiceCmd.GetPropertyValue(GlobalNameDefine.PropertyName_VarNodeId);
            if(tmp != null)
            {
                VarNode stateNode = DataManager.GetInstance().GetVarNodeById(tmp);
                cmbStateNode.SelectedItem = stateNode;
            }

            string type = _voiceCmd.GetPropertyValue(GlobalNameDefine.PropertyName_StateNodeState);
            bool.TryParse(type, out bool value);

            rdbOpen.Checked = value;
            rdbClose.Checked = !value;
        }

        public VoiceControlCmd Save()
        {
            StateNode stateNode = cmbStateNode.SelectedItem as StateNode;
            _voiceCmd.SetProperty(GlobalNameDefine.PropertyName_VarNodeId, stateNode?.id);
            _voiceCmd.SetProperty(GlobalNameDefine.PropertyName_StateNodeState, rdbOpen.Checked.ToString());

            return _voiceCmd;
        }

        #endregion
    }
}