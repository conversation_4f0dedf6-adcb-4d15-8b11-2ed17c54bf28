﻿using System.Windows.Forms;
using Fpi.UI.PC.DockForms;
using Fpi.WMS3000.UI.DataQuery;
using Sunny.UI;

namespace Fpi.WMS3000.Pollution.UI.DataQuery
{
    /// <summary>
    /// 核查数据查询
    /// </summary>
    public partial class FrmQueryCheckData : BaseWindow
    {
        public FrmQueryCheckData()
        {
            InitializeComponent();
        }

        private void FrmQueryCheckData_Load(object sender, System.EventArgs e)
        {
            {
                var page = new UIPage();
                var uc = new UC_QuerySampleCheckData();
                page.Controls.Add(uc);
                uc.Dock = DockStyle.Fill;
                page.Text = uc.TitleName;
                tabMain.AddPage(page);
            }

            {
                var page = new UIPage();
                var uc = new UC_QueryBlindCheckData();
                page.Controls.Add(uc);
                uc.Dock = DockStyle.Fill;
                page.Text = uc.TitleName;
                tabMain.AddPage(page);
            }

            {
                var page = new UIPage();
                var uc = new UC_QueryBlankCheckData();
                page.Controls.Add(uc);
                uc.Dock = DockStyle.Fill;
                page.Text = uc.TitleName;
                tabMain.AddPage(page);
            }

            {
                var page = new UIPage();
                var uc = new UC_QueryMultiCheckData();
                page.Controls.Add(uc);
                uc.Dock = DockStyle.Fill;
                page.Text = uc.TitleName;
                tabMain.AddPage(page);
            }

            {
                var page = new UIPage();
                var uc = new UC_QueryMultiCheckDetailData();
                page.Controls.Add(uc);
                uc.Dock = DockStyle.Fill;
                page.Text = uc.TitleName;
                tabMain.AddPage(page);
            }

            {
                var page = new UIPage();
                var uc = new UC_QueryArbitraryConCheckData();
                page.Controls.Add(uc);
                uc.Dock = DockStyle.Fill;
                page.Text = uc.TitleName;
                tabMain.AddPage(page);
            }
        }
    }
}