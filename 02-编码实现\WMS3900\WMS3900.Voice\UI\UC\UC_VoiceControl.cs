﻿using System.Windows.Forms;
using Fpi.WMS3000.Voice.Config;
using Fpi.Xml;
using Sunny.UI;

namespace Fpi.WMS3000.Voice.UI.UC
{
    public partial class UC_VoiceControl : UIUserControl
    {
        #region 字段属性

        /// <summary>
        /// 对应配置存储实例
        /// </summary>
        private VoiceControlCmd _voice;

        /// <summary>
        /// 修改模式
        /// </summary>
        private readonly eEditType _editType;

        #endregion

        #region 构造

        public UC_VoiceControl()
        {
            InitializeComponent();

            this.Dock = DockStyle.Fill;
        }

        public UC_VoiceControl(eEditType editType) : this()
        {
            _editType = editType;
        }

        #endregion

        #region 公共方法

        public void LoadOpreation(VoiceControlCmd voice)
        {
            this._voice = voice;
            this.txtId.ReadOnly = _editType == eEditType.编辑;

            if(voice.id.Equals(IdNameNode.DefaultID))
            {
                return;
            }

            this.txtId.Text = voice.id;
            this.txtName.Text = voice.name;
            this.txtDesc.Text = voice.description;
            this.txtSubCmds.Text = voice.SubCmdList;
            this.chkNeedConfirm.Checked = voice.isNeedConfirm;
        }

        public void Check()
        {
            if(string.IsNullOrEmpty(this.txtId.Text))
            {
                throw new System.Exception("请输入命令ID");
            }
            if(string.IsNullOrEmpty(this.txtName.Text))
            {
                throw new System.Exception("请输入命令名称");
            }
            if(string.IsNullOrEmpty(this.txtSubCmds.Text))
            {
                throw new System.Exception("请输入子命令");
            }
        }

        public void Save()
        {
            _voice.id = this.txtId.Text;
            _voice.name = this.txtName.Text;
            _voice.description = this.txtDesc.Text;
            _voice.SubCmdList = this.txtSubCmds.Text;
            _voice.isNeedConfirm = this.chkNeedConfirm.Checked;
        }

        #endregion
    }
}