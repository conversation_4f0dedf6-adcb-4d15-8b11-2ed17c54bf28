﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using Fpi.Communication.Converter;

namespace Fpi.WMS3000.Equipment.QCD3900
{
    /// <summary>
    /// 器件寿命信息
    /// </summary>
    public class QCD3900ElementLifeInfo
    {
        #region 属性

        /// <summary>
        /// 试剂报警阈值（%）
        /// </summary>
        [Description("试剂报警阈值")]
        public int ReagentAlarmLimit { get; private set; } = -1;

        /// <summary>
        /// 寿命诊断报警阈值（%）
        /// </summary>
        [Description("寿命诊断报警阈值")]
        public int CompLifeAlarmLimit { get; private set; } = -1;

        /// <summary>
        /// 水样管更换时间
        /// </summary>
        [Description("水样管更换时间")]
        public DateTime WaterTubeChangeTime { get; private set; } = DateTime.MinValue;

        /// <summary>
        /// 水样管保质期
        /// </summary>
        [Description("水样管保质期")]
        public int WTubeGuaranteePeriod { get; private set; } = -1;

        /// <summary>
        /// 水样管最久可用时间
        /// </summary>
        [Description("水样管最久可用时间")]
        public DateTime WaterTubeLogestUseTime => WaterTubeChangeTime == DateTime.MinValue || WTubeGuaranteePeriod == -1
                    ? DateTime.MinValue
                    : WaterTubeChangeTime.AddDays(WTubeGuaranteePeriod);

        /// <summary>
        /// 储液环更换时间
        /// </summary>
        [Description("储液环更换时间")]
        public DateTime ReceiverRingChangeTime { get; private set; } = DateTime.MinValue;

        /// <summary>
        /// 储液环保质期
        /// </summary>
        [Description("储液环保质期")]
        public int RRingGuaranteePeriod { get; private set; } = -1;

        /// <summary>
        /// 储液环最久可用时间
        /// </summary>
        [Description("储液环最久可用时间")]
        public DateTime ReceiverRingLogestUseTime => ReceiverRingChangeTime == DateTime.MinValue || RRingGuaranteePeriod == -1
                    ? DateTime.MinValue
                    : ReceiverRingChangeTime.AddDays(RRingGuaranteePeriod);

        /// <summary>
        /// 液位检测器1更换时间
        /// </summary>
        [Description("液位检测器1更换时间")]
        public DateTime LevelDetector1ChangeTime { get; private set; } = DateTime.MinValue;

        /// <summary>
        /// 液位检测器1保质期
        /// </summary>
        [Description("液位检测器1保质期")]
        public int LDetector1GuaranteePeriod { get; private set; } = -1;

        /// <summary>
        /// 液位检测器1最久可用时间
        /// </summary>
        [Description("液位检测器1最久可用时间")]
        public DateTime LevelDetector1LogestUseTime => LevelDetector1ChangeTime == DateTime.MinValue || LDetector1GuaranteePeriod == -1
                    ? DateTime.MinValue
                    : LevelDetector1ChangeTime.AddDays(LDetector1GuaranteePeriod);

        /// <summary>
        /// 液位检测器2更换时间
        /// </summary>
        [Description("液位检测器2更换时间")]
        public DateTime LevelDetector2ChangeTime { get; private set; } = DateTime.MinValue;

        /// <summary>
        /// 液位检测器2保质期
        /// </summary>
        [Description("液位检测器2保质期")]
        public int LDetector2GuaranteePeriod { get; private set; } = -1;

        /// <summary>
        /// 液位检测器2最久可用时间
        /// </summary>
        [Description("液位检测器2最久可用时间")]
        public DateTime LevelDetector2LogestUseTime => LevelDetector2ChangeTime == DateTime.MinValue || LDetector2GuaranteePeriod == -1
                    ? DateTime.MinValue
                    : LevelDetector2ChangeTime.AddDays(LDetector2GuaranteePeriod);

        /// <summary>
        /// 试剂维护信息
        /// </summary>
        [Description("试剂维护信息")]
        public Dictionary<eQCD3900LiquidToAnsysisType, OneQCD3900LiquidParamInfo> LiquidParamInfos { get; private set; } = new Dictionary<eQCD3900LiquidToAnsysisType, OneQCD3900LiquidParamInfo>();

        /// <summary>
        /// 器件维护信息
        /// </summary>
        [Description("器件维护信息")]
        public Dictionary<eQCD3900ElementToAnsysisType, OneQCD3900ElementParamInfo> ElementParamInfos { get; private set; } = new Dictionary<eQCD3900ElementToAnsysisType, OneQCD3900ElementParamInfo>();

        #endregion

        #region 构造

        public QCD3900ElementLifeInfo()
        {
            foreach(eQCD3900LiquidToAnsysisType item in Enum.GetValues(typeof(eQCD3900LiquidToAnsysisType)))
            {
                LiquidParamInfos.Add(item, new OneQCD3900LiquidParamInfo(item.ToString(), (byte)item, false));
            }

            foreach(eQCD3900ElementToAnsysisType item in Enum.GetValues(typeof(eQCD3900ElementToAnsysisType)))
            {
                ElementParamInfos.Add(item, new OneQCD3900ElementParamInfo(item.ToString(), (byte)item));
            }
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 更新试剂寿命信息
        /// </summary>
        /// <param name="data"></param>
        public void UpdateLiqueValue(byte[] data, int startIndex)
        {
            if(data.Length < startIndex + 86)
            {
                throw new Exception("读取器件寿命信息回应数据不完整！");
            }

            ReagentAlarmLimit = DataConverter.GetInstance().ToInt32(data, startIndex);
            CompLifeAlarmLimit = DataConverter.GetInstance().ToInt32(data, startIndex + 2);

            // 解析时，按协议定义位置解析
            int i = 3;
            foreach(eQCD3900LiquidToAnsysisType item in Enum.GetValues(typeof(eQCD3900LiquidToAnsysisType)))
            {
                if(LiquidParamInfos.ContainsKey(item))
                {
                    LiquidParamInfos[item].UpdateLiqueInfo(data, startIndex, i++ * 4);
                }
            }

            WaterTubeChangeTime = DataConvertHelper.GetDateTimeFromUnixTimeSeconds(data, startIndex + 74);
            ReceiverRingChangeTime = DataConvertHelper.GetDateTimeFromUnixTimeSeconds(data, startIndex + 78);
            WTubeGuaranteePeriod = DataConverter.GetInstance().ToInt32(data, startIndex + 82);
            RRingGuaranteePeriod = DataConverter.GetInstance().ToInt32(data, startIndex + 84);
        }

        /// <summary>
        /// 更新器件寿命信息
        /// </summary>
        /// <param name="data"></param>
        public void UpdateDetectorValue(byte[] data, int startIndex)
        {
            if(data.Length < startIndex + 180)
            {
                throw new Exception("读取器件寿命信息回应数据不完整！");
            }

            LevelDetector1ChangeTime = DataConvertHelper.GetDateTimeFromUnixTimeSeconds(data, startIndex + 96);
            LevelDetector2ChangeTime = DataConvertHelper.GetDateTimeFromUnixTimeSeconds(data, startIndex + 100);
            LDetector1GuaranteePeriod = DataConverter.GetInstance().ToInt32(data, startIndex + 104);
            LDetector2GuaranteePeriod = DataConverter.GetInstance().ToInt32(data, startIndex + 106);

            // 解析时，按协议定义位置解析
            int i = 0;
            foreach(eQCD3900ElementToAnsysisType item in Enum.GetValues(typeof(eQCD3900ElementToAnsysisType)))
            {
                if(ElementParamInfos.ContainsKey(item))
                {
                    ElementParamInfos[item].UpdateDetectorInfo(data, startIndex, i++ * 4);
                }
            }
        }

        #endregion
    }

    /// <summary>
    /// QCD3900试剂参数信息
    /// </summary>

    public class OneQCD3900LiquidParamInfo
    {
        #region 字段属性

        #region 基础信息

        /// <summary>
        /// 试剂名称
        /// </summary>
        public readonly string Name;

        /// <summary>
        /// 是否是废液桶
        /// </summary>
        public readonly bool IsWasteLiquid;

        /// <summary>
        /// 对应起始寄存器地址
        /// </summary>
        public readonly byte StartIndex;

        #endregion

        #region 可读数据

        /// <summary>
        /// 余量
        /// </summary>
        [Description("余量")]
        public float Residual { get; private set; } = float.NaN;

        /// <summary>
        /// 总量
        /// </summary>
        [Description("总量")]
        public float Total { get; private set; } = float.NaN;

        /// <summary>
        /// 更换时间
        /// </summary>
        [Description("更换时间")]
        public DateTime ChangeTime { get; private set; } = DateTime.MinValue;

        /// <summary>
        /// 保质期
        /// </summary>
        [Description("保质期")]
        public int GuaranteePeriod { get; private set; } = -1;

        #endregion

        #endregion

        #region 构造

        public OneQCD3900LiquidParamInfo(string name, byte startIndex, bool isWasteLiquid = false)
        {
            Name = name;
            IsWasteLiquid = isWasteLiquid;
            StartIndex = startIndex;
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 更新试剂寿命信息
        /// </summary>
        /// <param name="data"></param>
        public void UpdateLiqueInfo(byte[] data, int startIndex, int dataIndex)
        {
            Residual = DataConverter.GetInstance().ToSingle(data, startIndex + dataIndex);
            Total = DataConverter.GetInstance().ToSingle(data, startIndex + dataIndex + 20);
            ChangeTime = DataConvertHelper.GetDateTimeFromUnixTimeSeconds(data, startIndex + dataIndex + 40);
            GuaranteePeriod = DataConverter.GetInstance().ToInt32(data, startIndex + dataIndex / 2 + 62);
        }

        #endregion
    }

    /// <summary>
    /// QCD3900器件参数信息
    /// </summary>

    public class OneQCD3900ElementParamInfo
    {
        #region 字段属性

        #region 基础信息

        /// <summary>
        /// 器件名称
        /// </summary>
        public readonly string Name;

        /// <summary>
        /// 对应起始寄存器地址
        /// </summary>
        public readonly byte StartIndex;

        #endregion

        #region 可读数据

        /// <summary>
        /// 已用次数/已用时长
        /// </summary>
        [Description("已用次数/已用时长")]
        public float Residual { get; private set; } = float.NaN;

        /// <summary>
        /// 总次数/总时长
        /// </summary>
        [Description("总次数/总时长")]
        public float Total { get; private set; } = float.NaN;

        /// <summary>
        /// 更换时间
        /// </summary>
        [Description("更换时间")]
        public DateTime ChangeTime { get; private set; } = DateTime.MinValue;

        /// <summary>
        /// 保质期
        /// </summary>
        [Description("保质期")]
        public int GuaranteePeriod { get; private set; } = -1;

        #endregion

        #endregion

        #region 构造

        public OneQCD3900ElementParamInfo(string name, byte startIndex)
        {
            Name = name;
            StartIndex = startIndex;
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 更新器件寿命信息
        /// </summary>
        /// <param name="data"></param>
        public void UpdateDetectorInfo(byte[] data, int startIndex, int dataIndex)
        {
            Total = DataConverter.GetInstance().ToInt64(data, startIndex + dataIndex);
            Residual = DataConverter.GetInstance().ToInt64(data, startIndex + dataIndex + 48);
            ChangeTime = DataConvertHelper.GetDateTimeFromUnixTimeSeconds(data, startIndex + dataIndex + 108);
            GuaranteePeriod = DataConverter.GetInstance().ToInt32(data, startIndex + dataIndex / 2 + 156);
        }

        #endregion
    }
}