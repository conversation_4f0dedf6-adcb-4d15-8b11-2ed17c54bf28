﻿using System;
using System.Collections.Generic;
using System.Linq;
using Fpi.Communication.Protocols;
using Fpi.Devices;
using Fpi.HB.Business.Protocols.Helper;
using Fpi.Operations.Config;
using Fpi.WMS3000.Equipment;
using Fpi.WMS3000.Equipment.Config;
using Fpi.WMS3000.Equipment.Interface;
using Fpi.WMS3000.Equipment.QCD3900;
using Fpi.WMS3000.Equipment.SIA3900;
using Fpi.WMS3000.Equipment.WCS3900;
using Fpi.WMS3000.Remote.GJDBS;
using Fpi.WMS3000.Remote.GJDBS.Config;
using Fpi.WMS3000.Remote.GJDBS.GJDBSDataFrame;
using Fpi.WMS3000.SystemConfig;

namespace Fpi.WMS3000.Remote.FPISZH
{
    /// <summary>
    /// 谱育科技数智化水站数据传输协议-数据接收器
    /// </summary>
    public class FPISZHReceiver : GJDBSReceiver
    {
        #region 数据解析\处理

        /// <summary>
        /// 重写部分反控处理逻辑
        /// </summary>
        /// <param name="dataFrm"></param>
        /// <param name="exeRtn"></param>
        /// <returns></returns>
        protected override bool DealRequestCommand(GJDBSCommand dataFrm, ref eExeRtn exeRtn)
        {
            try
            {
                switch(dataFrm.CN)
                {
                    case "2067": // 五参数水样比对数据
                        CustomDataAddendum(dataFrm, eUploadDataType.五参数水样比对数据, ref exeRtn);
                        break;

                    case "2068": // 取盲样测试数据
                        CustomDataAddendum(dataFrm, eUploadDataType.盲样测试, ref exeRtn);
                        break;

                    case "2069": // 取空白测试数据
                        CustomDataAddendum(dataFrm, eUploadDataType.空白测试, ref exeRtn);
                        break;

                    case "2070": // 取任意浓度核查数据
                        CustomDataAddendum(dataFrm, eUploadDataType.任意浓度核查, ref exeRtn);
                        break;

                    case "2071": // 取多点线性核查数据
                        CustomDataAddendum(dataFrm, eUploadDataType.多点线性核查, ref exeRtn);
                        break;

                    case "3087": //  启动单台仪表测量
                                 // 待机状态下执行
                        if(!IsDeviceUse(dataFrm))
                        {
                            StartDeviceControl(dataFrm, eMeasureDeviceOperType.Measure, ref exeRtn);
                        }
                        else
                        {
                            exeRtn = eExeRtn.系统繁忙不能执行;
                        }
                        break;

                    case "3088": //  启动单台仪表空白测试
                                 // 待机状态下执行
                        if(!IsDeviceUse(dataFrm))
                        {
                            StartDeviceControl(dataFrm, eMeasureDeviceOperType.BlankTest, ref exeRtn);
                        }
                        else
                        {
                            exeRtn = eExeRtn.系统繁忙不能执行;
                        }
                        break;

                    case "3089": //  启动单台仪表初始化
                                 // 待机状态下执行
                        if(!IsDeviceUse(dataFrm))
                        {
                            StartDeviceControl(dataFrm, eMeasureDeviceOperType.Initialize, ref exeRtn);
                        }
                        else
                        {
                            exeRtn = eExeRtn.系统繁忙不能执行;
                        }
                        break;

                    case "3090": //  启动单台仪表停止测试
                                 // 待机状态下执行
                        if(!IsDeviceUse(dataFrm))
                        {
                            StartDeviceControl(dataFrm, eMeasureDeviceOperType.StopMeasture, ref exeRtn);
                        }
                        else
                        {
                            exeRtn = eExeRtn.系统繁忙不能执行;
                        }
                        break;

                    case "3091": //  启动单台仪表重启
                                 // 待机状态下执行
                        if(!IsDeviceUse(dataFrm))
                        {
                            StartDeviceControl(dataFrm, eMeasureDeviceOperType.ReStart, ref exeRtn);
                        }
                        else
                        {
                            exeRtn = eExeRtn.系统繁忙不能执行;
                        }
                        break;

                    case "3092": //  启动单台仪表标定
                                 // 待机状态下执行
                        if(!IsDeviceUse(dataFrm))
                        {
                            StartDeviceControl(dataFrm, eMeasureDeviceOperType.Demarcate, ref exeRtn);
                        }
                        else
                        {
                            exeRtn = eExeRtn.系统繁忙不能执行;
                        }
                        break;

                    case "3093": //  启动单台仪表试剂导入
                                 // 待机状态下执行
                        if(!IsDeviceUse(dataFrm))
                        {
                            StartDeviceControl(dataFrm, eMeasureDeviceOperType.ReagentIntroduct, ref exeRtn);
                        }
                        else
                        {
                            exeRtn = eExeRtn.系统繁忙不能执行;
                        }
                        break;

                    case "3094": //  启动单台仪表系统排空
                                 // 待机状态下执行
                        if(!IsDeviceUse(dataFrm))
                        {
                            StartDeviceControl(dataFrm, eMeasureDeviceOperType.Empty, ref exeRtn);
                        }
                        else
                        {
                            exeRtn = eExeRtn.系统繁忙不能执行;
                        }
                        break;

                    case "3095": //  启动单台仪表检测室清洗
                                 // 待机状态下执行
                        if(!IsDeviceUse(dataFrm))
                        {
                            StartDeviceControl(dataFrm, eMeasureDeviceOperType.Wash, ref exeRtn);
                        }
                        else
                        {
                            exeRtn = eExeRtn.系统繁忙不能执行;
                        }
                        break;

                    case "3096": //  启动单台仪表信号调整
                                 // 待机状态下执行
                        if(!IsDeviceUse(dataFrm))
                        {
                            StartDeviceControl(dataFrm, eMeasureDeviceOperType.SignalTransformate, ref exeRtn);
                        }
                        else
                        {
                            exeRtn = eExeRtn.系统繁忙不能执行;
                        }
                        break;

                    case "3097": //  启动单台仪表一键维护
                                 // 待机状态下执行
                        if(!IsDeviceUse(dataFrm))
                        {
                            StartDeviceControl(dataFrm, eMeasureDeviceOperType.Maintain, ref exeRtn);
                        }
                        else
                        {
                            exeRtn = eExeRtn.系统繁忙不能执行;
                        }
                        break;

                    case "3098": //  启动单台仪表量程校准
                                 // 待机状态下执行
                        if(!IsDeviceUse(dataFrm))
                        {
                            StartDeviceControl(dataFrm, eMeasureDeviceOperType.RangeCalibration, ref exeRtn);
                        }
                        else
                        {
                            exeRtn = eExeRtn.系统繁忙不能执行;
                        }
                        break;

                    case "3099": //  启动单台仪表关键器件自检流程
                                 // 待机状态下执行
                        if(!IsDeviceUse(dataFrm))
                        {
                            StartDeviceCheck(dataFrm, ref exeRtn);
                        }
                        else
                        {
                            exeRtn = eExeRtn.系统繁忙不能执行;
                        }
                        break;

                    case "3100": // 远程切换WCS3900运行模式
                                 // 待机状态下执行
                        if(!IsWCS3900Busying())
                        {
                            SetWCS3900RunModel(dataFrm, ref exeRtn);
                        }
                        else
                        {
                            exeRtn = eExeRtn.系统繁忙不能执行;
                        }
                        break;

                    case "3101": // 五参数仪表复位排空
                                 // 待机状态下执行
                        if(!IsWCS3900Busying())
                        {
                            StartWCS3900Control(dataFrm, eWCSOperType.复位排空清洗, ref exeRtn);
                        }
                        else
                        {
                            exeRtn = eExeRtn.系统繁忙不能执行;
                        }
                        break;

                    case "3102": // 五参数仪表紧急停止
                        StartWCS3900Control(dataFrm, eWCSOperType.紧急停止, ref exeRtn);
                        break;

                    case "3103": // 五参数仪表标定
                        if(!IsWCS3900Busying())
                        {
                            StartWCS3900Control(dataFrm, eWCSOperType.标定, ref exeRtn);
                        }
                        else
                        {
                            exeRtn = eExeRtn.系统繁忙不能执行;
                        }
                        break;

                    case "4001": // 启动单台仪表多点线性核查
                        // 待机状态下执行
                        if(!IsDeviceUse(dataFrm))
                        {
                            StartSIA3900FlowControl(dataFrm, eMeasureDeviceOperType.Linearity, ref exeRtn);
                        }
                        else
                        {
                            exeRtn = eExeRtn.系统繁忙不能执行;
                        }
                        break;

                    case "4002": // 启动单台仪表盲样测试
                        // 待机状态下执行
                        if(!IsDeviceUse(dataFrm))
                        {
                            StartSIA3900FlowControl(dataFrm, eMeasureDeviceOperType.BlindCheck, ref exeRtn);
                        }
                        else
                        {
                            exeRtn = eExeRtn.系统繁忙不能执行;
                        }
                        break;

                    case "4003": // 启动单台仪表任意浓度核查
                        // 待机状态下执行
                        if(!IsDeviceUse(dataFrm))
                        {
                            StartSIA3900FlowControl(dataFrm, eMeasureDeviceOperType.ArbitraryConCheck, ref exeRtn);
                        }
                        else
                        {
                            exeRtn = eExeRtn.系统繁忙不能执行;
                        }
                        break;

                    // 配置实现
                    case "4004": // 启动五参数仪表测量
                                 // 待机状态下执行
                        if(IsSoftFree())
                        {
                            ActiveOtherOperation(dataFrm, ref exeRtn);
                        }
                        else
                        {
                            exeRtn = eExeRtn.系统繁忙不能执行;
                        }
                        break;

                    // 配置实现
                    case "4005": // 启动五参数仪表核查
                    case "4006": // 启动五参数仪表水样比对
                                 // 待机状态下执行
                        if(!IsWCS3900Busying())
                        {
                            StartWCS3900FlowControl(dataFrm, ref exeRtn);
                        }
                        else
                        {
                            exeRtn = eExeRtn.系统繁忙不能执行;
                        }
                        break;

                    case "4021": // 门禁控制
                        DoorControl(dataFrm, ref exeRtn);
                        break;

                    case "4022": // 空调控制
                        AirConditionControl(dataFrm, ref exeRtn);
                        break;

                    case "4023": // 排风扇控制
                        AirVentilatorControl(dataFrm, ref exeRtn);
                        break;

                    case "4024": // 灯控制
                        LampControl(dataFrm, ref exeRtn);
                        break;

                    // 非定制功能，走标准处理方式
                    default:
                        return false;
                }
            }
            catch
            {
                exeRtn = eExeRtn.执行失败但不知道原因;
            }

            return true;
        }

        #endregion

        #region 数据补遗

        /// <summary>
        /// 数据补遗
        /// </summary>
        /// <param name="dataFrm"></param>
        /// <param name="type"></param>
        /// <param name="exeRtn"></param>
        private void CustomDataAddendum(GJDBSCommand dataFrm, eUploadDataType type, ref eExeRtn exeRtn)
        {
            if(null != dataFrm.CP && !string.IsNullOrEmpty(dataFrm.CP.BeginTime) && !string.IsNullOrEmpty(dataFrm.CP.EndTime))
            {
                try
                {
                    DateTime begin = DateTime.ParseExact(dataFrm.CP.BeginTime, GJDBSProtocolDesc.DateTimeFormat, null);
                    DateTime end = DateTime.ParseExact(dataFrm.CP.EndTime, GJDBSProtocolDesc.DateTimeFormat, null);
                    try
                    {
                        SendAddendumData(dataFrm, type, begin, end);
                    }
                    catch
                    {
                        exeRtn = eExeRtn.执行失败但不知道原因;
                    }
                }
                catch
                {
                    exeRtn = eExeRtn.命令请求条件错误;
                }
            }
            else
            {
                exeRtn = eExeRtn.命令请求条件错误;
            }
        }

        /// <summary>
        /// 发送补遗数据
        /// </summary>
        /// <param name="type"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        private void SendAddendumData(GJDBSCommand dataFrm, eUploadDataType type, DateTime startTime, DateTime endTime)
        {
            if(_desc != null)
            {
                try
                {
                    // 待发送数据表
                    var cmdList = new List<GJDBSCommand>();
                    switch(type)
                    {
                        case eUploadDataType.五参数水样比对数据:
                            cmdList.AddRange(FPISZHHelper.BuildFiveParamCheckData(_desc, startTime, endTime, eWCS3900DetailDataType.水样比对数据, eGetDataCount.所有数据, dataFrm.QN));
                            break;

                        case eUploadDataType.盲样测试:
                            cmdList.AddRange(FPISZHHelper.BuildBlindCheckData(_desc, startTime, endTime, eGetDataCount.所有数据, dataFrm.QN));
                            break;

                        case eUploadDataType.空白测试:
                            cmdList.AddRange(FPISZHHelper.BuildBlankTestData(_desc, startTime, endTime, eGetDataCount.所有数据, dataFrm.QN));
                            break;

                        case eUploadDataType.任意浓度核查:
                            cmdList.AddRange(FPISZHHelper.BuildArbitraryCheckData(_desc, startTime, endTime, eGetDataCount.所有数据, dataFrm.QN));
                            break;

                        case eUploadDataType.多点线性核查:
                            cmdList.AddRange(FPISZHHelper.BuildMultiPointCheckData(_desc, startTime, endTime, eGetDataCount.所有数据, dataFrm.QN));
                            break;

                        default:
                            throw new Exception("非定制部分数据类型，请走标准协议处理！");
                    }

                    // 发送数据
                    foreach(GJDBSCommand cmd in cmdList)
                    {
                        // 数据不需回应
                        cmd.Flag = dataFrm.Flag - 1;
                        _desc.SendDataFrame(cmd);
                    }
                }
                catch(Exception e)
                {
                    ProtocolLogHelper.ShowMsg($"上传{type}类型数据出错:{e.Message}");
                }
            }
            else
            {
                throw new Exception("协议描述器为空！");
            }
        }

        #endregion

        #region 仪表反控

        #region SIA3900

        /// <summary>
        /// 启动SIA3900仪表关键器件自检流程
        /// </summary>
        /// <param name="dataCmd"></param>
        /// <param name="opType"></param>
        /// <param name="exeRtn"></param>
        private void StartDeviceCheck(GJDBSCommand dataCmd, ref eExeRtn exeRtn)
        {
            if(dataCmd.CP == null || string.IsNullOrEmpty(dataCmd.CP.PolId) || dataCmd.CP.OpId < 1 || dataCmd.CP.OpId > 9)
            {
                exeRtn = eExeRtn.命令请求条件错误;
                return;
            }

            try
            {
                Device dev = GetTargetDevByPolid(dataCmd.CP.PolId);

                if(dev is SIA3900Equipment measureDev)
                {
                    measureDev.WriteDeviceCheck((eSIA3900DeviceCheckType)dataCmd.CP.OpId);
                }
                else
                {
                    exeRtn = eExeRtn.命令请求条件错误;
                }
            }
            catch
            {
                exeRtn = eExeRtn.执行失败但不知道原因;
            }
        }

        /// <summary>
        /// 启动单个流程反控操作
        /// </summary>
        /// <param name="dataCmd"></param>
        /// <param name="exeRtn"></param>
        private void StartSIA3900FlowControl(GJDBSCommand dataCmd, eMeasureDeviceOperType optype, ref eExeRtn exeRtn)
        {
            if(dataCmd.CP == null || string.IsNullOrEmpty(dataCmd.CP.PolId))
            {
                exeRtn = eExeRtn.命令请求条件错误;
                return;
            }
            // 多点线性
            if(optype == eMeasureDeviceOperType.Linearity)
            {
                if(float.IsNaN(dataCmd.CP.AStandardValue) || float.IsNaN(dataCmd.CP.BStandardValue) || float.IsNaN(dataCmd.CP.CStandardValue) || float.IsNaN(dataCmd.CP.DStandardValue))
                {
                    exeRtn = eExeRtn.命令请求条件错误;
                    return;
                }
            }
            // 任意浓度核查
            else if(optype == eMeasureDeviceOperType.ArbitraryConCheck)
            {
                if(float.IsNaN(dataCmd.CP.StandardValue))
                {
                    exeRtn = eExeRtn.命令请求条件错误;
                    return;
                }
            }
            // 盲样核查
            else if(optype == eMeasureDeviceOperType.BlindCheck)
            {
                // 可不下发浓度
                //if(float.IsNaN(dataCmd.CP.StandardValue))
                //{
                //    exeRtn = eExeRtn.命令请求条件错误;
                //    return;
                //}
            }

            // 控制参数赋值给QCD设备
            if(GetTargetQCDDevByPolid(dataCmd.CP.PolId) is IQCDeviceOperation qcdDevice && qcdDevice.GetEquipParam() is QCD3900MeasureParam qcdParam)
            {
                if(optype == eMeasureDeviceOperType.Linearity)
                {
                    qcdParam.CheckAConfigValue = dataCmd.CP.AStandardValue;
                    qcdParam.CheckBConfigValue = dataCmd.CP.BStandardValue;
                    qcdParam.CheckCConfigValue = dataCmd.CP.CStandardValue;
                    qcdParam.CheckDConfigValue = dataCmd.CP.DStandardValue;
                }
                // 盲样核查
                else if(optype == eMeasureDeviceOperType.BlindCheck)
                {
                    qcdParam.BlindCheckConfigValue = dataCmd.CP.StandardValue;
                }
                // 任意浓度核查
                else if(optype == eMeasureDeviceOperType.ArbitraryConCheck)
                {
                    qcdParam.ArbitraryConCheckConfigValue = dataCmd.CP.StandardValue;
                }
            }
            else
            {
                exeRtn = eExeRtn.系统故障;
                return;
            }

            try
            {
                string opid = GetTargetFlow(dataCmd.CN, dataCmd.CP.PolId);
                if(!string.IsNullOrEmpty(opid))
                {
                    // 启动流程，传入被控因子id
                    OperationManager.GetInstance().StartDo(opid, "self", true, dataCmd.CP.PolId);
                }
                else
                {
                    exeRtn = eExeRtn.系统故障;
                }

            }
            catch
            {
                exeRtn = eExeRtn.执行失败但不知道原因;
            }
        }

        #endregion

        #region 五参数

        /// <summary>
        /// 检测WMS3900设备是否繁忙中
        /// </summary>
        /// <returns></returns>
        protected bool IsWCS3900Busying()
        {
            bool isUse = false;
            WCS3900Equip wcs3900Equip = DeviceManager.GetInstance().GetDeviceListUsedByType(eDeviceType.WMS).OfType<WCS3900Equip>().FirstOrDefault();
            if(wcs3900Equip != null)
            {
                isUse = wcs3900Equip.DeviceStateParams.FlowState != eWCSOperType.空闲;
            }

            return isUse;
        }

        /// <summary>
        /// 设置WCS3900仪表运行模式
        /// </summary>
        /// <param name="dataCmd"></param>
        /// <param name="exeRtn"></param>
        private void SetWCS3900RunModel(GJDBSCommand dataCmd, ref eExeRtn exeRtn)
        {
            if(dataCmd.CP == null || !Enum.IsDefined(typeof(eWCS3900WorkModel), (int)dataCmd.CP.RunMode))
            {
                exeRtn = eExeRtn.命令请求条件错误;
            }

            try
            {
                WCS3900Equip wcs3900Equip = DeviceManager.GetInstance().GetDeviceListUsedByType(eDeviceType.WMS).OfType<WCS3900Equip>().FirstOrDefault();

                if(wcs3900Equip != null)
                {
                    wcs3900Equip.SwitchWorkModel((eWCS3900WorkModel)dataCmd.CP.RunMode);
                }
                else
                {
                    exeRtn = eExeRtn.命令请求条件错误;
                }
            }
            catch
            {
                exeRtn = eExeRtn.执行失败但不知道原因;
            }
        }

        /// <summary>
        /// 启动WCS3900仪表反控操作
        /// </summary>
        /// <param name="dataCmd"></param>
        /// <param name="opType"></param>
        /// <param name="exeRtn"></param>
        private void StartWCS3900Control(GJDBSCommand dataCmd, eWCSOperType opType, ref eExeRtn exeRtn)
        {
            if(opType == eWCSOperType.标定)
            {
                if(dataCmd.CP == null || string.IsNullOrEmpty(dataCmd.CP.PolIdList))
                {
                    exeRtn = eExeRtn.命令请求条件错误;
                    return;
                }
            }

            try
            {
                WCS3900Equip wcs3900Equip = DeviceManager.GetInstance().GetDeviceListUsedByType(eDeviceType.WMS).OfType<WCS3900Equip>().FirstOrDefault();

                if(wcs3900Equip != null)
                {
                    Dictionary<eWCSNodeType, WCS39900OperParams> calibrateOperParams = null;
                    // 标定流程要挂参数
                    if(opType == eWCSOperType.标定)
                    {
                        calibrateOperParams = new Dictionary<eWCSNodeType, WCS39900OperParams>
                        {
                            { eWCSNodeType.w01001, new WCS39900OperParams(dataCmd.CP.PolIdList.Contains(eWCSNodeType.w01001.ToString()), 0x00) },
                            { eWCSNodeType.w01014, new WCS39900OperParams(dataCmd.CP.PolIdList.Contains(eWCSNodeType.w01014.ToString()), 0x00) },
                            { eWCSNodeType.w01009, new WCS39900OperParams(dataCmd.CP.PolIdList.Contains(eWCSNodeType.w01009.ToString()), 0x00) },
                            { eWCSNodeType.w01003, new WCS39900OperParams(dataCmd.CP.PolIdList.Contains(eWCSNodeType.w01003.ToString()), 0x00) },
                        };
                    }
                    wcs3900Equip.StartOper(opType, calibrateOperParams);
                }
                else
                {
                    exeRtn = eExeRtn.系统故障;
                }
            }
            catch
            {
                exeRtn = eExeRtn.执行失败但不知道原因;
            }
        }

        /// <summary>
        /// 启动WCS3900仪表流程反控操作
        /// </summary>
        /// <param name="dataCmd"></param>
        /// <param name="exeRtn"></param>

        private void StartWCS3900FlowControl(GJDBSCommand dataCmd, ref eExeRtn exeRtn)
        {
            if(dataCmd.CP == null || dataCmd.CP.PolCmdParameter == null || dataCmd.CP.PolCmdParameter.Count == 0)
            {
                exeRtn = eExeRtn.命令请求条件错误;
                return;
            }

            try
            {
                // 未配置对应协议的反控指令组
                if(RemoteControlManager.GetInstance().remoteControls.FindNode(this.impProtocol) is RemoteControl rControl)
                {
                    // 对应操作编号
                    string opId = string.Empty;
                    foreach(RemoteFunction rFun in rControl.remoteFunctions)
                    {
                        if(dataCmd.CN == rFun.CN)
                        {
                            opId = rFun.OperationId;
                            break;
                        }
                    }
                    // 未配置对应命令码
                    if(!string.IsNullOrEmpty(opId))
                    {
                        exeRtn = eExeRtn.系统故障;
                    }

                    // 组装控制参数
                    Dictionary<eWCSNodeType, WCS39900OperParams> operParams = new Dictionary<eWCSNodeType, WCS39900OperParams>()
                    {
                        { eWCSNodeType.w01001, new WCS39900OperParams(false, 0x00) },
                        { eWCSNodeType.w01014, new WCS39900OperParams(false,0x00) },
                        { eWCSNodeType.w01009, new WCS39900OperParams(false, 0x00) },
                        { eWCSNodeType.w01003, new WCS39900OperParams(false, 0x00) },
                        { eWCSNodeType.w01010, new WCS39900OperParams(false, 0x00) }
                    };

                    // 遍历平台下发内容
                    foreach(var polParam in dataCmd.CP.PolCmdParameter)
                    {
                        if(Enum.TryParse(polParam.PolId, out eWCSNodeType wcsNodeType))
                        {
                            var param = operParams[wcsNodeType];
                            param.IsEnable = true;
                            param.ParamType = (byte)polParam.FlowId;
                        }
                    }

                    // 启动流程，传入被控参数
                    OperationManager.GetInstance().StartDo(opId, "self", true, operParams);
                }
                else
                {
                    exeRtn = eExeRtn.系统故障;
                }
            }
            catch
            {
                exeRtn = eExeRtn.执行失败但不知道原因;
            }
        }

        #endregion

        #endregion

        #region 动环控制

        /// <summary>
        /// 门禁控制
        /// </summary>
        /// <param name="dataCmd"></param>
        /// <param name="exeRtn"></param>
        private void DoorControl(GJDBSCommand dataCmd, ref eExeRtn exeRtn)
        {
            if(dataCmd.CP == null || (dataCmd.CP.DoorState != 0 && dataCmd.CP.DoorState != 1))
            {
                exeRtn = eExeRtn.命令请求条件错误;
                return;
            }

            try
            {
                var netEntrance = ExterEquipConfigManager.GetInstance().EntranceSelect.MainEntran;
                if(netEntrance != null)
                {
                    if(dataCmd.CP.DoorState == 1)
                    {
                        netEntrance.OpenDoorAsync().Wait();
                        //netEntrance.OpenDoor();
                    }
                    // 不支持远程关门
                    else
                    {
                        exeRtn = eExeRtn.系统故障;
                        //netEntrance.CloseDoor();
                    }
                }
                else
                {
                    exeRtn = eExeRtn.系统故障;
                }
            }
            catch
            {
                exeRtn = eExeRtn.执行失败但不知道原因;
            }
        }

        /// <summary>
        /// 空调控制
        /// </summary>
        /// <param name="dataCmd"></param>
        /// <param name="exeRtn"></param>
        private void AirConditionControl(GJDBSCommand dataCmd, ref eExeRtn exeRtn)
        {
            // 三个控制参数都未输入
            if(dataCmd.CP == null || ((dataCmd.CP.ACStatus < 0 && dataCmd.CP.ACStatus > 1) && (dataCmd.CP.ACMode < 0 && dataCmd.CP.ACMode > 3) && (float.IsNaN(dataCmd.CP.ACTemp))))
            {
                exeRtn = eExeRtn.命令请求条件错误;
                return;
            }

            try
            {
                var airEquip = ExterEquipConfigManager.GetInstance().DeviceSelect.AirControlDevice as JDRKRSEquip;
                if(airEquip != null)
                {
                    // 开关机
                    if(dataCmd.CP.ACStatus >= 0 && dataCmd.CP.ACStatus <= 1)
                    {
                        switch(dataCmd.CP.ACMode)
                        {
                            case 0:
                                airEquip.SetACState(eACState.关机);
                                break;
                            case 1:
                                airEquip.SetACState(eACState.开机);
                                break;
                        }
                    }

                    // 模式切换
                    if(dataCmd.CP.ACMode >= 0 && dataCmd.CP.ACMode <= 3)
                    {
                        switch(dataCmd.CP.ACMode)
                        {
                            case 0:
                                airEquip.SetACState(eACState.通风);
                                break;
                            case 1:
                                airEquip.SetACState(eACState.制冷);
                                break;
                            case 2:
                                airEquip.SetACState(eACState.制热);
                                break;
                            case 3:
                                airEquip.SetACState(eACState.除湿);
                                break;
                        }
                    }

                    // 温度设置
                    if(!float.IsNaN(dataCmd.CP.ACTemp))
                    {
                        airEquip.SetACTemp((int)dataCmd.CP.ACTemp);
                    }
                }
                else
                {
                    exeRtn = eExeRtn.系统故障;
                }
            }
            catch
            {
                exeRtn = eExeRtn.执行失败但不知道原因;
            }
        }

        /// <summary>
        /// 排风扇控制
        /// </summary>
        /// <param name="dataCmd"></param>
        /// <param name="exeRtn"></param>
        private void AirVentilatorControl(GJDBSCommand dataCmd, ref eExeRtn exeRtn)
        {
            if(dataCmd.CP == null || (dataCmd.CP.FanState != 0 && dataCmd.CP.FanState != 1))
            {
                exeRtn = eExeRtn.命令请求条件错误;
                return;
            }

            try
            {
                var airVentilatorNode = ExterEquipConfigManager.GetInstance().StationEnvConfigInfo.AirVentilatorNode;
                if(airVentilatorNode != null)
                {
                    // 待设置状态
                    bool setsState = dataCmd.CP.FanState == 1;

                    // 读取当前状态
                    bool oldState = airVentilatorNode.GetValue();

                    // 状态切换时进行控制，输出日志
                    if(oldState != setsState)
                    {
                        // 设置开关量状态
                        airVentilatorNode.SetValue(setsState);
                        try
                        {
                            // 写开关量输出
                            bool result = SystemManager.GetInstance().WriteOneSwitch(airVentilatorNode);

                            if(!result)
                            {
                                // 恢复开关量状态
                                airVentilatorNode.SetValue(!setsState);
                            }
                        }
                        catch
                        {
                            // 恢复开关量状态
                            airVentilatorNode.SetValue(!setsState);
                            throw;
                        }
                    }
                }
                else
                {
                    exeRtn = eExeRtn.系统故障;
                }
            }
            catch
            {
                exeRtn = eExeRtn.执行失败但不知道原因;
            }
        }

        /// <summary>
        /// 灯控制
        /// </summary>
        /// <param name="dataCmd"></param>
        /// <param name="exeRtn"></param>
        private void LampControl(GJDBSCommand dataCmd, ref eExeRtn exeRtn)
        {
            if(dataCmd.CP == null || (dataCmd.CP.LampState != 0 && dataCmd.CP.LampState != 1))
            {
                exeRtn = eExeRtn.命令请求条件错误;
                return;
            }

            try
            {
                var lampNode = ExterEquipConfigManager.GetInstance().StationEnvConfigInfo.LampNode;
                if(lampNode != null)
                {
                    // 待设置状态
                    bool setsState = dataCmd.CP.LampState == 1;

                    // 读取当前状态
                    bool oldState = lampNode.GetValue();

                    // 状态切换时进行控制，输出日志
                    if(oldState != setsState)
                    {
                        // 设置开关量状态
                        lampNode.SetValue(setsState);
                        try
                        {
                            // 写开关量输出
                            bool result = SystemManager.GetInstance().WriteOneSwitch(lampNode);

                            if(!result)
                            {
                                // 恢复开关量状态
                                lampNode.SetValue(!setsState);
                            }
                        }
                        catch
                        {
                            // 恢复开关量状态
                            lampNode.SetValue(!setsState);
                            throw;
                        }
                    }
                }
                else
                {
                    exeRtn = eExeRtn.系统故障;
                }
            }
            catch
            {
                exeRtn = eExeRtn.执行失败但不知道原因;
            }
        }

        #endregion
    }
}