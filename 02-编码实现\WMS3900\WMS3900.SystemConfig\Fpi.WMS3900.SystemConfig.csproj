﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{74B5BD59-A90B-4F35-8F41-74A54A290940}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Fpi.WMS3000.SystemConfig</RootNamespace>
    <AssemblyName>Fpi.WMS3900.SystemConfig</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
    <SccProjectName>
    </SccProjectName>
    <SccLocalPath>
    </SccLocalPath>
    <SccAuxPath>
    </SccAuxPath>
    <SccProvider>
    </SccProvider>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\..\Product\Debug\bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>..\..\Product\Release\bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="HZH_Controls, Version=1.5.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\FpiDLL\HZH_Controls.dll</HintPath>
    </Reference>
    <Reference Include="itextsharp">
      <HintPath>..\FpiDLL\itextsharp.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\FpiDLL\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="OpenCvSharp">
      <HintPath>..\FpiDLL\OpenCvSharp.dll</HintPath>
    </Reference>
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
    <Reference Include="SunnyUI, Version=3.7.2.0, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\FpiDLL\SunnyUI.dll</HintPath>
    </Reference>
    <Reference Include="SunnyUI.Common, Version=3.7.2.0, Culture=neutral, PublicKeyToken=5a271fb7ba597231, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\FpiDLL\SunnyUI.Common.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Design" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xaml" />
    <Reference Include="System.Xml" />
    <Reference Include="UIAutomationProvider" />
    <Reference Include="WindowsBase" />
    <Reference Include="WindowsFormsIntegration" />
    <Reference Include="WinFormsUI, Version=2.3.3505.27065, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\FpiDLL\WinFormsUI.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="CustomTask\ImagePatrolTask.cs" />
    <Compile Include="DeviceUsageStatistics\Config\EquipUsageInfo.cs" />
    <Compile Include="DeviceUsageStatistics\UI\FrmEquipUsageInfoModify.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DeviceUsageStatistics\UI\FrmEquipUsageInfoModify.Designer.cs">
      <DependentUpon>FrmEquipUsageInfoModify.cs</DependentUpon>
    </Compile>
    <Compile Include="DeviceUsageStatistics\UI\FrmDeviceUsageInfoModify.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DeviceUsageStatistics\UI\FrmDeviceUsageInfoModify.Designer.cs">
      <DependentUpon>FrmDeviceUsageInfoModify.cs</DependentUpon>
    </Compile>
    <Compile Include="DeviceUsageStatistics\UI\UC\UC_OneEquipUsageInfo.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="DeviceUsageStatistics\UI\UC\UC_OneEquipUsageInfo.Designer.cs">
      <DependentUpon>UC_OneEquipUsageInfo.cs</DependentUpon>
    </Compile>
    <Compile Include="DeviceUsageStatistics\UI\UC_AllCameraUsageInfos.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="DeviceUsageStatistics\UI\UC_AllCameraUsageInfos.Designer.cs">
      <DependentUpon>UC_AllCameraUsageInfos.cs</DependentUpon>
    </Compile>
    <Compile Include="DeviceUsageStatistics\UI\UC_AllEquipUsageInfos.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="DeviceUsageStatistics\UI\UC_AllEquipUsageInfos.Designer.cs">
      <DependentUpon>UC_AllEquipUsageInfos.cs</DependentUpon>
    </Compile>
    <Compile Include="ImagePatrol\Config\Base\ImageUnitSmartPatrolBase.cs" />
    <Compile Include="ImagePatrol\Config\Base\ImageUnitPatrolResultBase.cs" />
    <Compile Include="ImagePatrol\Config\ImagePatrolManager.cs" />
    <Compile Include="ImagePatrol\Config\OldImagePatrolResult.cs" />
    <Compile Include="ImagePatrol\Config\ImagePatrolResult.cs" />
    <Compile Include="ImagePatrol\Config\PatrolDetail\FiveParamFlowPoolPatrol.cs" />
    <Compile Include="ImagePatrol\Config\PatrolDetail\FiveParamBucketPatrol.cs" />
    <Compile Include="ImagePatrol\Config\PatrolDetail\CodMnNH4EquipPatrol.cs" />
    <Compile Include="ImagePatrol\Config\PatrolDetail\TPTNEquipPatrol.cs" />
    <Compile Include="ImagePatrol\Config\PatrolDetail\CodMnNH4QCDPatrol.cs" />
    <Compile Include="ImagePatrol\Config\PatrolDetail\TPTNQCDPatrol.cs" />
    <Compile Include="ImagePatrol\Config\PatrolDetail\PipePatrol.cs" />
    <Compile Include="ImagePatrol\Config\PatrolDetail\SandSinkPatrol.cs" />
    <Compile Include="ImagePatrol\Config\ResultDetail\CodMnNH4QCDPatrolResult.cs" />
    <Compile Include="ImagePatrol\Config\ResultDetail\FiveParamFlowPoolPatrolResult.cs" />
    <Compile Include="ImagePatrol\Config\ResultDetail\StationInsidePatrolResult.cs" />
    <Compile Include="ImagePatrol\Config\ResultDetail\StationOutsidePatrolResult.cs" />
    <Compile Include="ImagePatrol\Config\ResultDetail\TPTNEquipPatrolResult.cs" />
    <Compile Include="ImagePatrol\Config\ResultDetail\TPTNQCDPatrolResult.cs" />
    <Compile Include="ImagePatrol\Config\ResultDetail\FiveParamBucketPatrolResult.cs" />
    <Compile Include="ImagePatrol\Config\ResultDetail\CodMnNH4EquipPatrolResult.cs" />
    <Compile Include="ImagePatrol\Config\ResultDetail\SandSinkPatrolResult.cs" />
    <Compile Include="ImagePatrol\Config\ResultDetail\PipePatrolResult.cs" />
    <Compile Include="ImagePatrol\Config\ResultDetail\WaterPointPatrolResult.cs" />
    <Compile Include="ImagePatrol\Enums.cs" />
    <Compile Include="ImagePatrol\ImagePatrolLogHelper.cs" />
    <Compile Include="ImagePatrol\UI\FrmImagePatrolCtrl.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ImagePatrol\UI\FrmImagePatrolCtrl.Designer.cs">
      <DependentUpon>FrmImagePatrolCtrl.cs</DependentUpon>
    </Compile>
    <Compile Include="ImagePatrol\UI\FrmOneImageShow.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ImagePatrol\UI\FrmOneImageShow.Designer.cs">
      <DependentUpon>FrmOneImageShow.cs</DependentUpon>
    </Compile>
    <Compile Include="ImagePatrol\UI\FrmImagePatrolResultShow.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ImagePatrol\UI\FrmImagePatrolResultShow.Designer.cs">
      <DependentUpon>FrmImagePatrolResultShow.cs</DependentUpon>
    </Compile>
    <Compile Include="ImagePatrol\UI\FrmImagePatrolConfig.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ImagePatrol\UI\FrmImagePatrolConfig.Designer.cs">
      <DependentUpon>FrmImagePatrolConfig.cs</DependentUpon>
    </Compile>
    <Compile Include="ImagePatrol\UI\UC\UC_ImagePatrolResult.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ImagePatrol\UI\UC\UC_ImagePatrolResult.Designer.cs">
      <DependentUpon>UC_ImagePatrolResult.cs</DependentUpon>
    </Compile>
    <Compile Include="ImagePatrol\UI\UC\UC_OneModelImageResult.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ImagePatrol\UI\UC\UC_OneModelImageResult.Designer.cs">
      <DependentUpon>UC_OneModelImageResult.cs</DependentUpon>
    </Compile>
    <Compile Include="NodeRelationAlarm\Config\NodeRelationAlarmManager.cs" />
    <Compile Include="NodeRelationAlarm\Config\RelateInfo.cs" />
    <Compile Include="NodeRelationAlarm\UI\FrmNodeRelationAlarmConfig.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NodeRelationAlarm\UI\FrmNodeRelationAlarmConfig.Designer.cs">
      <DependentUpon>FrmNodeRelationAlarmConfig.cs</DependentUpon>
    </Compile>
    <Compile Include="SmartPatrol\Config\Base\ElectrodeUsageStatistics.cs" />
    <Compile Include="SmartPatrol\Config\Base\ReagentUsageState.cs" />
    <Compile Include="SmartPatrol\Config\PatrolDetail\TXSFUnitSmartPatrol.cs" />
    <Compile Include="SmartPatrol\Config\PatrolDetail\FYUnitSmartPatrol.cs" />
    <Compile Include="SmartPatrol\Config\PatrolDetail\FZUnitSmartPatrol.cs" />
    <Compile Include="SmartPatrol\Config\PatrolDetail\LYUnitSmartPatrol.cs" />
    <Compile Include="SmartPatrol\Config\PatrolDetail\YCLUnitSmartPatrol.cs" />
    <Compile Include="SmartPatrol\Config\PatrolDetail\SJUnitSmartPatrol.cs" />
    <Compile Include="SmartPatrol\Config\PatrolDetail\SPUnitSmartPatrol.cs" />
    <Compile Include="SmartPatrol\Config\PatrolDetail\ZKUnitSmartPatrol.cs" />
    <Compile Include="SmartPatrol\Config\PatrolDetail\FXUnitSmartPatrol.cs" />
    <Compile Include="SmartPatrol\Helper\PatrolDataDisplayHelper.cs" />
    <Compile Include="SmartPatrol\Helper\PatroReportPdfExport.cs" />
    <Compile Include="SmartPatrol\Config\ResultDetail\FZUnitPatrolResult.cs" />
    <Compile Include="SmartPatrol\Config\ResultDetail\YCLUnitPatrolResult.cs" />
    <Compile Include="SmartPatrol\Config\ResultDetail\SPUnitPatrolResult.cs" />
    <Compile Include="SmartPatrol\Config\ResultDetail\FYUnitPatrolResult.cs" />
    <Compile Include="SmartPatrol\Config\ResultDetail\LYUnitPatrolResult.cs" />
    <Compile Include="SmartPatrol\Config\ResultDetail\SJUnitPatrolResult.cs" />
    <Compile Include="SmartPatrol\Config\ResultDetail\Sub\QCDEquipResult.cs" />
    <Compile Include="SmartPatrol\Config\Base\KeyDeviceUsageStatistics.cs" />
    <Compile Include="SmartPatrol\Config\ResultDetail\Sub\WCSEquipResult.cs" />
    <Compile Include="SmartPatrol\Config\ResultDetail\Sub\SIAEquipResult.cs" />
    <Compile Include="SmartPatrol\Config\ResultDetail\ZKUnitPatrolResult.cs" />
    <Compile Include="SmartPatrol\Config\ResultDetail\FXUnitPatrolResult.cs" />
    <Compile Include="SmartPatrol\Config\ResultDetail\PSUnitPatrolResult.cs" />
    <Compile Include="SmartPatrol\Config\SmartPatrolManager.cs" />
    <Compile Include="SmartPatrol\Config\Base\SubModelResultBase.cs" />
    <Compile Include="SmartPatrol\Config\PatrolDetail\PSUnitSmartPatrol.cs" />
    <Compile Include="SmartPatrol\Config\PatrolDetail\CSUnitSmartPatrol.cs" />
    <Compile Include="SmartPatrol\Config\PatrolDetail\KZUnitSmartPatrol.cs" />
    <Compile Include="SmartPatrol\Config\SmartPatrolResult.cs" />
    <Compile Include="SmartPatrol\Config\Base\SingleUnitPatrolResultBase.cs" />
    <Compile Include="SmartPatrol\Config\Base\SingleUnitSmartPatrolBase.cs" />
    <Compile Include="SmartPatrol\Config\ResultDetail\CSUnitPatrolResult.cs" />
    <Compile Include="SmartPatrol\Config\ResultDetail\KZUnitPatrolResult.cs" />
    <Compile Include="SmartPatrol\Config\ResultDetail\Sub\ExterCSModelResult.cs" />
    <Compile Include="SmartPatrol\Config\ResultDetail\Sub\PumpModelResult.cs" />
    <Compile Include="SmartPatrol\Enums.cs" />
    <Compile Include="SmartPatrol\Helper\InspectionStructureBuilder.cs" />
    <Compile Include="SmartPatrol\SmartPatrolLogHelper.cs" />
    <Compile Include="SmartPatrol\UI\FrmOnePatrolResultShow.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SmartPatrol\UI\FrmOnePatrolResultShow.Designer.cs">
      <DependentUpon>FrmOnePatrolResultShow.cs</DependentUpon>
    </Compile>
    <Compile Include="SmartPatrol\UI\FrmPatrolErrorResultShow.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SmartPatrol\UI\FrmPatrolErrorResultShow.Designer.cs">
      <DependentUpon>FrmPatrolErrorResultShow.cs</DependentUpon>
    </Compile>
    <Compile Include="SmartPatrol\UI\FrmPatrolResultShow.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SmartPatrol\UI\FrmPatrolResultShow.Designer.cs">
      <DependentUpon>FrmPatrolResultShow.cs</DependentUpon>
    </Compile>
    <Compile Include="SmartPatrol\UI\FrmSmartPatrolConfig.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SmartPatrol\UI\FrmSmartPatrolConfig.Designer.cs">
      <DependentUpon>FrmSmartPatrolConfig.cs</DependentUpon>
    </Compile>
    <Compile Include="SmartPatrol\UI\FrmPatrolResultUploadConfig.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SmartPatrol\UI\FrmPatrolResultUploadConfig.Designer.cs">
      <DependentUpon>FrmPatrolResultUploadConfig.cs</DependentUpon>
    </Compile>
    <Compile Include="SmartPatrol\UI\FrmSmartPatrolCtrl.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SmartPatrol\UI\FrmSmartPatrolCtrl.Designer.cs">
      <DependentUpon>FrmSmartPatrolCtrl.cs</DependentUpon>
    </Compile>
    <Compile Include="CustomTask\SmartPatrolTask.cs" />
    <Compile Include="CustomTask\AirConditioningControlTask.cs" />
    <Compile Include="CustomTask\AirVentilatorControlTask.cs" />
    <Compile Include="CustomTask\DehumidifierControlTask.cs" />
    <Compile Include="DeviceUsageStatistics\Config\DeviceUsageInfo.cs" />
    <Compile Include="DeviceUsageStatistics\UsageStatisticsManager.cs" />
    <Compile Include="DeviceUsageStatistics\UI\UC\UC_OneDeviceUsageInfo.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="DeviceUsageStatistics\UI\UC\UC_OneDeviceUsageInfo.Designer.cs">
      <DependentUpon>UC_OneDeviceUsageInfo.cs</DependentUpon>
    </Compile>
    <Compile Include="DeviceUsageStatistics\UI\UC_AllDeviceUsageInfos.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="DeviceUsageStatistics\UI\UC_AllDeviceUsageInfos.Designer.cs">
      <DependentUpon>UC_AllDeviceUsageInfos.cs</DependentUpon>
    </Compile>
    <Compile Include="DynamicAdd\FrmConfigDynamic.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="DynamicAdd\FrmConfigDynamic.Designer.cs">
      <DependentUpon>FrmConfigDynamic.cs</DependentUpon>
    </Compile>
    <Compile Include="DynamicAdd\DynamicAddManager.cs" />
    <Compile Include="FiveParamCheck\Config\FiveParamCheckManager.cs" />
    <Compile Include="FiveParamCheck\Config\ParamCheckInfo.cs" />
    <Compile Include="FiveParamCheck\UI\FrmFiveParamManualCheck.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FiveParamCheck\UI\FrmFiveParamManualCheck.Designer.cs">
      <DependentUpon>FrmFiveParamManualCheck.cs</DependentUpon>
    </Compile>
    <Compile Include="FiveParamCheck\UI\UC_NodeCheckConfig.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="FiveParamCheck\UI\UC_NodeCheckConfig.Designer.cs">
      <DependentUpon>UC_NodeCheckConfig.cs</DependentUpon>
    </Compile>
    <Compile Include="FlowCount\FormFlowCount.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FlowCount\FormFlowCount.designer.cs">
      <DependentUpon>FormFlowCount.cs</DependentUpon>
    </Compile>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="StationInfo\StationInfoManager.cs" />
    <Compile Include="StationInfo\UI\FrmStationInfoManager.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="StationInfo\UI\FrmStationInfoManager.Designer.cs">
      <DependentUpon>FrmStationInfoManager.cs</DependentUpon>
    </Compile>
    <Compile Include="SystemParam\UI\FrmConfigSystemParam.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SystemParam\UI\FrmConfigSystemParam.Designer.cs">
      <DependentUpon>FrmConfigSystemParam.cs</DependentUpon>
    </Compile>
    <Compile Include="SystemPowerNode\Config\SystemPowerManager.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="SystemPowerNode\UI\FormSystemPowerNode.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SystemPowerNode\UI\FormSystemPowerNode.Designer.cs">
      <DependentUpon>FormSystemPowerNode.cs</DependentUpon>
    </Compile>
    <Compile Include="EnumRelated\Enums.cs" />
    <Compile Include="ExcepQuit\Config\ExcepQuitManager.cs" />
    <Compile Include="ExcepQuit\UI\FormExcepQuitConfig.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ExcepQuit\UI\FormExcepQuitConfig.Designer.cs">
      <DependentUpon>FormExcepQuitConfig.cs</DependentUpon>
    </Compile>
    <Compile Include="ManualQC\Config\ManualQCPlanManager.cs" />
    <Compile Include="ManualQC\Config\QCPlanInfo.cs" />
    <Compile Include="ManualQC\UI\FrmManualQCConfig.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ManualQC\UI\FrmManualQCConfig.Designer.cs">
      <DependentUpon>FrmManualQCConfig.cs</DependentUpon>
    </Compile>
    <Compile Include="ManualQC\UI\FrmManualQC.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ManualQC\UI\FrmManualQC.Designer.cs">
      <DependentUpon>FrmManualQC.cs</DependentUpon>
    </Compile>
    <Compile Include="ManualQC\UI\UC_OneQCFlow.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ManualQC\UI\UC_OneQCFlow.Designer.cs">
      <DependentUpon>UC_OneQCFlow.cs</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="SystemConfig\SystemHelper.cs" />
    <Compile Include="SystemConfig\SystemLogHelper.cs" />
    <Compile Include="SystemConfig\SystemManager.cs" />
    <Compile Include="SystemParam\Config\SystemParam.cs" />
    <Compile Include="SystemParam\Config\SystemParamManager.cs" />
    <Compile Include="SystemParam\UI\FrmEditSystemParam.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SystemParam\UI\FrmEditSystemParam.Designer.cs">
      <DependentUpon>FrmEditSystemParam.cs</DependentUpon>
    </Compile>
    <Compile Include="SystemParam\UI\FrmSelectNode.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SystemParam\UI\FrmSelectNode.Designer.cs">
      <DependentUpon>FrmSelectNode.cs</DependentUpon>
    </Compile>
    <Compile Include="SystemParam\UI\UC_ConfigSystemParam.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="SystemParam\UI\UC_ConfigSystemParam.Designer.cs">
      <DependentUpon>UC_ConfigSystemParam.cs</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Fpi.Alarm\Fpi.Alarm.csproj">
      <Project>{E714875C-0EC1-4C0F-8571-D0F631430C82}</Project>
      <Name>Fpi.Alarm</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Camera\Fpi.Camera.csproj">
      <Project>{1007e2b0-01aa-4bc4-9753-29e75615c1a0}</Project>
      <Name>Fpi.Camera</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Communication\Fpi.Communication.csproj">
      <Project>{D95F58B1-2E07-4D52-BA26-3F9B6EEACF29}</Project>
      <Name>Fpi.Communication</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Data\Fpi.Data.csproj">
      <Project>{07B7E9D5-5D00-4815-9409-0D7466A09F96}</Project>
      <Name>Fpi.Data</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.DB\Fpi.DB.csproj">
      <Project>{89D85957-BA9E-4BD9-99FE-7B73B6176A6F}</Project>
      <Name>Fpi.DB</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Device\Fpi.Device.csproj">
      <Project>{88FEF5D2-E039-4AC0-942B-442F23755978}</Project>
      <Name>Fpi.Device</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.HB.Business\Fpi.HB.Business.csproj">
      <Project>{13650425-1448-4DF5-884F-B7CD466ECB24}</Project>
      <Name>Fpi.HB.Business</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Json\Fpi.Json.csproj">
      <Project>{958C97C1-360F-4434-9C37-6C6030EB5FCD}</Project>
      <Name>Fpi.Json</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Log\Fpi.Log.csproj">
      <Project>{C7C2425F-8926-43C6-996E-47205531C604}</Project>
      <Name>Fpi.Log</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Operation\Fpi.Operation.csproj">
      <Project>{1657672D-6FBA-47A5-8C40-0DA507D578F2}</Project>
      <Name>Fpi.Operation</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Timer\Fpi.Timer.csproj">
      <Project>{1DC3DD73-A4F5-4CA4-96D3-43712267C864}</Project>
      <Name>Fpi.Timer</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.UI.Common\Fpi.UI.Common.csproj">
      <Project>{c238e665-75b4-4eda-b574-a37f2794ba54}</Project>
      <Name>Fpi.UI.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.UI.PC\Fpi.UI.PC.csproj">
      <Project>{2d502016-b3b3-43ff-9bae-ad1d2a18d42e}</Project>
      <Name>Fpi.UI.PC</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.User\Fpi.User.csproj">
      <Project>{7D3E1D03-9F81-4B4A-B6B3-334D829DB1F4}</Project>
      <Name>Fpi.User</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Util\Fpi.Util.csproj">
      <Project>{6E37D7B3-8D08-4EF3-A924-3B87982AB246}</Project>
      <Name>Fpi.Util</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Xml\Fpi.Xml.csproj">
      <Project>{3AF9654D-39EE-4BE9-8553-A9BB9B83A33B}</Project>
      <Name>Fpi.Xml</Name>
    </ProjectReference>
    <ProjectReference Include="..\WMS3900.Algorithm\Fpi.WMS3900.Algorithm.csproj">
      <Project>{85367fca-5dd1-4a81-8390-553b0c1e392c}</Project>
      <Name>Fpi.WMS3900.Algorithm</Name>
    </ProjectReference>
    <ProjectReference Include="..\WMS3900.DB\Fpi.WMS3900.DB.csproj">
      <Project>{4e6961d6-ba42-4cb1-89a7-c25557a2f82a}</Project>
      <Name>Fpi.WMS3900.DB</Name>
    </ProjectReference>
    <ProjectReference Include="..\WMS3900.Equipment\Fpi.WMS3900.Equipment.csproj">
      <Project>{63a37282-fea3-4f07-98f1-164045b58d8b}</Project>
      <Name>Fpi.WMS3900.Equipment</Name>
    </ProjectReference>
    <ProjectReference Include="..\WMS3900.Inspection\Fpi.WMS3900.Inspection.csproj">
      <Project>{a5e42fa2-c43e-421c-a0de-2f06f6efe255}</Project>
      <Name>Fpi.WMS3900.Inspection</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="DeviceUsageStatistics\UI\FrmEquipUsageInfoModify.resx">
      <DependentUpon>FrmEquipUsageInfoModify.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DeviceUsageStatistics\UI\FrmDeviceUsageInfoModify.resx">
      <DependentUpon>FrmDeviceUsageInfoModify.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DeviceUsageStatistics\UI\UC\UC_OneEquipUsageInfo.resx">
      <DependentUpon>UC_OneEquipUsageInfo.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DeviceUsageStatistics\UI\UC_AllCameraUsageInfos.resx">
      <DependentUpon>UC_AllCameraUsageInfos.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DeviceUsageStatistics\UI\UC_AllEquipUsageInfos.resx">
      <DependentUpon>UC_AllEquipUsageInfos.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ImagePatrol\UI\FrmImagePatrolCtrl.resx">
      <DependentUpon>FrmImagePatrolCtrl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ImagePatrol\UI\FrmOneImageShow.resx">
      <DependentUpon>FrmOneImageShow.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ImagePatrol\UI\FrmImagePatrolResultShow.resx">
      <DependentUpon>FrmImagePatrolResultShow.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ImagePatrol\UI\FrmImagePatrolConfig.resx">
      <DependentUpon>FrmImagePatrolConfig.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ImagePatrol\UI\UC\UC_ImagePatrolResult.resx">
      <DependentUpon>UC_ImagePatrolResult.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ImagePatrol\UI\UC\UC_OneModelImageResult.resx">
      <DependentUpon>UC_OneModelImageResult.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NodeRelationAlarm\UI\FrmNodeRelationAlarmConfig.resx">
      <DependentUpon>FrmNodeRelationAlarmConfig.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SmartPatrol\UI\FrmOnePatrolResultShow.resx">
      <DependentUpon>FrmOnePatrolResultShow.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SmartPatrol\UI\FrmPatrolErrorResultShow.resx">
      <DependentUpon>FrmPatrolErrorResultShow.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SmartPatrol\UI\FrmPatrolResultShow.resx">
      <DependentUpon>FrmPatrolResultShow.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SmartPatrol\UI\FrmSmartPatrolConfig.resx">
      <DependentUpon>FrmSmartPatrolConfig.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SmartPatrol\UI\FrmPatrolResultUploadConfig.resx">
      <DependentUpon>FrmPatrolResultUploadConfig.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SmartPatrol\UI\FrmSmartPatrolCtrl.resx">
      <DependentUpon>FrmSmartPatrolCtrl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DeviceUsageStatistics\UI\UC\UC_OneDeviceUsageInfo.resx">
      <DependentUpon>UC_OneDeviceUsageInfo.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DeviceUsageStatistics\UI\UC_AllDeviceUsageInfos.resx">
      <DependentUpon>UC_AllDeviceUsageInfos.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="DynamicAdd\FrmConfigDynamic.resx">
      <DependentUpon>FrmConfigDynamic.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FiveParamCheck\UI\FrmFiveParamManualCheck.resx">
      <DependentUpon>FrmFiveParamManualCheck.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FiveParamCheck\UI\UC_NodeCheckConfig.resx">
      <DependentUpon>UC_NodeCheckConfig.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FlowCount\FormFlowCount.resx">
      <DependentUpon>FormFlowCount.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="StationInfo\UI\FrmStationInfoManager.resx">
      <DependentUpon>FrmStationInfoManager.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SystemParam\UI\FrmConfigSystemParam.resx">
      <DependentUpon>FrmConfigSystemParam.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SystemPowerNode\UI\FormSystemPowerNode.resx">
      <DependentUpon>FormSystemPowerNode.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ExcepQuit\UI\FormExcepQuitConfig.resx">
      <DependentUpon>FormExcepQuitConfig.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ManualQC\UI\FrmManualQCConfig.resx">
      <DependentUpon>FrmManualQCConfig.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ManualQC\UI\FrmManualQC.resx">
      <DependentUpon>FrmManualQC.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ManualQC\UI\UC_OneQCFlow.resx">
      <DependentUpon>UC_OneQCFlow.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SystemParam\UI\FrmEditSystemParam.resx">
      <DependentUpon>FrmEditSystemParam.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SystemParam\UI\FrmSelectNode.resx">
      <DependentUpon>FrmSelectNode.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SystemParam\UI\UC_ConfigSystemParam.resx">
      <DependentUpon>UC_ConfigSystemParam.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\1.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\2.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\3.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\4.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\5.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\6.gif" />
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>