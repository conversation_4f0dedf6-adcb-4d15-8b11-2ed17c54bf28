﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Net.NetworkInformation;
using System.Windows.Forms;
using Fpi.Devices;
using Fpi.WMS3000.Equipment.Config;

namespace Fpi.WMS3000.SystemConfig.SmartPatrol.Config
{
    /// <summary>
    /// 控制单元巡检执行类
    /// </summary>
    public class KZUnitSmartPatrol : SingleUnitSmartPatrolBase
    {
        #region 构造

        public KZUnitSmartPatrol()
        {
            UnitId = "SystemControl";
            UnitName = "控制单元";
        }

        #endregion

        #region 方法重写

        public override SingleUnitPatrolResultBase ExecutePatrol()
        {
            KZUnitPatrolResult result = new KZUnitPatrolResult();

            // 检查联网状态
            result.NetState = CheckNetState();
            // 检查仪表通信状态
            result.DevComState = CheckDevComState(out List<string> comFailtDveList);
            result.ComFailtDveList = comFailtDveList;
            // 检查磁盘存储空间大小
            result.DiskState = CheckDiskState();

            // 分数计算
            result.PatrolScore = 0;
            if(result.NetState == eModuleWorkingState.正常)
            {
                result.PatrolScore += 30;
            }
            if(result.DevComState == eModuleWorkingState.正常)
            {
                result.PatrolScore += 40;
            }
            if(result.VideoComState == eModuleWorkingState.正常)
            {
                result.PatrolScore += 10;
            }
            if(result.DiskState == eModuleWorkingState.正常)
            {
                result.PatrolScore += 20;
            }

            // 结果判定
            result.PatrolResult = result.PatrolScore >= 80 ? ePatrolResult.正常 : ePatrolResult.异常;

            return result;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 检查联网状态
        /// </summary>
        /// <returns></returns>
        private eModuleWorkingState CheckNetState()
        {
            // 方法1：检查是否可以连接到互联网
            try
            {
                string addr = SmartPatrolManager.GetInstance().NetTestAddr;
                if(string.IsNullOrWhiteSpace(addr))
                {
                    addr = "***************";
                }
                using(var ping = new Ping())
                {
                    var result = ping.Send(addr, 3000); // 超时设置为3秒
                    return result.Status == IPStatus.Success ? eModuleWorkingState.正常 : eModuleWorkingState.异常;
                }
            }
            catch
            {
                // 方法2：检查网络接口状态
                return NetworkInterface.GetIsNetworkAvailable() ? eModuleWorkingState.正常 : eModuleWorkingState.异常;
            }
        }

        /// <summary>
        /// 检查仪表通信状态
        /// </summary>
        /// <returns></returns>
        private eModuleWorkingState CheckDevComState(out List<string> comFailtDveList)
        {
            comFailtDveList = new List<string>();
            eModuleWorkingState result = eModuleWorkingState.正常;
            // 遍历当前启用的仪表
            foreach(var dev in DeviceManager.GetInstance().GetDeviceListUsed())
            {
                // 任一仪表通信异常，则置异常状态
                if(dev.IsComStateError())
                {
                    comFailtDveList.Add(dev.name);
                    result = eModuleWorkingState.异常;
                }
            }

            return result;
        }

        /// <summary>
        /// 检查仪表通信状态
        /// </summary>
        /// <returns></returns>
        private eModuleWorkingState CheckDiskState()
        {
            // 定义阈值（20GB，单位为字节）
            long MIN_DISK_SPACE = 20L * 1024 * 1024 * 1024;

            // 获取当前应用程序所在的驱动器
            string currentDrive = Path.GetPathRoot(Application.StartupPath);

            // 获取驱动器信息
            DriveInfo driveInfo = new DriveInfo(currentDrive);

            // 获取可用空间（字节）
            long freeSpace = driveInfo.AvailableFreeSpace;

            // 转换为GB以便于显示
            double freeSpaceGB = Math.Round((double)freeSpace / (1024 * 1024 * 1024), 2);

            // 检查是否低于阈值
            return freeSpace < MIN_DISK_SPACE ? eModuleWorkingState.异常 : eModuleWorkingState.正常;
        }

        #endregion
    }
}