<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="toolStrip.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>25, 15</value>
  </metadata>
  <assembly alias="mscorlib" name="mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="toolStrip.AutoSize" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="toolStrip.BackgroundImageLayout" type="System.Windows.Forms.ImageLayout, System.Windows.Forms">
    <value>None</value>
  </data>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="toolStrip.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 12pt</value>
  </data>
  <data name="toolStripLabel1.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 12pt</value>
  </data>
  <data name="toolStripLabel1.Size" type="System.Drawing.Size, System.Drawing">
    <value>46, 32</value>
  </data>
  <data name="toolStripLabel1.Text" xml:space="preserve">
    <value>显示:</value>
  </data>
  <data name="cmbTypes.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 12pt</value>
  </data>
  <data name="cmbTypes.IntegralHeight" type="System.Boolean, mscorlib">
    <value>False</value>
  </data>
  <data name="cmbTypes.Size" type="System.Drawing.Size, System.Drawing">
    <value>160, 35</value>
  </data>
  <data name="cmbTypes.ToolTipText" xml:space="preserve">
    <value>选择输出类型</value>
  </data>
  <data name="toolStripSeparator1.Size" type="System.Drawing.Size, System.Drawing">
    <value>6, 35</value>
  </data>
  <data name="tsbCopy.BackgroundImageLayout" type="System.Windows.Forms.ImageLayout, System.Windows.Forms">
    <value>Zoom</value>
  </data>
  <data name="tsbCopy.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 12pt</value>
  </data>
  <data name="tsbCopy.ImageTransparentColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="tsbCopy.Size" type="System.Drawing.Size, System.Drawing">
    <value>116, 32</value>
  </data>
  <data name="tsbCopy.Text" xml:space="preserve">
    <value>       复制       </value>
  </data>
  <data name="tsbCopy.ToolTipText" xml:space="preserve">
    <value>复制</value>
  </data>
  <data name="toolStripLabel3.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 12pt</value>
  </data>
  <data name="toolStripLabel3.Size" type="System.Drawing.Size, System.Drawing">
    <value>30, 32</value>
  </data>
  <data name="toolStripLabel3.Text" xml:space="preserve">
    <value>    </value>
  </data>
  <data name="tsbClear.BackgroundImageLayout" type="System.Windows.Forms.ImageLayout, System.Windows.Forms">
    <value>Zoom</value>
  </data>
  <data name="tsbClear.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 12pt</value>
  </data>
  <data name="tsbClear.ImageTransparentColor" type="System.Drawing.Color, System.Drawing">
    <value>Transparent</value>
  </data>
  <data name="tsbClear.Size" type="System.Drawing.Size, System.Drawing">
    <value>116, 32</value>
  </data>
  <data name="tsbClear.Text" xml:space="preserve">
    <value>        清空      </value>
  </data>
  <data name="tsbClear.ToolTipText" xml:space="preserve">
    <value>全部清除</value>
  </data>
  <data name="toolStrip.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 0</value>
  </data>
  <data name="toolStrip.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>0, 0, 0, 0</value>
  </data>
  <data name="toolStrip.Size" type="System.Drawing.Size, System.Drawing">
    <value>796, 35</value>
  </data>
  <data name="toolStrip.TabIndex" type="System.Int32, mscorlib">
    <value>0</value>
  </data>
  <data name="&gt;&gt;toolStrip.Name" xml:space="preserve">
    <value>toolStrip</value>
  </data>
  <data name="&gt;&gt;toolStrip.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStrip, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;toolStrip.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;toolStrip.ZOrder" xml:space="preserve">
    <value>2</value>
  </data>
  <metadata name="cmsCtrl.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>210, 21</value>
  </metadata>
  <data name="cmsCtrl.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 12pt</value>
  </data>
  <data name="tsmCopy.Size" type="System.Drawing.Size, System.Drawing">
    <value>144, 26</value>
  </data>
  <data name="tsmCopy.Text" xml:space="preserve">
    <value>复制</value>
  </data>
  <data name="tsmClear.Size" type="System.Drawing.Size, System.Drawing">
    <value>144, 26</value>
  </data>
  <data name="tsmClear.Text" xml:space="preserve">
    <value>全部清除</value>
  </data>
  <data name="cmsCtrl.Size" type="System.Drawing.Size, System.Drawing">
    <value>145, 56</value>
  </data>
  <data name="&gt;&gt;cmsCtrl.Name" xml:space="preserve">
    <value>cmsCtrl</value>
  </data>
  <data name="&gt;&gt;cmsCtrl.Type" xml:space="preserve">
    <value>Sunny.UI.UIContextMenuStrip, SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb</value>
  </data>
  <data name="lvLog.Dock" type="System.Windows.Forms.DockStyle, System.Windows.Forms">
    <value>Fill</value>
  </data>
  <data name="lvLog.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 12pt</value>
  </data>
  <data name="lvLog.Location" type="System.Drawing.Point, System.Drawing">
    <value>0, 35</value>
  </data>
  <data name="lvLog.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>4, 5, 4, 5</value>
  </data>
  <data name="lvLog.MinimumSize" type="System.Drawing.Size, System.Drawing">
    <value>1, 1</value>
  </data>
  <data name="lvLog.Padding" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>2, 2, 2, 2</value>
  </data>
  <data name="lvLog.Size" type="System.Drawing.Size, System.Drawing">
    <value>796, 539</value>
  </data>
  <data name="lvLog.TabIndex" type="System.Int32, mscorlib">
    <value>2</value>
  </data>
  <data name="lvLog.Text" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="&gt;&gt;lvLog.Name" xml:space="preserve">
    <value>lvLog</value>
  </data>
  <data name="&gt;&gt;lvLog.Type" xml:space="preserve">
    <value>Sunny.UI.UIListBox, SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb</value>
  </data>
  <data name="&gt;&gt;lvLog.Parent" xml:space="preserve">
    <value>$this</value>
  </data>
  <data name="&gt;&gt;lvLog.ZOrder" xml:space="preserve">
    <value>1</value>
  </data>
  <metadata name="toolTip.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>126, 19</value>
  </metadata>
  <metadata name="$this.Localizable" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="$this.ClientSize" type="System.Drawing.Size, System.Drawing">
    <value>796, 574</value>
  </data>
  <data name="$this.Font" type="System.Drawing.Font, System.Drawing">
    <value>微软雅黑, 9pt</value>
  </data>
  <data name="$this.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAEAEBAQAAAAAAAoAQAAFgAAACgAAAAQAAAAIAAAAAEABAAAAAAAgAAAAAAAAAAAAAAAEAAAABAA
        AAAAAAAAAACAAACAAAAAgIAAgAAAAIAAgACAgAAAgICAAMDAwAAAAP8AAP8AAAD//wD/AAAA/wD/AP//
        AAD///8AAAAAAAAAAAAAeIiIiIiIAAB///////gAAH+IiIiI+AAAf//////4AAB/iIiIiPgAAH//////
        +AAAf4iIiIj4AAB///////gAAH+IiIiI+AAAf/////+IAAB/iIiI8AAAAH/////3BwAAf/////dwAAB3
        d3d3dwAAAAAAAAAAAADAAQAAwAEAAMABAADAAQAAwAEAAMABAADAAQAAwAEAAMABAADAAQAAwAEAAMAB
        AADACwAAwAcAAMAPAAD//wAA
</value>
  </data>
  <data name="$this.Margin" type="System.Windows.Forms.Padding, System.Windows.Forms">
    <value>3, 4, 3, 4</value>
  </data>
  <data name="$this.StartPosition" type="System.Windows.Forms.FormStartPosition, System.Windows.Forms">
    <value>Manual</value>
  </data>
  <data name="$this.Text" xml:space="preserve">
    <value>系统实时日志</value>
  </data>
  <data name="&gt;&gt;toolStripLabel1.Name" xml:space="preserve">
    <value>toolStripLabel1</value>
  </data>
  <data name="&gt;&gt;toolStripLabel1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripLabel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;cmbTypes.Name" xml:space="preserve">
    <value>cmbTypes</value>
  </data>
  <data name="&gt;&gt;cmbTypes.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripComboBox, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator1.Name" xml:space="preserve">
    <value>toolStripSeparator1</value>
  </data>
  <data name="&gt;&gt;toolStripSeparator1.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripSeparator, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tsbCopy.Name" xml:space="preserve">
    <value>tsbCopy</value>
  </data>
  <data name="&gt;&gt;tsbCopy.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;toolStripLabel3.Name" xml:space="preserve">
    <value>toolStripLabel3</value>
  </data>
  <data name="&gt;&gt;toolStripLabel3.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripLabel, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tsbClear.Name" xml:space="preserve">
    <value>tsbClear</value>
  </data>
  <data name="&gt;&gt;tsbClear.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripButton, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tsmCopy.Name" xml:space="preserve">
    <value>tsmCopy</value>
  </data>
  <data name="&gt;&gt;tsmCopy.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;tsmClear.Name" xml:space="preserve">
    <value>tsmClear</value>
  </data>
  <data name="&gt;&gt;tsmClear.Type" xml:space="preserve">
    <value>System.Windows.Forms.ToolStripMenuItem, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </data>
  <data name="&gt;&gt;toolTip.Name" xml:space="preserve">
    <value>toolTip</value>
  </data>
  <data name="&gt;&gt;toolTip.Type" xml:space="preserve">
    <value>Sunny.UI.UIToolTip, SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb</value>
  </data>
  <data name="&gt;&gt;$this.Name" xml:space="preserve">
    <value>FrmCurrentLog</value>
  </data>
  <data name="&gt;&gt;$this.Type" xml:space="preserve">
    <value>Fpi.UI.PC.DockForms.DockView, Fpi.UI.PC, Version=*******, Culture=neutral, PublicKeyToken=null</value>
  </data>
</root>