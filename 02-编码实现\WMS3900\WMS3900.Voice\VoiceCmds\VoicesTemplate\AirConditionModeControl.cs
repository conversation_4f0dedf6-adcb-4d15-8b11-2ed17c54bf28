﻿using System;
using System.Threading.Tasks;
using Fpi.WMS3000.Equipment;
using Fpi.WMS3000.Equipment.Config;
using Fpi.WMS3000.Voice.Helper;
using Fpi.WMS3000.Voice.Interface;

namespace Fpi.WMS3000.Voice.VoicesTemplate
{
    /// <summary>
    /// 空调模式设置
    /// </summary>
    public class AirConditionModeControl : CustomVoiceCmd, IVoicesTemplateConfigView
    {
        #region 字段属性

        /// <summary>
        /// 是否初始化
        /// </summary>
        private bool _inited;

        /// <summary>
        /// 控制模式
        /// </summary>
        public eACState _acState;

        #endregion

        #region 公共方法（重写）

        public override string ToString()
        {
            return "空调模式控制";
        }

        public override string CustomDo(int paran)
        {
            try
            {
                if(!_inited)
                {
                    InitProperty();
                }

                // 找到空调设备
                if(ExterEquipConfigManager.GetInstance().DeviceSelect.AirControlDevice is not JDRKRSEquip airEquip)
                {
                    throw new Exception("系统内未配置空调设备！");
                }

                // 获取空调运行逻辑配置
                var info = ExterEquipConfigManager.GetInstance().AirConditioningConfigInfo;

                // 自动模式下才进行自动控制
                if(info.AirConditionerRunMode == eRunMode.自动模式)
                {
                    throw new Exception("当前空调处于自动运行模式！");
                }

                // 读取当前状态
                eACState oldState = info.AirConditionerCurrentState;

                // 状态切换时进行控制
                if(oldState != _acState)
                {
                    // 异步执行控制
                    Task.Run(() =>
                    {
                        try
                        {
                            // 老状态为关机时，先开机再切新状态
                            if(oldState == eACState.关机 && _acState != eACState.关机)
                            {
                                info.AirConditionerController.SetACState(eACState.开机);
                            }

                            // 设置空调状态
                            info.AirConditionerController.SetACState(_acState);

                            VoiceLogHelper.Info($"[{CmdConfig.name}]命令执行成功，空调状态由{oldState}切换为{_acState}。");
                        }
                        catch(Exception e)
                        {
                            VoiceLogHelper.Info($"[{CmdConfig.name}]命令执行出错：{e.Message}");
                        }
                    });
                }

                string str = _acState == eACState.开机 ? "打开" : "关闭";
                return $"好的，正在{str}";
            }
            catch(Exception ex)
            {
                VoiceLogHelper.Info($"[{CmdConfig.name}]命令执行出错: {ex.Message}");
                return $"抱歉，执行出错，{ex.Message}";
            }
        }

        #endregion

        #region IVoicesTemplateConfigView 成员

        public IVoiceConfig TemplateConfigView => new UC_AirConditionModeControl();

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化参数
        /// </summary>
        private void InitProperty()
        {
            string tmp = CmdConfig.GetPropertyValue(GlobalNameDefine.PropertyName_AirConditionMode);
            if(string.IsNullOrEmpty(tmp))
            {
                throw new Exception("空调模式未配置！");
            }
            int mode;
            if(!int.TryParse(tmp, out mode))
            {
                throw new Exception($"空调模式配置不合法：当前配置[{tmp}]非数字！");
            }
            if(!Enum.IsDefined(typeof(eACState), mode))
            {
                throw new Exception("空调模式配置不合法：超出定义范围！");
            }
            _acState = (eACState)mode;

            _inited = true;
        }

        #endregion
    }
}