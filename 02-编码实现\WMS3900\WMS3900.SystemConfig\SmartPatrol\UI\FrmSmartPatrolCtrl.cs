﻿using System;
using System.Collections.Generic;
using System.Windows.Forms;
using Fpi.HB.Business.Tasks;
using Fpi.Timers.UI.PC;
using Fpi.Util.ThreadRelated;
using Fpi.WMS3000.DB;
using Fpi.WMS3000.SystemConfig.SmartPatrol;
using Fpi.WMS3000.SystemConfig.SmartPatrol.Config;
using Fpi.WMS3000.SystemConfig.UI;
using Sunny.UI;
using Timer = Fpi.Timers.Timer;

namespace Fpi.WMS3000.SystemConfig
{
    /// <summary>
    /// 智能巡检控制界面
    /// </summary>
    public partial class FrmSmartPatrolCtrl : UIForm
    {
        #region 构造

        public FrmSmartPatrolCtrl()
        {
            InitializeComponent();
        }


        #endregion

        #region 事件

        private void FrmSmartPatrolCtrl_Load(object sender, EventArgs e)
        {
            // 初始化界面控件
            InitCtrl();

            // 根据当前巡检情况（是否进行中）设置各控件值
            SetCtrlInfo();

            // 初始化事件绑定
            InitEvent();
        }

        /// <summary>
        /// 关闭界面时解绑通知事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void FrmSmartPatrolCtrl_FormClosing(object sender, FormClosingEventArgs e)
        {
            SmartPatrolManager.GetInstance().PatrolStartEvent -= FrmSmartPatrolCtrl_PatrolStartEvent;
            SmartPatrolManager.GetInstance().PatrolEndEvent -= FrmSmartPatrolCtrl_PatrolEndEvent;
            SmartPatrolManager.GetInstance().SingleUnitPatrolStartEvent -= FrmSmartPatrolCtrl_SingleUnitPatrolStartEvent;
            SmartPatrolManager.GetInstance().SingleUnitPatrolEndEvent -= FrmSmartPatrolCtrl_SingleUnitPatrolEndEvent;
        }

        /// <summary>
        /// 开始/停止
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnStartDo_Click(object sender, EventArgs e)
        {
            if(btnStartDo.Text == "开始")
            {
                // 触发执行
                if(SmartPatrolManager.GetInstance().StartDoPatrol(ePatrolTriggerType.手动触发))
                {
                    // 记录系统操作日志
                    SystemOpLogHelper.SaveLog(new SystemOpLogInfo($"触发执行智能巡检", eOpType.控制操作, eOpStyle.本地操作));

                    btnStartDo.Text = "停止";

                    btnOpenResult.Enabled = btnTimerConfig.Enabled = false;
                }
            }
            else
            {
                // 记录系统操作日志
                SystemOpLogHelper.SaveLog(new SystemOpLogInfo($"触发停止智能巡检", eOpType.控制操作, eOpStyle.本地操作));

                // 触发停止
                SmartPatrolManager.GetInstance().StopDoPatrol();
            }
        }

        /// <summary>
        /// 查看结果
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnOpenResult_Click(object sender, EventArgs e)
        {
            if(SmartPatrolManager.GetInstance().LatestSmartPatrolResult is SmartPatrolResult result)
            {
                new FrmPatrolResultShow(result).ShowDialog();
            }
        }

        /// <summary>
        /// 智能巡检定时配置
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnTimerConfig_Click(object sender, EventArgs e)
        {
            // 标志任务是否是本次新增的
            bool newAdd = false;
            if(CustomTaskManager.GetInstance().GetCustomTaskByType(typeof(SmartPatrolTask)) is not SmartPatrolTask smartPatrolTask)
            {
                newAdd = true;
                smartPatrolTask = new SmartPatrolTask();
                smartPatrolTask.id = "SmartPatrolTask";
                smartPatrolTask.name = "智能巡检任务";
                smartPatrolTask.Description = "智能巡检任务";
                smartPatrolTask.IsValid = true;

                if(smartPatrolTask.timer == null)
                {
                    smartPatrolTask.timer = new Timer { description = "智能巡检任务定时器" };
                }
            }

            FormConfigTimerUser timerForm = new FormConfigTimerUser(smartPatrolTask.timer);
            if(timerForm.ShowDialog() == DialogResult.OK)
            {
                smartPatrolTask.timer = timerForm.Timer;
                if(smartPatrolTask.timer != null)
                {
                    smartPatrolTask.timer.description = "智能巡检任务定时器";
                }

                if(newAdd)
                {
                    CustomTaskManager.GetInstance().CustomTasks.AddNode(smartPatrolTask);
                }

                CustomTaskManager.GetInstance().Save();
            }
        }

        /// <summary>
        /// 巡检参数配置
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnParamConfig_Click(object sender, EventArgs e)
        {
            var form = new FrmSmartPatrolConfig();
            if(form.ShowDialog() == DialogResult.OK)
            {
                // 更新巡检内容项
                InitCtrl();
            }
        }

        #region 事件实现

        /// <summary>
        /// 巡检流程开始
        /// </summary>
        private void FrmSmartPatrolCtrl_PatrolStartEvent()
        {
            var result = SmartPatrolManager.GetInstance().LatestSmartPatrolResult;
            ThreadHelper.UpdateControlText(lblStartTime, result.StartTime.ToString("yyyy-MM-dd HH:mm:ss"));
            ThreadHelper.UpdateControlText(lblTriggerType, result.PatrolTriggerType.ToString());
            ThreadHelper.UpdateControlText(txtResultInfo, $"巡检开始时间：{result.StartTime:yyyy-MM-dd HH:mm:ss}，触发方式：{result.PatrolTriggerType}\r\n");
            ThreadHelper.UpdateControlText(lblPatrolState, SmartPatrolManager.GetInstance().PatrolState.ToString());
            ThreadHelper.UpdateGifAvatarActive(gifAvatar, true);
        }

        /// <summary>
        /// 巡检流程结束
        /// </summary>
        private void FrmSmartPatrolCtrl_PatrolEndEvent()
        {
            ThreadHelper.UpdateControlText(btnStartDo, "开始");
            ThreadHelper.UpdateControlEnable(btnOpenResult, true);
            ThreadHelper.UpdateControlEnable(btnTimerConfig, true);
            ThreadHelper.UpdateGifAvatarActive(gifAvatar, false);
            ThreadHelper.UpdateUCStepStep(stepCtrl, stepCtrl.Steps.Length);
            ThreadHelper.UpdateControlText(lblPatrolState, SmartPatrolManager.GetInstance().PatrolState.ToString());
        }

        /// <summary>
        /// 模块巡检开始
        /// </summary>
        /// <param name="obj"></param>
        private void FrmSmartPatrolCtrl_SingleUnitPatrolStartEvent(SingleUnitSmartPatrolBase obj)
        {
            int step = SmartPatrolManager.GetInstance().CurrnetUnitIndex;
            ThreadHelper.UpdateUCStepStep(stepCtrl, step);
        }

        /// <summary>
        /// 模块巡检结束，打印信息
        /// </summary>
        /// <param name="model"></param>
        private void FrmSmartPatrolCtrl_SingleUnitPatrolEndEvent(SingleUnitSmartPatrolBase model)
        {
            if(model != null && model.PatrolResult != null)
            {
                string info = $"{model.PatrolResult.GetResultStr()}\r\n";
                ThreadHelper.AppendText(txtResultInfo, info);
            }
        }

        #endregion

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化界面控件
        /// </summary>
        private void InitCtrl()
        {
            // 步骤表
            List<string> stepList = new List<string>();
            stepList.Add("开始");
            foreach(var model in SmartPatrolManager.GetInstance().PatrolUnitList)
            {
                stepList.Add(model.UnitName);
            }
            stepList.Add("结束");
            stepCtrl.Steps = stepList.ToArray();
            stepCtrl.StepIndex = 0;
        }

        /// <summary>
        /// 根据当前巡检情况（是否进行中）设置各控件值
        /// </summary>
        private void SetCtrlInfo()
        {
            // 巡检进度
            lblPatrolState.Text = SmartPatrolManager.GetInstance().PatrolState.ToString();

            if(SmartPatrolManager.GetInstance().PatrolState == ePatrolState.巡检中)
            {
                // 进度表
                stepCtrl.StepIndex = SmartPatrolManager.GetInstance().CurrnetUnitIndex;
                // gif动画
                gifAvatar.Active = true;
                // 开始时间
                lblStartTime.Text = SmartPatrolManager.GetInstance().LatestSmartPatrolResult.StartTime.ToString("yyyy-MM-dd HH:mm:ss");
                // 触发方式
                lblTriggerType.Text = SmartPatrolManager.GetInstance().LatestSmartPatrolResult.PatrolTriggerType.ToString();
                // 启动按钮
                btnStartDo.Text = "停止";
                // 查看结果
                btnOpenResult.Enabled = false;
                // 自动巡检定时配置
                btnTimerConfig.Enabled = false;
                // 过程信息
                txtResultInfo.Text = SmartPatrolManager.GetInstance().LatestSmartPatrolResult.GetResultStr();
            }
            else if(SmartPatrolManager.GetInstance().PatrolState == ePatrolState.未开始)
            {
                // 查看结果
                btnOpenResult.Enabled = false;
            }
            // 巡检完成
            // 巡检异常
            else
            {
                // 开始时间
                lblStartTime.Text = SmartPatrolManager.GetInstance().LatestSmartPatrolResult.StartTime.ToString("yyyy-MM-dd HH:mm:ss");
                // 触发方式
                lblTriggerType.Text = SmartPatrolManager.GetInstance().LatestSmartPatrolResult.PatrolTriggerType.ToString();
                // 过程信息
                txtResultInfo.Text = SmartPatrolManager.GetInstance().LatestSmartPatrolResult.GetResultStr();
            }
        }

        /// <summary>
        /// 初始化事件绑定
        /// </summary>
        private void InitEvent()
        {
            SmartPatrolManager.GetInstance().PatrolStartEvent += FrmSmartPatrolCtrl_PatrolStartEvent;
            SmartPatrolManager.GetInstance().PatrolEndEvent += FrmSmartPatrolCtrl_PatrolEndEvent;
            SmartPatrolManager.GetInstance().SingleUnitPatrolStartEvent += FrmSmartPatrolCtrl_SingleUnitPatrolStartEvent;
            SmartPatrolManager.GetInstance().SingleUnitPatrolEndEvent += FrmSmartPatrolCtrl_SingleUnitPatrolEndEvent;
        }

        #endregion
    }
}