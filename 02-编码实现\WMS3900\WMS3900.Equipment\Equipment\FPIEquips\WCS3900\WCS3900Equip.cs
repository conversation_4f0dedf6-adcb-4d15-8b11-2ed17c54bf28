﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using Fpi.Alarm;
using Fpi.Communication;
using Fpi.Communication.Converter;
using Fpi.Communication.Interfaces;
using Fpi.Data.Config;
using Fpi.Devices;
using Fpi.Devices.Channel;
using Fpi.Devices.DeviceProtocols;
using Fpi.Util.EnumRelated;
using Fpi.WMS3000.Equipment.Interface;
using Fpi.WMS3000.Equipment.UI;
using Fpi.WMS3000.Equipment.WCS3900;

namespace Fpi.WMS3000.Equipment
{
    /// <summary>
    /// 谱育科技WCS3900智能五参数仪表
    /// </summary>
    public class WCS3900Equip : Device, IWCSDeviceOperation
    {
        #region 字段属性

        /// <summary>
        /// 设备状态相关参数
        /// </summary>
        public WCS3900DeviceStateParam DeviceStateParams { get; } = new WCS3900DeviceStateParam();

        /// <summary>
        /// 设备日志区
        /// </summary>
        public WCS3900LogArea LogArea { get; } = new WCS3900LogArea();

        /// <summary>
        /// 公共区
        /// </summary>
        public WCS3900CommonParam CommonParam { get; } = new WCS3900CommonParam();

        #region 测量参数

        /// <summary>
        /// PH测量参数
        /// </summary>
        public WCS3900PHMeasureParam PHMeasureParam { get; } = new WCS3900PHMeasureParam();

        /// <summary>
        /// 电导率测量参数
        /// </summary>
        public WCS3900ConduMeasureParam ConduMeasureParam { get; } = new WCS3900ConduMeasureParam();

        /// <summary>
        /// 溶解氧测量参数
        /// </summary>
        public WCS3900OxyMeasureParam OxyMeasureParam { get; } = new WCS3900OxyMeasureParam();

        /// <summary>
        /// 浊度测量参数
        /// </summary>
        public WCS3900TurbMeasureParam TurbMeasureParam { get; } = new WCS3900TurbMeasureParam();

        /// <summary>
        /// 水温测量参数
        /// </summary>
        public WCS3900TempMeasureParam TempMeasureParam { get; } = new WCS3900TempMeasureParam();

        #endregion

        #endregion

        #region 构造

        public WCS3900Equip()
        {
            this.DeviceType = eDeviceType.WMS;

            if(string.IsNullOrEmpty(this.Description))
            {
                this.Description = "WCS3900五参数仪表。对应通道数据描述：1-PH;2-电导率;3-溶解氧;4-浊度;5-水温。";
            }

            if(this.id.Contains("DefaultID"))
            {
                this.id = "WCS3900_Equip";
            }

            if(this.name.Contains("DefaultName"))
            {
                this.name = "WCS3900五参数仪表";
            }

            if(string.IsNullOrEmpty(this.AlarmGroupId))
            {
                this.AlarmGroupId = "WCS3900_Equip";
            }

            if(string.IsNullOrEmpty(this.AlarmSourceId))
            {
                this.AlarmSourceId = "WCS3900_Equip";
            }

            if(string.IsNullOrEmpty(this.ProtocolImp))
            {
                this.ProtocolImp = typeof(ModbusProtocol).FullName;
            }
        }

        #endregion

        #region 公共（重写）方法

        public override string ToString()
        {
            return string.IsNullOrEmpty(this.name) || this.name.Contains("DefaultName") ? "谱育科技WCS3900智能五参数仪表" : this.name;
        }

        /// <summary>
        /// 报警设置
        /// </summary>
        public override void SetDeviceAlarmList()
        {
            base.SetDeviceAlarmList();

            this.DeviceAlarmList.Add("1", "主板1通讯异常");
            this.DeviceAlarmList.Add("2", "主板2通讯异常");
            this.DeviceAlarmList.Add("3", "主板3通讯异常");
            this.DeviceAlarmList.Add("4", "主板4通讯异常");
            this.DeviceAlarmList.Add("5", "pH标液（核查液）1余量不足");
            this.DeviceAlarmList.Add("6", "pH标液（核查液）2余量不足");
            this.DeviceAlarmList.Add("7", "pH标液（核查液）3余量不足");
            this.DeviceAlarmList.Add("8", "电导率标液（核查液）1余量不足");
            this.DeviceAlarmList.Add("9", "电导率标液（核查液）2余量不足");
            this.DeviceAlarmList.Add("10", "电导率标液（核查液）3余量不足");
            this.DeviceAlarmList.Add("11", "溶解氧标液（核查液）1余量不足");
            this.DeviceAlarmList.Add("12", "溶解氧标液（核查液）2余量不足");
            this.DeviceAlarmList.Add("13", "浊度标液（核查液）1余量不足");
            this.DeviceAlarmList.Add("14", "浊度标液（核查液）2余量不足");
            this.DeviceAlarmList.Add("15", "浊度标液（核查液）3余量不足");
            this.DeviceAlarmList.Add("16", "pH标液1保质期不足");
            this.DeviceAlarmList.Add("17", "pH标液2保质期不足");
            this.DeviceAlarmList.Add("18", "pH标液3保质期不足");
            this.DeviceAlarmList.Add("19", "电导率标液1保质期不足");
            this.DeviceAlarmList.Add("20", "电导率标液2保质期不足");
            this.DeviceAlarmList.Add("21", "电导率标液3保质期不足");
            this.DeviceAlarmList.Add("22", "溶解氧标液1保质期不足");
            this.DeviceAlarmList.Add("23", "溶解氧标液2保质期不足");
            this.DeviceAlarmList.Add("24", "浊度标液1保质期不足");
            this.DeviceAlarmList.Add("25", "浊度标液2保质期不足");
            this.DeviceAlarmList.Add("26", "浊度标液3保质期不足");
            this.DeviceAlarmList.Add("27", "pH核查1不通过");
            this.DeviceAlarmList.Add("28", "pH核查2不通过");
            this.DeviceAlarmList.Add("29", "pH核查3不合格");
            this.DeviceAlarmList.Add("30", "浊度核查1不通过");
            this.DeviceAlarmList.Add("31", "浊度核查2不通过");
            this.DeviceAlarmList.Add("32", "浊度核查3不通过");
            this.DeviceAlarmList.Add("33", "溶解氧核查1不通过");
            this.DeviceAlarmList.Add("34", "溶解氧核查2不通过");
            this.DeviceAlarmList.Add("35", "电导率核查1不通过");
            this.DeviceAlarmList.Add("36", "电导率核查2不通过");
            this.DeviceAlarmList.Add("37", "电导率核查3不通过");
            this.DeviceAlarmList.Add("38", "pH标定不通过");
            this.DeviceAlarmList.Add("39", "浊度标定不通过");
            this.DeviceAlarmList.Add("40", "溶解氧标定不通过");
            this.DeviceAlarmList.Add("41", "电导率标定不通过");
            this.DeviceAlarmList.Add("42", "pH通信异常");
            this.DeviceAlarmList.Add("43", "电导率通信异常");
            this.DeviceAlarmList.Add("44", "溶解氧通信异常");
            this.DeviceAlarmList.Add("45", "浊度通信异常");
            this.DeviceAlarmList.Add("46", "pH测量电势异常");
            this.DeviceAlarmList.Add("47", "pH标定时温度差大于0.5°C");
            this.DeviceAlarmList.Add("48", "pH标定异常");
            this.DeviceAlarmList.Add("49", "pH温度超范围");
            this.DeviceAlarmList.Add("50", "浊度超量程");
            this.DeviceAlarmList.Add("51", "浊度电机故障");
            this.DeviceAlarmList.Add("52", "浊度霍尔故障");
            this.DeviceAlarmList.Add("53", "溶解氧光强信号异常");
            this.DeviceAlarmList.Add("54", "溶解氧温度测量异常");
            this.DeviceAlarmList.Add("55", "溶解氧测试信号异常");
            this.DeviceAlarmList.Add("56", "电导值超过设定阈值");
            this.DeviceAlarmList.Add("57", "电导率温度小于0大于60");
            this.DeviceAlarmList.Add("58", "电导率标定时温度偏差大于0.3");
            this.DeviceAlarmList.Add("59", "水温核查不通过");

            this.DeviceAlarmList.Add("113", "pH主板上电");
            this.DeviceAlarmList.Add("114", "pH主板EEPROM故障");
            this.DeviceAlarmList.Add("115", "pH主板温度传感器故障");
            this.DeviceAlarmList.Add("116", "pH主板RTC故障");
            this.DeviceAlarmList.Add("117", "pH主板外部Flash故障");
            this.DeviceAlarmList.Add("118", "pH主板流程操作无效（无流路程序）");
            this.DeviceAlarmList.Add("119", "pH主板流程命令无效（流路错误）");
            this.DeviceAlarmList.Add("120", "pH进液异常");

            this.DeviceAlarmList.Add("145", "电导率主板上电");
            this.DeviceAlarmList.Add("146", "电导率主板EEPROM故障");
            this.DeviceAlarmList.Add("147", "电导率主板温度传感器故障");
            this.DeviceAlarmList.Add("148", "电导率主板RTC故障");
            this.DeviceAlarmList.Add("149", "电导率主板外部Flash故障");
            this.DeviceAlarmList.Add("150", "电导率主板流程操作无效（无流路程序）");
            this.DeviceAlarmList.Add("151", "电导率主板流程命令无效（流路错误）");
            this.DeviceAlarmList.Add("155", "电导率进液异常");

            this.DeviceAlarmList.Add("177", "溶解氧主板上电");
            this.DeviceAlarmList.Add("178", "溶解氧主板EEPROM故障");
            this.DeviceAlarmList.Add("179", "溶解氧主板温度传感器故障");
            this.DeviceAlarmList.Add("180", "溶解氧主板RTC故障");
            this.DeviceAlarmList.Add("181", "溶解氧主板外部Flash故障");
            this.DeviceAlarmList.Add("182", "溶解氧主板流程操作无效（无流路程序）");
            this.DeviceAlarmList.Add("183", "溶解氧主板流程命令无效（流路错误）");
            this.DeviceAlarmList.Add("186", "溶解氧进液异常");

            this.DeviceAlarmList.Add("209", "浊度主板上电");
            this.DeviceAlarmList.Add("210", "浊度主板EEPROM故障");
            this.DeviceAlarmList.Add("211", "浊度主板温度传感器故障");
            this.DeviceAlarmList.Add("212", "浊度主板RTC故障");
            this.DeviceAlarmList.Add("213", "浊度主板外部Flash故障");
            this.DeviceAlarmList.Add("214", "浊度主板流程操作无效（无流路程序）");
            this.DeviceAlarmList.Add("215", "浊度主板流程命令无效（流路错误）");
            this.DeviceAlarmList.Add("217", "浊度进液异常");

        }

        /// <summary>
        /// 设备状态参数查看界面
        /// </summary>
        public override UserControl GetDeviceParamUC()
        {
            return new UC_WCS3900Param(this);
        }

        public override void GetDeviceData()
        {
            try
            {
                #region 各电极数据读取

                try
                {
                    PHMeasureParam.UpdateData(ReadMeasureData(0x1000, 0x36), 3);
                }
                catch(Exception e)
                {
                    throw new Exception($"读取pH数据出错：{e.Message}");
                }

                try
                {
                    ConduMeasureParam.UpdateData(ReadMeasureData(0x2000, 0x22), 3);
                }
                catch(Exception e)
                {
                    throw new Exception($"读取电导率数据出错：{e.Message}");
                }

                try
                {
                    OxyMeasureParam.UpdateData(ReadMeasureData(0x3000, 0x27), 3);
                }
                catch(Exception e)
                {
                    throw new Exception($"读取溶解氧数据出错：{e.Message}");
                }

                try
                {
                    TurbMeasureParam.UpdateData(ReadMeasureData(0x4000, 0x36), 3);
                }
                catch(Exception e)
                {
                    throw new Exception($"读取浊度数据出错：{e.Message}");
                }

                try
                {
                    TempMeasureParam.UpdateData(ReadMeasureData(0x5000, 0x15), 3);
                }
                catch(Exception e)
                {
                    throw new Exception($"读取水温数据出错：{e.Message}");
                }

                #endregion

                #region 数据通道赋值

                if(this.InValueChannels.GetCount() > 0 && ((InValueChannel)this.InValueChannels[0]).ValueNode != null)
                {
                    ((InValueChannel)this.InValueChannels[0]).ChannelValue = PHMeasureParam.MeasureValue;
                    ((InValueChannel)this.InValueChannels[0]).ValueNode.State = (int)PHMeasureParam.WaterFlag;
                }
                if(this.InValueChannels.GetCount() > 1 && ((InValueChannel)this.InValueChannels[1]).ValueNode != null)
                {
                    ((InValueChannel)this.InValueChannels[1]).ChannelValue = ConduMeasureParam.MeasureValue;
                    ((InValueChannel)this.InValueChannels[1]).ValueNode.State = (int)ConduMeasureParam.WaterFlag;
                }
                if(this.InValueChannels.GetCount() > 2 && ((InValueChannel)this.InValueChannels[2]).ValueNode != null)
                {
                    ((InValueChannel)this.InValueChannels[2]).ChannelValue = OxyMeasureParam.MeasureValue;
                    ((InValueChannel)this.InValueChannels[2]).ValueNode.State = (int)OxyMeasureParam.WaterFlag;
                }
                if(this.InValueChannels.GetCount() > 3 && ((InValueChannel)this.InValueChannels[3]).ValueNode != null)
                {
                    ((InValueChannel)this.InValueChannels[3]).ChannelValue = TurbMeasureParam.MeasureValue;
                    ((InValueChannel)this.InValueChannels[3]).ValueNode.State = (int)TurbMeasureParam.WaterFlag;
                }
                if(this.InValueChannels.GetCount() > 4 && ((InValueChannel)this.InValueChannels[4]).ValueNode != null)
                {
                    ((InValueChannel)this.InValueChannels[4]).ChannelValue = TempMeasureParam.MeasureValue;
                    ((InValueChannel)this.InValueChannels[4]).ValueNode.State = (int)TempMeasureParam.WaterFlag;
                }

                #endregion

                //消除通信异常报警
                AlarmManager.GetInstance().RemoveAlarm(AlarmSourceId, ComErrorAlarmCodeId);
                this._communicationErrorCount = 0;
            }
            catch(Exception ex)
            {
                if(this._communicationErrorCount++ >= 3)
                {
                    foreach(InValueChannel inValueChl in InValueChannels)
                    {
                        inValueChl.ChannelValue = float.NaN;
                        if(inValueChl.ValueNode != null)
                        {
                            inValueChl.ValueNode.State = (int)eValueNodeState.F;
                        }
                    }

                    //增加通信异常报警
                    AlarmManager.GetInstance().AddAlarm(AlarmSourceId, ComErrorAlarmCodeId);
                }

                throw new Exception($"[{name}]读数失败：{ex.Message}");
            }
            finally
            {
                SetRealDataToNode();
            }
        }

        /// <summary>
        /// 获取设备报警及状态数据
        /// </summary>
        public override void GetDeviceState()
        {
            try
            {
                #region 公共区

                byte[] dataSend = { byte.Parse(this.Addr), 0x03, 0x00, 0x00, 0x00, 0x54 };
                IByteStream bs = new ByteArrayWrap(dataSend);
                IByteStream bsRecv = SendToDevice(id, bs);
                if(bsRecv == null)
                {
                    throw new Exception("读取状态参数无数据回应!");
                }
                byte[] dataReceive = bsRecv.GetBytes();

                if(dataReceive.Length > 2 && dataReceive[1] == 0x83)
                {
                    throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                }
                if(dataReceive.Length < 171)
                {
                    throw new Exception("读取状态参数回应数据长度不合法！");
                }
                if(dataReceive[2] != 0xA8)
                {
                    throw new Exception("读取状态参数回应数据错位！");
                }

                // 更新状态参数
                DeviceStateParams.UpdateValue(dataReceive, 3);

                // 解析报警
                ParseDeviceAlarm(dataReceive, 9);

                // 更新日志
                LogArea.UpdataValue(dataReceive, 51, this.id);

                #endregion

                // PH公共区
                ReadCommonnData(eWCSNodeType.w01001, 0x01);

                // 电导率公共区
                ReadCommonnData(eWCSNodeType.w01014, 0x02);

                // 溶解氧公共区
                ReadCommonnData(eWCSNodeType.w01009, 0x03);

                // 浊度公共区
                ReadCommonnData(eWCSNodeType.w01003, 0x04);

                #region 版本信息区域

                dataSend = new byte[] { byte.Parse(this.Addr), 0x03, 0x08, 0x00, 0x00, 0x26 };
                bs = new ByteArrayWrap(dataSend);
                bsRecv = SendToDevice(id, bs);
                if(bsRecv == null)
                {
                    throw new Exception("读取版本信息区域无数据回应!");
                }
                dataReceive = bsRecv.GetBytes();

                if(dataReceive.Length > 2 && dataReceive[1] == 0x83)
                {
                    throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                }
                if(dataReceive.Length < 79)
                {
                    throw new Exception("读取版本信息区域数据长度不合法！");
                }
                if(dataReceive[2] != 0x4C)
                {
                    throw new Exception("读取版本信息区域回应数据错位！");
                }
                CommonParam.UpdateValue(dataReceive, 3);

                #endregion

                //消除通信异常报警
                AlarmManager.GetInstance().RemoveAlarm(AlarmSourceId, ComErrorAlarmCodeId);
                this._communicationErrorCount = 0;
            }
            catch(Exception ex)
            {
                if(this._communicationErrorCount++ >= 3)
                {
                    //增加通信异常报警
                    AlarmManager.GetInstance().AddAlarm(AlarmSourceId, ComErrorAlarmCodeId);
                }

                throw new Exception($"[{name}]读数失败：{ex.Message}");
            }
        }

        #endregion

        #region 公共方法

        #region 不定参数读写

        #region 写

        /// <summary>
        /// 下发多个参数到设备
        /// </summary>
        /// <param name="paramList"></param>
        public void WriteParamToDevice(short startIndex, List<object> paramList)
        {
            // 组装数据段
            List<byte> dataArea = new List<byte>();
            foreach(var param in paramList)
            {
                if(param is uint)
                {
                    dataArea.AddRange(DataConverter.GetInstance().GetBytes((ulong)param));
                }
                else if(param is int)
                {
                    dataArea.AddRange(DataConverter.GetInstance().GetBytes((long)param));
                }
                else if(param is ushort)
                {
                    dataArea.AddRange(DataConverter.GetInstance().GetBytes((uint)param));
                }
                else if(param is short)
                {
                    dataArea.AddRange(DataConverter.GetInstance().GetBytes((int)param));
                }
                else
                {
                    dataArea.AddRange(DataConverter.GetInstance().GetBytes((float)param));
                }
            }

            // 拼接完整报文
            List<byte> dataSend = new List<byte> { byte.Parse(this.Addr), 0x10, (byte)(startIndex >> 8), (byte)startIndex, 0x00, (byte)(dataArea.Count / 2), (byte)dataArea.Count };
            dataSend.AddRange(dataArea);
            IByteStream bs = new ByteArrayWrap(dataSend.ToArray());
            IByteStream bsRecv = SendToDevice(id, bs);
            if(bsRecv == null)
            {
                throw new Exception("无数据回应!");
            }
            byte[] dataReceive = bsRecv.GetBytes();

            if(dataReceive.Length > 2 && dataReceive[1] == 0x90)
            {
                throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
            }
            if(dataReceive.Length < 7)
            {
                throw new Exception("回应数据长度不合法！");
            }
        }

        /// <summary>
        /// 下发Float类型参数到设备
        /// </summary>
        /// <param name="param"></param>
        public void WriteFloatParamToDevice(short startIndex, float param)
        {
            List<byte> dataSend = new List<byte> { byte.Parse(this.Addr), 0x10, (byte)(startIndex >> 8), (byte)startIndex, 0x00, 0x02, 0x04 };
            dataSend.AddRange(DataConverter.GetInstance().GetBytes(param));
            IByteStream bs = new ByteArrayWrap(dataSend.ToArray());
            IByteStream bsRecv = SendToDevice(id, bs);
            if(bsRecv == null)
            {
                throw new Exception("无数据回应!");
            }
            byte[] dataReceive = bsRecv.GetBytes();

            if(dataReceive.Length > 2 && dataReceive[1] == 0x90)
            {
                throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
            }
            if(dataReceive.Length < 7)
            {
                throw new Exception("回应数据长度不合法！");
            }
        }

        /// <summary>
        /// 下发Int16类型参数到设备
        /// </summary>
        /// <param name="param"></param>
        public void WriteInt16ParamToDevice(short startIndex, short param)
        {
            List<byte> dataSend = new List<byte> { byte.Parse(this.Addr), 0x10, (byte)(startIndex >> 8), (byte)startIndex, 0x00, 0x01, 0x02 };
            dataSend.AddRange(DataConverter.GetInstance().GetBytes(param));
            IByteStream bs = new ByteArrayWrap(dataSend.ToArray());
            IByteStream bsRecv = SendToDevice(id, bs);
            if(bsRecv == null)
            {
                throw new Exception("无数据回应!");
            }
            byte[] dataReceive = bsRecv.GetBytes();

            if(dataReceive.Length > 2 && dataReceive[1] == 0x90)
            {
                throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
            }
            if(dataReceive.Length < 7)
            {
                throw new Exception("回应数据长度不合法！");
            }
        }

        /// <summary>
        /// 下发Int32类型参数到设备
        /// </summary>
        /// <param name="param"></param>
        public void WriteInt32ParamToDevice(short startIndex, int param)
        {
            List<byte> dataSend = new List<byte> { byte.Parse(this.Addr), 0x10, (byte)(startIndex >> 8), (byte)startIndex, 0x00, 0x02, 0x04 };
            dataSend.AddRange(DataConverter.GetInstance().GetBytes((long)param));
            IByteStream bs = new ByteArrayWrap(dataSend.ToArray());
            IByteStream bsRecv = SendToDevice(id, bs);
            if(bsRecv == null)
            {
                throw new Exception("无数据回应!");
            }
            byte[] dataReceive = bsRecv.GetBytes();

            if(dataReceive.Length > 2 && dataReceive[1] == 0x90)
            {
                throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
            }
            if(dataReceive.Length < 7)
            {
                throw new Exception("回应数据长度不合法！");
            }
        }

        #endregion

        #region 读

        /// <summary>
        /// 从设备上读取Float类型参数
        /// </summary>
        /// <param name="param"></param>
        public float ReadFloatParamFromDevice(short startIndex)
        {
            byte[] dataSend = { byte.Parse(this.Addr), 0x03, (byte)(startIndex >> 8), (byte)startIndex, 0x00, 0x02 };
            IByteStream bs = new ByteArrayWrap(dataSend);
            IByteStream bsRecv = SendToDevice(id, bs);
            if(bsRecv == null)
            {
                throw new Exception("无数据回应!");
            }
            byte[] dataReceive = bsRecv.GetBytes();

            if(dataReceive.Length > 2 && dataReceive[1] == 0x83)
            {
                throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
            }
            if(dataReceive.Length < 7)
            {
                throw new Exception("回应数据长度不合法！");
            }
            if(dataReceive[2] != 0x04)
            {
                throw new Exception("回应数据错位！");
            }

            return (float)DataConverter.GetInstance().ToSingle(dataReceive, 3);
        }

        /// <summary>
        /// 从设备上读取Int16类型参数
        /// </summary>
        /// <param name="param"></param>
        public int ReadInt16ParamFromDevice(short startIndex)
        {
            byte[] dataSend = { byte.Parse(this.Addr), 0x03, (byte)(startIndex >> 8), (byte)startIndex, 0x00, 0x01 };
            IByteStream bs = new ByteArrayWrap(dataSend);
            IByteStream bsRecv = SendToDevice(id, bs);
            if(bsRecv == null)
            {
                throw new Exception("无数据回应!");
            }
            byte[] dataReceive = bsRecv.GetBytes();

            if(dataReceive.Length > 2 && dataReceive[1] == 0x83)
            {
                throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
            }
            if(dataReceive.Length < 7)
            {
                throw new Exception("回应数据长度不合法！");
            }

            if(dataReceive[2] != 0x02)
            {
                throw new Exception("回应数据错位！");
            }

            return DataConverter.GetInstance().ToInt32(dataReceive, 3);
        }

        /// <summary>
        /// 从设备上读取Int32类型参数
        /// </summary>
        /// <param name="param"></param>
        public int ReadInt32ParamFromDevice(short startIndex)
        {
            byte[] dataSend = { byte.Parse(this.Addr), 0x03, (byte)(startIndex >> 8), (byte)startIndex, 0x00, 0x02 };
            IByteStream bs = new ByteArrayWrap(dataSend);
            IByteStream bsRecv = SendToDevice(id, bs);
            if(bsRecv == null)
            {
                throw new Exception("无数据回应!");
            }
            byte[] dataReceive = bsRecv.GetBytes();

            if(dataReceive.Length > 2 && dataReceive[1] == 0x83)
            {
                throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
            }
            if(dataReceive.Length < 7)
            {
                throw new Exception("回应数据长度不合法！");
            }
            if(dataReceive[2] != 0x04)
            {
                throw new Exception("回应数据错位！");
            }

            return (int)DataConverter.GetInstance().ToInt64(dataReceive, 3);
        }

        #endregion

        #endregion

        #region 时间校准

        public void SetDeviceTime(DateTime time)
        {
            try
            {
                byte[] dataSend ={byte.Parse(Addr), 0x10, 0xB0, 0x01, 0x00, 0x03, 0x06,
                (byte)(time.Year - 2000),(byte)time.Month, (byte)time.Day,(byte)time.Hour,(byte)time.Minute, (byte) time.Second };

                IByteStream bs = new ByteArrayWrap(dataSend);
                IByteStream bsRecv = SendToDevice(id, bs);
                if(bsRecv == null)
                {
                    throw new Exception("无数据回应!");
                }
                byte[] dataReceive = bsRecv.GetBytes();

                if(dataReceive.Length > 2 && dataReceive[1] == 0x90)
                {
                    throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                }
                if(dataReceive.Length < 7)
                {
                    throw new Exception("回应数据长度不合法！");
                }
            }
            catch(Exception ex)
            {
                throw new Exception($"[{name}]校准时间失败：{ex.Message}");
            }
        }

        #endregion

        #region 版本信息读取

        /// <summary>
        /// 版本信息读取
        /// </summary>
        /// <param name="softVersion">界面软件版本</param>
        /// <param name="mainBoardVersion">主板软件版本</param>
        /// <param name="sn">SN</param>
        /// <exception cref="Exception"></exception>
        public void GetVersionSN(out string softVersion, out string mainBoardVersion, out string sn)
        {
            try
            {
                byte[] dataSend = { byte.Parse(this.Addr), 0x03, 0x03, 0x00, 0x00, 0x26 };
                IByteStream bs = new ByteArrayWrap(dataSend);
                IByteStream bsRecv = SendToDevice(id, bs);
                if(bsRecv == null)
                {
                    throw new Exception("无数据回应!");
                }
                byte[] dataReceive = bsRecv.GetBytes();

                if(dataReceive.Length > 2 && dataReceive[1] == 0x83)
                {
                    throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                }
                if(dataReceive.Length < 79)
                {
                    throw new Exception("回应数据长度不合法！");
                }
                if(dataReceive[2] != 0x4C)
                {
                    throw new Exception("回应数据错位！");
                }

                softVersion = Encoding.UTF8.GetString(dataReceive, 3, 32).Replace("\0", "");
                mainBoardVersion = Encoding.UTF8.GetString(dataReceive, 35, 32).Replace("\0", "");
                sn = Encoding.UTF8.GetString(dataReceive, 67, 12).Replace("\0", "");
            }
            catch(Exception ex)
            {
                throw new Exception($"[{name}]读取版本信息失败：{ex.Message}");
            }
        }

        #endregion

        #region 点位控制

        /// <summary>
        /// 器件控制
        /// </summary>
        /// <param name="elementType"></param>
        /// <param name="state"></param>
        public void ElementControl(eWCS3900ElementType elementType, bool state, eWCSNodeType dataType)
        {
            try
            {
                List<byte> dataSend = new List<byte> { byte.Parse(Addr), 0x10, 0xB0, 0x00,0x00, 0x02, 0x04,0x00,(byte)dataType,
                    (byte)elementType, state ? (byte)1 : (byte)0 };
                IByteStream bs = new ByteArrayWrap(dataSend.ToArray());
                IByteStream bsRecv = SendToDevice(id, bs);
                if(bsRecv == null)
                {
                    throw new Exception("无数据回应!");
                }
                byte[] dataReceive = bsRecv.GetBytes();

                if(dataReceive.Length > 2 && dataReceive[1] == 0x90)
                {
                    throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                }
                if(dataReceive.Length < 7)
                {
                    throw new Exception("回应数据长度不合法！");
                }
            }
            catch(Exception ex)
            {
                throw new Exception($"[{name}]器件控制失败：{ex.Message}");
            }
        }

        #endregion

        #region 工作模式

        /// <summary>
        /// 工作模式切换
        /// </summary>
        /// <param name="elementType"></param>
        /// <param name="state"></param>
        public void SwitchWorkModel(eWCS3900WorkModel workModel)
        {
            try
            {
                List<byte> dataSend = new List<byte> { byte.Parse(Addr), 0x10, 0x00, 0x02, 0x00, 0x01, 0x02, 0x00, (byte)workModel };
                IByteStream bs = new ByteArrayWrap(dataSend.ToArray());
                IByteStream bsRecv = SendToDevice(id, bs);
                if(bsRecv == null)
                {
                    throw new Exception("无数据回应!");
                }
                byte[] dataReceive = bsRecv.GetBytes();

                if(dataReceive.Length > 2 && dataReceive[1] == 0x90)
                {
                    throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                }
                if(dataReceive.Length < 7)
                {
                    throw new Exception("回应数据长度不合法！");
                }
            }
            catch(Exception ex)
            {
                throw new Exception($"[{name}]工作模式切换失败：{ex.Message}");
            }
        }

        #endregion

        #region 读取核查过程溯源数据

        /// <summary>
        /// 读取核查过程溯源数据
        /// </summary>
        public void ReadCheckDetailData(eWCSNodeType nodeType)
        {
            if(nodeType == eWCSNodeType.w01001)
            {
                try
                {
                    int GroupLength = PHMeasureParam.CheckData.CheckDetailDataCount;
                    if(GroupLength > 0)
                    {
                        var totalData = ReadAllPackageCheckDetailData(GroupLength, 20, 6, 0x1036);
                        PHMeasureParam.CheckData.UpdateDetailData(totalData, 0);
                    }
                }
                catch(Exception e)
                {
                    throw new Exception($"读取{this.name}pH过程溯源数据出错：{e.Message}");
                }
            }
            else if(nodeType == eWCSNodeType.w01014)
            {
                try
                {
                    int GroupLength = ConduMeasureParam.CheckData.CheckDetailDataCount;
                    if(GroupLength > 0)
                    {
                        var totalData = ReadAllPackageCheckDetailData(GroupLength, 30, 4, 0x2022);
                        ConduMeasureParam.CheckData.UpdateDetailData(totalData, 0);
                    }
                }
                catch(Exception e)
                {
                    throw new Exception($"读取{this.name}电导率过程溯源数据出错：{e.Message}");
                }
            }
            else if(nodeType == eWCSNodeType.w01009)
            {
                try
                {
                    int GroupLength = OxyMeasureParam.CheckData.CheckDetailDataCount;
                    if(GroupLength > 0)
                    {
                        var totalData = ReadAllPackageCheckDetailData(GroupLength, 20, 6, 0x3027);
                        OxyMeasureParam.CheckData.UpdateDetailData(totalData, 0);
                    }
                }
                catch(Exception e)
                {
                    throw new Exception($"读取{this.name}溶解氧过程溯源数据出错：{e.Message}");
                }
            }
            else if(nodeType == eWCSNodeType.w01003)
            {
                try
                {
                    int GroupLength = TurbMeasureParam.CheckData.CheckDetailDataCount;
                    if(GroupLength > 0)
                    {
                        var totalData = ReadAllPackageCheckDetailData(GroupLength, 20, 6, 0x4036);
                        TurbMeasureParam.CheckData.UpdateDetailData(totalData, 0);
                    }
                }
                catch(Exception e)
                {
                    throw new Exception($"读取{this.name}浊度过程溯源数据出错：{e.Message}");
                }
            }
            else if(nodeType == eWCSNodeType.w01010)
            {
                try
                {
                    int GroupLength = TempMeasureParam.CheckData.CheckDetailDataCount;
                    if(GroupLength > 0)
                    {
                        var totalData = ReadAllPackageCheckDetailData(GroupLength, 30, 4, 0x5015);
                        TempMeasureParam.CheckData.UpdateDetailData(totalData, 0);
                    }
                }
                catch(Exception e)
                {
                    throw new Exception($"读取{this.name}水温过程溯源数据出错：{e.Message}");
                }
            }
        }

        #endregion

        #endregion

        #region IWCSDeviceOperation

        ///// <summary>
        ///// 最近一次核查或者水样比对的控制参数
        ///// 保存数据时用来比对，应该保存哪几个因子数据
        ///// </summary>
        public Dictionary<eWCSNodeType, WCS39900OperParams> LastOperNedParams { get; set; }

        public void StartOper(eWCSOperType opType, Dictionary<eWCSNodeType, WCS39900OperParams> operNedParams = null)
        {
            try
            {
                List<byte> dataSend = new List<byte>();

                switch(opType)
                {
                    case eWCSOperType.核查:
                    case eWCSOperType.比对:
                        dataSend.AddRange(new byte[] { byte.Parse(Addr), 0x10, 0xB0, 0x05, 0x00, 0x06, 0x0C,0x00,(byte)opType,
                    operNedParams[eWCSNodeType.w01001].IsEnable?(byte)0x01:(byte)0x00,operNedParams[eWCSNodeType.w01001].ParamType,
                    operNedParams[eWCSNodeType.w01014].IsEnable?(byte)0x01:(byte)0x00,operNedParams[eWCSNodeType.w01014].ParamType,
                    operNedParams[eWCSNodeType.w01009].IsEnable?(byte)0x01:(byte)0x00,operNedParams[eWCSNodeType.w01009].ParamType,
                    operNedParams[eWCSNodeType.w01003].IsEnable?(byte)0x01:(byte)0x00,operNedParams[eWCSNodeType.w01003].ParamType,
                    operNedParams[eWCSNodeType.w01010].IsEnable?(byte)0x01:(byte)0x00,operNedParams[eWCSNodeType.w01010].ParamType});
                        break;
                    case eWCSOperType.标定:
                        dataSend.AddRange(new byte[] { byte.Parse(Addr), 0x10, 0xB0, 0x05, 0x00, 0x05, 0x0A ,0x00,(byte)opType,
                    operNedParams[eWCSNodeType.w01001].IsEnable?(byte)0x01:(byte)0x00,0x00,
                    operNedParams[eWCSNodeType.w01014].IsEnable?(byte)0x01:(byte)0x00,0x00,
                    operNedParams[eWCSNodeType.w01009].IsEnable?(byte)0x01:(byte)0x00,0x00,
                    operNedParams[eWCSNodeType.w01003].IsEnable?(byte)0x01:(byte)0x00,0x00});
                        break;
                    case eWCSOperType.五参数测量:
                    case eWCSOperType.复位排空清洗:
                    case eWCSOperType.紧急停止:
                        dataSend.AddRange(new byte[] { byte.Parse(Addr), 0x10, 0xB0, 0x05, 0x00, 0x01, 0x02, 0x00, (byte)opType });
                        break;
                }

                IByteStream bs = new ByteArrayWrap(dataSend.ToArray());
                IByteStream bsRecv = SendToDevice(id, bs) ?? throw new Exception("无数据回应！");
                byte[] dataRecv = bsRecv.GetBytes();
                if(dataRecv.Length > 2 && dataRecv[1] == 0x90)
                {
                    throw new Exception($"{(eControlErrorType)dataRecv[2]}!");
                }

                if(dataRecv.Length < 7)
                {
                    throw new Exception("回应数据长度不合法！");
                }

                // 更新核查或水样比对控制参数
                if(opType == eWCSOperType.比对 || opType == eWCSOperType.核查)
                {
                    LastOperNedParams = operNedParams;
                }
            }
            catch(Exception e)
            {
                throw new Exception($"触发{this.name}执行{opType}流程出错：{e.Message}");
            }
        }

        /// <summary>
        /// 保存核查数据到特定数据库
        /// </summary>
        public void SaveCheckDataToDB(eWCSNodeType nodeType, DateTime dateTime)
        {
            try
            {
                ReadCheckDetailData(nodeType);
            }
            catch
            {
            }

            switch(nodeType)
            {
                case eWCSNodeType.w01001:
                    PHMeasureParam.SaveToDb(dateTime);
                    break;
                case eWCSNodeType.w01014:
                    ConduMeasureParam.SaveToDb(dateTime);
                    break;
                case eWCSNodeType.w01009:
                    OxyMeasureParam.SaveToDb(dateTime);
                    break;
                case eWCSNodeType.w01003:
                    TurbMeasureParam.SaveToDb(dateTime);
                    break;
                case eWCSNodeType.w01010:
                    TempMeasureParam.SaveToDb(dateTime);
                    break;
            }
        }

        #endregion

        #region 私有方法

        #region 解析报警

        /// <summary>
        /// 解析报警
        /// </summary>
        /// <param name="dataReceive"></param>
        private void ParseDeviceAlarm(byte[] dataReceive, int startIndex)
        {
            if(dataReceive.Length < startIndex + 30)
            {
                throw new Exception("读取设备报警回应数据不完整！");
            }

            // 遍历索引startIndex开始的30个字节
            for(int i = 0; i < 15; i++)
            {
                // 取一个寄存器的报警信息
                int alarmInfo = DataConverter.GetInstance().ToInt32(dataReceive, startIndex + i);
                // 遍历齐数据位
                for(int j = 1; j <= 16; j++)
                {
                    // 本位对应报警的报警码号
                    int alarmCode = j + i * 16;

                    //// 当前上传的报警码只有86个，提高遍历效率，超过86时直接退出
                    //if(alarmCode > 86)
                    //{
                    //    return;
                    //}

                    //  判断最低位是否为1
                    if((alarmInfo & 0x0001) == 0x0001)
                    {
                        AlarmManager.GetInstance().AddAlarm(AlarmSourceId, alarmCode.ToString());
                    }
                    else
                    {
                        AlarmManager.GetInstance().RemoveAlarm(AlarmSourceId, alarmCode.ToString());
                    }

                    // 移位
                    alarmInfo >>= 1;
                }
            }
        }

        #endregion

        #region 读取测量数据

        /// <summary>
        /// 读取测量数据
        /// </summary>
        /// <param name="startIndex">起始寄存器地址</param>
        /// <param name="registerCount">寄存器数量</param>
        /// <returns></returns>
        private byte[] ReadMeasureData(short startIndex, short registerCount)
        {
            byte[] dataSend = { byte.Parse(this.Addr), 0x03, (byte)(startIndex >> 8), (byte)startIndex, (byte)(registerCount >> 8), (byte)registerCount };
            IByteStream bs = new ByteArrayWrap(dataSend);
            IByteStream bsRecv = SendToDevice(id, bs);
            if(bsRecv == null)
            {
                throw new Exception("无数据回应!");
            }
            byte[] dataReceive = bsRecv.GetBytes();

            if(dataReceive.Length > 2 && dataReceive[1] == 0x83)
            {
                throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
            }
            if(dataReceive.Length < registerCount * 2 + 3)
            {
                throw new Exception("回应数据长度不合法！");
            }
            if(dataReceive[2] != registerCount * 2)
            {
                throw new Exception("回应数据错位！");
            }

            return new ArraySegment<byte>(dataReceive, 3, registerCount * 2).Array;
        }

        #endregion

        #region 读取过程溯源数据

        /// <summary>
        /// 分包读取长段的数据
        /// 每包最多120个寄存器
        /// </summary>
        /// <param name="GroupLength">数据组数量</param>
        /// <param name="oneGroupCount">一包读取的数据组数</param>
        /// <param name="oneGroupRegisterLength">一组数据寄存器数量</param>
        /// <param name="nodeName">因子名</param>
        /// <returns></returns>
        private byte[] ReadAllPackageCheckDetailData(int GroupLength, int oneGroupCount, int oneGroupRegisterLength, short firstRegisterAddr)
        {
            // 总数据
            List<byte> totalData = new List<byte>();

            // 当前数据包数
            int packageIndex = 1;
            // 遍历读取各包
            while((packageIndex - 1) * oneGroupCount < GroupLength)
            {
                // 起始地址
                short startIndex = (short)(firstRegisterAddr + ((packageIndex - 1) * oneGroupCount * oneGroupRegisterLength));
                // 本包数据寄存器数量
                short thisPackageRegisterCount = (short)(GroupLength >= (packageIndex * oneGroupCount) ? oneGroupCount * oneGroupRegisterLength : GroupLength % oneGroupCount * oneGroupRegisterLength);

                var dataSend = new byte[] { byte.Parse(this.Addr), 0x03, (byte)(startIndex >> 8), (byte)startIndex, (byte)(thisPackageRegisterCount >> 8), (byte)thisPackageRegisterCount };
                var bs = new ByteArrayWrap(dataSend);
                var bsRecv = SendToDevice(id, bs);
                if(bsRecv == null)
                {
                    throw new Exception($"读取第{packageIndex}包过程溯源数据无数据回应!");
                }
                byte[] dataReceive = bsRecv.GetBytes();

                if(dataReceive.Length > 2 && dataReceive[1] == 0x83)
                {
                    throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                }
                if(dataReceive.Length < (thisPackageRegisterCount * 2 + 3))
                {
                    throw new Exception($"读取第{packageIndex}包过程溯源数据回应数据长度不合法！");
                }
                if(dataReceive[2] != (thisPackageRegisterCount * 2))
                {
                    throw new Exception($"读取第{packageIndex}包过程溯源数据回应数据错位！");
                }

                // 融合数据段
                ArraySegment<byte> thisPackageData = new ArraySegment<byte>(dataReceive, 3, thisPackageRegisterCount * 2);
                totalData.AddRange(thisPackageData);

                packageIndex++;
            }

            return totalData.ToArray();
        }

        #endregion

        #region 读公共寄存器区

        private void ReadCommonnData(eWCSNodeType eWCSNodeType, byte code)
        {
            if(eWCSNodeType == eWCSNodeType.w01009)
            {
                byte[] dataSend = new byte[] { byte.Parse(Addr), 0x03, code, 0x00, 0x00, 0x30 };
                IByteStream bs = new ByteArrayWrap(dataSend);
                IByteStream bsRecv = SendToDevice(id, bs) ?? throw new Exception($"读取{eWCSNodeType}公共区无数据回应!");

                byte[] dataReceive = bsRecv.GetBytes();

                if(dataReceive.Length > 2 && dataReceive[1] == 0x83)
                {
                    throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                }
                if(dataReceive.Length < 99)
                {
                    throw new Exception($"读取{EnumOperate.GetEnumDesc(eWCSNodeType)}公共区回应数据长度不足！");
                }
                if(dataReceive[2] != 0x60)
                {
                    throw new Exception($"读取{EnumOperate.GetEnumDesc(eWCSNodeType)}公共区回应数据错位！");
                }

                CommonParam.CommonParams[eWCSNodeType].UpdateValue(dataReceive, 3, eWCSNodeType);
            }
            else
            {
                byte[] dataSend = new byte[] { byte.Parse(Addr), 0x03, code, 0x00, 0x00, 0x37 };
                IByteStream bs = new ByteArrayWrap(dataSend);
                IByteStream bsRecv = SendToDevice(id, bs) ?? throw new Exception($"读取{eWCSNodeType}公共区无数据回应!");

                byte[] dataReceive = bsRecv.GetBytes();

                if(dataReceive.Length > 2 && dataReceive[1] == 0x83)
                {
                    throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                }
                if(dataReceive.Length < 113)
                {
                    throw new Exception($"读取{EnumOperate.GetEnumDesc(eWCSNodeType)}公共区回应数据长度不足！");
                }
                if(dataReceive[2] != 0x6E)
                {
                    throw new Exception($"读取{EnumOperate.GetEnumDesc(eWCSNodeType)}公共区回应数据错位！");
                }

                CommonParam.CommonParams[eWCSNodeType].UpdateValue(dataReceive, 3, eWCSNodeType);
            }
        }

        #endregion

        #endregion    
    }

    /// <summary>
    /// WCS3900流程参数
    /// </summary>
    public struct WCS39900OperParams
    {
        /// <summary>
        /// 是否启用
        /// </summary>
        public bool IsEnable;

        /// <summary>
        /// 核查或比对类型
        /// </summary>
        public byte ParamType;

        public WCS39900OperParams(bool isEnable, byte type)
        {
            IsEnable = isEnable;
            ParamType = type;
        }
    }
}