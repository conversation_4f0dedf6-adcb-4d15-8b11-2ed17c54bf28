﻿using System;
using System.Collections.Generic;
using Fpi.WMS3000.Equipment.Interface;
using Fpi.WMS3000.SystemConfig.ImagePatrol.Config;
using Sunny.UI;

namespace Fpi.WMS3000.SystemConfig.ImagePatrol.UI
{
    /// <summary>
    /// 图像巡检信息显示界面
    /// 外部界面传值，或者显示最新图像巡检结果数据
    /// </summary>
    public partial class UC_ImagePatrolResult : UIUserControl, IRefreshUI
    {
        #region 字段属性

        /// <summary>
        /// 界面对应图像巡检结果
        /// </summary>
        private OldImagePatrolResult _imagePatrolResult;

        #endregion

        #region 构造

        public UC_ImagePatrolResult()
        {
            InitializeComponent();
        }

        #endregion

        #region 事件

        /// <summary>
        /// 点击刷新按钮
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnRefresh_Click(object sender, EventArgs e)
        {
            RefreshUI();
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 是否显示刷新按钮
        /// </summary>
        /// <param name="visible"></param>
        public void SetRefreshBtnVisible(bool visible)
        {
            btnRefresh.Visible = visible;
        }

        /// <summary>
        /// 设置关联的巡检结果
        /// </summary>
        /// <param name="imagePatrolResult"></param>
        public void SetTragetPatrolResult(OldImagePatrolResult imagePatrolResult)
        {
            _imagePatrolResult = imagePatrolResult;
            RefreshUI();
        }

        #endregion

        #region IRefreshUI

        public void RefreshUI()
        {
            var imagePatrolResult = _imagePatrolResult;
            // 未有传入值时，显示最新巡检信息
            if(imagePatrolResult == null)
            {
                imagePatrolResult = ImagePatrolManager.GetInstance().LatestImagePatrolResult;
            }

            if(imagePatrolResult != null)
            {
                // 开始时间
                lblStartTime.Text = imagePatrolResult.StartTime.ToString("yyyy-MM-dd HH:mm:ss");
                // 触发方式
                lblPatrolTriggerType.Text = imagePatrolResult.PatrolTriggerType.ToString();

                // 添加各模块显示内容

                uc_SandSink.SetPicture(imagePatrolResult.SandSinkCameraImagePath);
                uc_SandSink.SetInfo(new List<string> { $"配水预处理沉砂池脏污状态：{imagePatrolResult.SandSinkSmutState}" });

                uc_Pipe.SetPicture(imagePatrolResult.PipeCameraImagePath);
                uc_Pipe.SetInfo(new List<string> { $"配水预处理管路脏污状态：{imagePatrolResult.PipeSmutState}" });

                uc_FiveParamFlowPool.SetPicture(imagePatrolResult.FiveParamFlowPoolCameraImagePath);
                uc_FiveParamFlowPool.SetInfo(new List<string> { $"五参数流通池脏污状态：{imagePatrolResult.FiveParamFlowPoolState}" });

                uc_FiveParamBucket.SetPicture(imagePatrolResult.FiveParamBucketCameraImagePath);
                uc_FiveParamBucket.SetInfo(
                    new List<string>
                    {
                        //$"五参数废水桶状态：{imagePatrolResult.FiveParamWasteWaterBucketState}",
                        $"五参数废液桶状态：{imagePatrolResult.FiveParamWasteTankBucketState}",
                        $"五参数纯水桶状态：{imagePatrolResult.FiveParamWaterBucketState}"
                    });

                uc_CodMnNH4Equip.SetPicture(imagePatrolResult.CodMnNH4EquipCameraImagePath);
                uc_CodMnNH4Equip.SetInfo(
                    new List<string>
                    {
                        $"高指管路脏污状态：{imagePatrolResult.CodMnPipeSmutState}",
                        $"高指反应单元脏污状态：{imagePatrolResult.CodMnReactionUnitSmutState}",
                        $"氨氮管路脏污状态：{imagePatrolResult.NH4PipeSmutState}",
                        $"氨氮反应单元脏污状态：{imagePatrolResult.NH4ReactionUnitSmutState}"
                    });

                uc_TPTNEquip.SetPicture(imagePatrolResult.TPTNEquipCameraImagePath);
                uc_TPTNEquip.SetInfo(
                    new List<string>
                    {
                        $"总磷管路脏污状态：{imagePatrolResult.TPPipeSmutState}",
                        $"总磷反应单元脏污状态：{imagePatrolResult.TPReactionUnitSmutState}",
                        $"总氮管路脏污状态：{imagePatrolResult.TNPipeSmutState}",
                        $"总氮反应单元脏污状态：{imagePatrolResult.TNReactionUnitSmutState}"
                    });

                uc_CodMnNH4QCD.SetPicture(imagePatrolResult.CodMnNH4QCDCameraImagePath);
                uc_CodMnNH4QCD.SetInfo(
                    new List<string>
                    {
                        $"高指质控水样管状态：{imagePatrolResult.CodMnWaterTubeSmutState}",
                        $"高指质控标样管状态：{imagePatrolResult.CodMnSampleTubeSmutState}",
                        $"高指质控水样杯脏污：{imagePatrolResult.CodMnWaterCupSmutState}",
                        $"高指质控标样杯脏污：{imagePatrolResult.CodMnSampleCupSmutState}",
                        $"氨氮质控水样管状态：{imagePatrolResult.NH4WaterTubeSmutState}",
                        $"氨氮质控标样管状态：{imagePatrolResult.NH4SampleTubeSmutState}",
                        $"氨氮质控水样杯脏污：{imagePatrolResult.NH4WaterCupSmutState}",
                        $"氨氮质控标样杯脏污：{imagePatrolResult.NH4SampleCupSmutState}",
                        $"高指氨氮废水桶状态：{imagePatrolResult.CodMnNH4WasteWaterBucketState}",
                        $"高指氨氮废液桶状态：{imagePatrolResult.CodMnNH4WasteTankBucketState}",
                        $"高指氨氮纯水桶状态：{imagePatrolResult.CodMnNH4WaterBucketState}"
                    });

                uc_TPTNQCD.SetPicture(imagePatrolResult.TPTNQCDCameraImagePath);
                uc_TPTNQCD.SetInfo(
                    new List<string>
                    {
                        $"总磷质控水样管状态：{imagePatrolResult.TPWaterTubeSmutState}",
                        $"总磷质控标样管状态：{imagePatrolResult.TPSampleTubeSmutState}",
                        $"总磷质控水样杯脏污：{imagePatrolResult.TPWaterCupSmutState}",
                        $"总磷质控标样杯脏污：{imagePatrolResult.TPSampleCupSmutState}",
                        $"总氮质控水样管状态：{imagePatrolResult.TNWaterTubeSmutState}",
                        $"总氮质控标样管状态：{imagePatrolResult.TNSampleTubeSmutState}",
                        $"总氮质控水样杯脏污：{imagePatrolResult.TNWaterCupSmutState}",
                        $"总氮质控标样杯脏污：{imagePatrolResult.TNSampleCupSmutState}",
                        $"总磷总氮废水桶状态：{imagePatrolResult.TPTNWasteWaterBucketState}",
                        $"总磷总氮废液桶状态：{imagePatrolResult.TPTNWasteTankBucketState}",
                        $"总磷总氮纯水桶状态：{imagePatrolResult.TPTNWaterBucketState}"
                    });
            }
        }

        #endregion
    }
}