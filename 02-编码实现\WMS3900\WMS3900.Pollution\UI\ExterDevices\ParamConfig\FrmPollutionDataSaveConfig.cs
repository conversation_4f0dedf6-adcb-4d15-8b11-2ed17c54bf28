﻿using System;
using System.Collections.Generic;
using System.Text;
using Fpi.Data.Config;
using Fpi.HB.Business.HisData;
using Fpi.UI.Common.PC;
using Fpi.Util.EnumRelated;
using Fpi.WMS3000.DB;
using Fpi.WMS3000.Equipment;
using Fpi.WMS3000.Equipment.Config;
using Sunny.UI;

namespace Fpi.WMS3000.Pollution.UI.ExterDevices
{
    /// <summary>
    /// 污染源数据存储配置
    /// </summary>
    public partial class FrmPollutionDataSaveConfig : UIForm
    {
        #region 构造

        public FrmPollutionDataSaveConfig()
        {
            InitializeComponent();
        }

        #endregion

        #region 事件

        private void FrmPollutionDataSaveConfig_Load(object sender, EventArgs e)
        {
            InitControl();

            LoadConfig();
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                Check();

                Save();

                FpiMessageBox.ShowInfo($"保存污染源数据存储配置成功！");

                // 记录系统操作日志
                SystemOpLogHelper.SaveLog(new SystemOpLogInfo($"修改污染源数据存储配置", eOpType.配置操作, eOpStyle.本地操作));

                this.Close();
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError(ex.Message);
            }
        }


        private void btnAdd_Click(object sender, EventArgs e)
        {
            if(lsbAll.SelectedIndices.Count > 0)
            {
                var list = new List<ValueNode>();
                foreach(ValueNode node in lsbAll.SelectedItems)
                {
                    list.Add(node);
                }

                foreach(ValueNode var in list)
                {
                    if(!lsbSub.Items.Contains(var))
                    {
                        lsbSub.Items.Add(var);
                        lsbAll.Items.Remove(var);
                    }
                }

                lsbAll.ClearSelected();
            }
        }

        private void btnSub_Click(object sender, EventArgs e)
        {
            if(lsbSub.SelectedIndices.Count > 0)
            {
                var list = new List<ValueNode>();
                foreach(ValueNode node in lsbSub.SelectedItems)
                {
                    list.Add(node);
                }

                foreach(ValueNode var in list)
                {
                    lsbSub.Items.Remove(var);
                    lsbAll.Items.Add(var);
                }

                lsbSub.ClearSelected();
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 初始化控件
        /// </summary>
        private void InitControl()
        {
            var valueNodeList = DataManager.GetInstance().GetAllValueNodes().ToArray();
            cmbTotalFlowNode.Items.AddRange(valueNodeList);
            cmbRealTimeFlowNode.Items.AddRange(valueNodeList);

            EnumOperate.BandEnumToCmb(cmbDayDataCalculateMethod, typeof(eDataCalculateMethod));
            EnumOperate.BandEnumToCmb(cmbMinuteDataCalculateMethod, typeof(eDataCalculateMethod));

            // 添加全部因子到表格中
            lsbAll.Items.AddRange(ReportManager.GetInstance().GetFirstQueryGroup().GetQueryGroupValueNode().ToArray());
        }

        /// <summary>
        /// 加载当前配置
        /// </summary>
        private void LoadConfig()
        {
            cmbDayDataCalculateMethod.SelectedValue = (int)ExterEquipConfigManager.GetInstance().PollutionDataSaveConfigInfo.DayDataCalculateMethod;
            cmbMinuteDataCalculateMethod.SelectedValue = (int)ExterEquipConfigManager.GetInstance().PollutionDataSaveConfigInfo.MinuteDataCalculateMethod;

            cmbTotalFlowNode.SelectedItem = ExterEquipConfigManager.GetInstance().PollutionDataSaveConfigInfo.TotalFlowNode;
            cmbRealTimeFlowNode.SelectedItem = ExterEquipConfigManager.GetInstance().PollutionDataSaveConfigInfo.RealTimeFlowNode;

            string weightNodeList = ExterEquipConfigManager.GetInstance().PollutionDataSaveConfigInfo.WeightNodeList;
            if(!string.IsNullOrEmpty(weightNodeList))
            {
                foreach(ValueNode queryNode in ReportManager.GetInstance().GetFirstQueryGroup().GetQueryGroupValueNode())
                {
                    if(weightNodeList.Contains(queryNode.id))
                    {
                        lsbSub.Items.Add(queryNode);
                        lsbAll.Items.Remove(queryNode);
                    }
                }
            }
        }

        private void Check()
        {
            if(cmbDayDataCalculateMethod.SelectedIndex == -1)
            {
                throw new Exception("请选择日数据计算方法！");
            }
            if(cmbMinuteDataCalculateMethod.SelectedIndex == -1)
            {
                throw new Exception("请选择分钟小时数据计算方法！");
            }
            if(cmbTotalFlowNode.SelectedIndex == -1)
            {
                throw new Exception("请选择累积流量因子！");
            }
            if(cmbRealTimeFlowNode.SelectedIndex == -1)
            {
                throw new Exception("请选择瞬时流量因子！");
            }
        }

        private void Save()
        {
            ExterEquipConfigManager.GetInstance().PollutionDataSaveConfigInfo.DayDataCalculateMethod = (eDataCalculateMethod)cmbDayDataCalculateMethod.SelectedValue;
            ExterEquipConfigManager.GetInstance().PollutionDataSaveConfigInfo.MinuteDataCalculateMethod = (eDataCalculateMethod)cmbMinuteDataCalculateMethod.SelectedValue;

            ExterEquipConfigManager.GetInstance().PollutionDataSaveConfigInfo.TotalFlowNodeId = (cmbTotalFlowNode.SelectedItem as VarNode).id;
            ExterEquipConfigManager.GetInstance().PollutionDataSaveConfigInfo.RealTimeFlowNodeId = (cmbRealTimeFlowNode.SelectedItem as VarNode).id;

            StringBuilder str = new StringBuilder();
            for(int i = 0; i < lsbSub.Items.Count; i++)
            {
                str.Append((lsbSub.Items[i] as VarNode).id);
                if(i != lsbSub.Items.Count - 1)
                {
                    str.Append(";");
                }
            }
            ExterEquipConfigManager.GetInstance().PollutionDataSaveConfigInfo.WeightNodeList = str.ToString();

            ExterEquipConfigManager.GetInstance().Save();
        }

        #endregion
    }
}