﻿using System;
using System.IO;
using System.Windows.Forms;
using Fpi.UI.Common.PC;
using Fpi.UI.PC.Config;
using Sunny.UI;

namespace Fpi.WMS3000.UI.SystemConfig
{
    public partial class FrmLogoConfig : UIForm
    {
        #region 构造

        public FrmLogoConfig()
        {
            InitializeComponent();
        }

        #endregion

        #region 事件

        /// <summary>
        /// 加载
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void FormRestore_Load(object sender, EventArgs e)
        {
            try
            {
                txtTitle.Text = UIManager.GetInstance().uIProperty.Title;
                txtCopyRight.Text = UIManager.GetInstance().uIProperty.Copyright;
                txtURL.Text = UIManager.GetInstance().uIProperty.URL;

                string logoname = UIManager.GetInstance().uIProperty.TitleImageFile;
                if(!string.IsNullOrEmpty(logoname))
                {
                    txtLogo.Text = Path.Combine(UIHelper.ImagePath, logoname);
                }

                string iconname = UIManager.GetInstance().uIProperty.IconFile;
                if(!string.IsNullOrEmpty(iconname))
                {
                    txtAppCoin.Text = Path.Combine(UIHelper.ImagePath, iconname);
                }

                string iniimagename = UIManager.GetInstance().uIProperty.WelcomeImageFile;
                if(!string.IsNullOrEmpty(iniimagename))
                {
                    txtIniImage.Text = Path.Combine(UIHelper.ImagePath, iniimagename);
                }
            }
            catch(Exception)
            {
            }
        }

        private void btnSelectAppCoin_Click(object sender, EventArgs e)
        {
            OpenFileDialog open = new OpenFileDialog();
            open.Filter = "图标文件|*.ico";
            open.CheckFileExists = true;
            open.CheckPathExists = true;
            open.Multiselect = false;
            if(!string.IsNullOrEmpty(txtAppCoin.Text))
            {
                open.InitialDirectory = Path.GetDirectoryName(txtAppCoin.Text);
            }
            else
            {
                open.InitialDirectory = UIHelper.ImagePath;
            }

            if(open.ShowDialog() == DialogResult.OK)
            {
                txtAppCoin.Text = open.FileName;
            }
        }

        private void btnSelectLogo_Click(object sender, EventArgs e)
        {
            OpenFileDialog open = new OpenFileDialog();
            open.Filter = "图片文件|*.jpg;*.jpeg;*.png";
            open.CheckFileExists = true;
            open.CheckPathExists = true;
            open.Multiselect = false;
            if(!string.IsNullOrEmpty(txtLogo.Text))
            {
                open.InitialDirectory = Path.GetDirectoryName(txtLogo.Text);
            }
            else
            {
                open.InitialDirectory = UIHelper.ImagePath;
            }

            if(open.ShowDialog() == DialogResult.OK)
            {
                txtLogo.Text = open.FileName;
            }
        }

        private void btnSelectIniInmage_Click(object sender, EventArgs e)
        {
            OpenFileDialog open = new OpenFileDialog();
            open.Filter = "图片文件|*.jpg;*.jpeg;*.png";
            open.CheckFileExists = true;
            open.CheckPathExists = true;
            open.Multiselect = false;
            if(!string.IsNullOrEmpty(txtIniImage.Text))
            {
                open.InitialDirectory = Path.GetDirectoryName(txtIniImage.Text);
            }
            else
            {
                open.InitialDirectory = UIHelper.ImagePath;
            }

            if(open.ShowDialog() == DialogResult.OK)
            {
                txtIniImage.Text = open.FileName;
            }
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                Check();
                SaveImage();
                SaveXML();

                FpiMessageBox.ShowInfo("保存配置成功！");

                this.Close();
            }
            catch(Exception ex)
            {
                FpiMessageBox.ShowError($"保存失败：{ex.Message}");
            }
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 检查配置项是否合格
        /// </summary>
        /// <returns></returns>
        private void Check()
        {
            if(string.IsNullOrEmpty(txtTitle.Text) || string.IsNullOrEmpty(txtAppCoin.Text) || string.IsNullOrEmpty(txtIniImage.Text) || string.IsNullOrEmpty(txtLogo.Text))
            {
                throw new Exception("配置项不允许为空，请重新检查配置！");
            }
            if(!File.Exists(txtAppCoin.Text))
            {
                throw new Exception("软件图标文件不存在，请重新检查配置！");
            }
            if(!File.Exists(txtLogo.Text))
            {
                throw new Exception("软件Logo文件不存在，请重新检查配置！");
            }
            if(!File.Exists(txtIniImage.Text))
            {
                throw new Exception("初始化界面图片文件不存在，请重新检查配置！");
            }
        }

        /// <summary>
        /// 保存图片，如果在软件Images目录下没有则复制过去
        /// </summary>
        private void SaveImage()
        {
            CopyFileToFolder(txtAppCoin.Text);
            CopyFileToFolder(txtLogo.Text);
            CopyFileToFolder(txtIniImage.Text);
        }

        /// <summary>
        /// 保存uimanager.xml文件
        /// </summary>
        private void SaveXML()
        {
            UIManager.GetInstance().uIProperty.Title = txtTitle.Text;
            UIManager.GetInstance().uIProperty.URL = txtURL.Text;
            UIManager.GetInstance().uIProperty.Copyright = txtCopyRight.Text;
            UIManager.GetInstance().uIProperty.IconFile = Path.GetFileName(txtAppCoin.Text);
            UIManager.GetInstance().uIProperty.AboutImageFile = Path.GetFileName(txtLogo.Text);
            UIManager.GetInstance().uIProperty.TitleImageFile = Path.GetFileName(txtLogo.Text);
            UIManager.GetInstance().uIProperty.WelcomeImageFile = Path.GetFileName(txtIniImage.Text);
            UIManager.GetInstance().Save();
        }

        /// <summary>
        /// 复制指定文件到图片目录中
        /// </summary>
        /// <param name="newFilePath"></param>
        private void CopyFileToFolder(string newFilePath)
        {
            var fileName = Path.GetFileName(newFilePath);
            string targetFileName = Path.Combine(UIHelper.ImagePath, fileName);
            // 不是同一个文件，则复制替换
            if(targetFileName != newFilePath)
            {
                File.Copy(newFilePath, targetFileName, true);
            }
        }

        #endregion
    }
}