﻿using System.Collections.Generic;
using System.ComponentModel;
using Fpi.WMS3000.Equipment;

namespace Fpi.WMS3000.SystemConfig.ImagePatrol.Config
{
    /// <summary>
    /// 五参数水桶巡检结果
    /// </summary>
    public class FiveParamBucketPatrolResult : ImageUnitPatrolResultBase
    {
        #region 字段属性

        /// <summary>
        /// 五参数废液桶状态
        /// </summary>
        [Description("五参数废液桶状态")]
        public eEarlyWarnState FiveParamWasteTankBucketState { get; set; }

        /// <summary>
        /// 五参数纯水桶状态
        /// </summary>
        [Description("五参数纯水桶状态")]
        public eEarlyWarnState FiveParamWaterBucketState { get; set; }

        #endregion

        #region 构造

        public FiveParamBucketPatrolResult()
        {
            UnitId = "FiveParamBucket";
            UnitName = "五参数水桶";
        }

        #endregion

        #region 方法重写

        /// <summary>
        /// 打印巡检结果
        /// </summary>
        /// <returns></returns>
        public override List<string> GetResultStr()
        {
            return new List<string>
            {
                $"五参数废液桶状态：{FiveParamWasteTankBucketState}",
                $"五参数纯水桶状态：{FiveParamWaterBucketState}"
            };
        }

        #endregion
    }
}