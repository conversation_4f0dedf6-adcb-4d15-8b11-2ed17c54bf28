﻿using System;
using Fpi.Util.Extensions;
using Fpi.WMS3000.Algorithm;
using Fpi.WMS3000.Equipment;
using Fpi.WMS3000.Equipment.Config;
using OpenCvSharp;

namespace Fpi.WMS3000.SystemConfig.ImagePatrol.Config
{
    /// <summary>
    /// 五参数水桶液位检测执行类
    /// </summary>
    public class FiveParamBucketPatrol : ImageUnitSmartPatrolBase
    {
        #region 构造

        public FiveParamBucketPatrol()
        {
            UnitId = "FiveParamBucket";
            UnitName = "五参数水桶液位检测";
        }

        #endregion

        #region 方法重写

        public override void ExecutePatrol(OldImagePatrolResult patrolResult)
        {
            if(ExterEquipConfigManager.GetInstance().CameraSelect.FiveParamBucketCamera == null)
            {
                throw new Exception("对应摄像机未配置！");
            }

            ExterEquipConfigManager.GetInstance().CameraSelect.FiveParamBucketCamera.ScreenShot(out string picPath);
            patrolResult.FiveParamBucketCameraImagePath = FileExtension.GetRelativePath(picPath);
            AlgorithmHelper.CheckFiveParamBucketState(new Mat(picPath), out bool wasteWaterBucketWarn, out bool wasteTankBucketWarn, out bool waterBucketWarn);
            //patrolResult.FiveParamWasteWaterBucketState = wasteWaterBucketWarn ? eEarlyWarnState.预警 : eEarlyWarnState.正常;
            patrolResult.FiveParamWasteTankBucketState = wasteTankBucketWarn ? eEarlyWarnState.预警 : eEarlyWarnState.正常;
            patrolResult.FiveParamWaterBucketState = waterBucketWarn ? eEarlyWarnState.预警 : eEarlyWarnState.正常;
        }

        #endregion
    }
}