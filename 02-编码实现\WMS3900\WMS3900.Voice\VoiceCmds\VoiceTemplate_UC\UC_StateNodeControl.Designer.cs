﻿namespace Fpi.WMS3000.Voice.VoicesTemplate
{
    partial class UC_StateNodeControl
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if(disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.uiLabel1 = new Sunny.UI.UILabel();
            this.uiPanel1 = new Sunny.UI.UIPanel();
            this.rdbClose = new Sunny.UI.UIRadioButton();
            this.rdbOpen = new Sunny.UI.UIRadioButton();
            this.uiLabel2 = new Sunny.UI.UILabel();
            this.cmbStateNode = new Sunny.UI.UIComboBox();
            this.uiPanel1.SuspendLayout();
            this.SuspendLayout();
            // 
            // uiLabel1
            // 
            this.uiLabel1.AutoSize = true;
            this.uiLabel1.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel1.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel1.Location = new System.Drawing.Point(35, 87);
            this.uiLabel1.Name = "uiLabel1";
            this.uiLabel1.Size = new System.Drawing.Size(74, 21);
            this.uiLabel1.Style = Sunny.UI.UIStyle.Custom;
            this.uiLabel1.TabIndex = 3;
            this.uiLabel1.Text = "控制类型";
            this.uiLabel1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // uiPanel1
            // 
            this.uiPanel1.Controls.Add(this.rdbClose);
            this.uiPanel1.Controls.Add(this.rdbOpen);
            this.uiPanel1.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiPanel1.Location = new System.Drawing.Point(114, 80);
            this.uiPanel1.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.uiPanel1.MinimumSize = new System.Drawing.Size(1, 1);
            this.uiPanel1.Name = "uiPanel1";
            this.uiPanel1.Size = new System.Drawing.Size(300, 35);
            this.uiPanel1.TabIndex = 1;
            this.uiPanel1.Text = null;
            this.uiPanel1.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // rdbClose
            // 
            this.rdbClose.AutoSize = true;
            this.rdbClose.BackColor = System.Drawing.Color.Transparent;
            this.rdbClose.Checked = true;
            this.rdbClose.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rdbClose.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.rdbClose.Location = new System.Drawing.Point(183, 4);
            this.rdbClose.MinimumSize = new System.Drawing.Size(1, 1);
            this.rdbClose.Name = "rdbClose";
            this.rdbClose.Size = new System.Drawing.Size(65, 26);
            this.rdbClose.TabIndex = 1;
            this.rdbClose.Text = "关闭";
            // 
            // rdbOpen
            // 
            this.rdbOpen.AutoSize = true;
            this.rdbOpen.BackColor = System.Drawing.Color.Transparent;
            this.rdbOpen.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rdbOpen.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.rdbOpen.Location = new System.Drawing.Point(53, 4);
            this.rdbOpen.MinimumSize = new System.Drawing.Size(1, 1);
            this.rdbOpen.Name = "rdbOpen";
            this.rdbOpen.Size = new System.Drawing.Size(65, 26);
            this.rdbOpen.TabIndex = 0;
            this.rdbOpen.Text = "打开";
            // 
            // uiLabel2
            // 
            this.uiLabel2.AutoSize = true;
            this.uiLabel2.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel2.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel2.Location = new System.Drawing.Point(35, 30);
            this.uiLabel2.Name = "uiLabel2";
            this.uiLabel2.Size = new System.Drawing.Size(74, 21);
            this.uiLabel2.Style = Sunny.UI.UIStyle.Custom;
            this.uiLabel2.TabIndex = 2;
            this.uiLabel2.Text = "控制因子";
            this.uiLabel2.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // cmbStateNode
            // 
            this.cmbStateNode.DataSource = null;
            this.cmbStateNode.DropDownAutoWidth = true;
            this.cmbStateNode.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            this.cmbStateNode.FillColor = System.Drawing.Color.White;
            this.cmbStateNode.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.cmbStateNode.ItemHoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(155)))), ((int)(((byte)(200)))), ((int)(((byte)(255)))));
            this.cmbStateNode.ItemSelectForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(243)))), ((int)(((byte)(255)))));
            this.cmbStateNode.Location = new System.Drawing.Point(114, 25);
            this.cmbStateNode.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.cmbStateNode.MinimumSize = new System.Drawing.Size(63, 0);
            this.cmbStateNode.Name = "cmbStateNode";
            this.cmbStateNode.Padding = new System.Windows.Forms.Padding(0, 0, 30, 2);
            this.cmbStateNode.Size = new System.Drawing.Size(300, 29);
            this.cmbStateNode.SymbolSize = 24;
            this.cmbStateNode.TabIndex = 0;
            this.cmbStateNode.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.cmbStateNode.Watermark = "";
            // 
            // UC_StateNodeControl
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.Controls.Add(this.cmbStateNode);
            this.Controls.Add(this.uiLabel2);
            this.Controls.Add(this.uiPanel1);
            this.Controls.Add(this.uiLabel1);
            this.Name = "UC_StateNodeControl";
            this.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.Size = new System.Drawing.Size(448, 141);
            this.uiPanel1.ResumeLayout(false);
            this.uiPanel1.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private Sunny.UI.UILabel uiLabel1;
        private Sunny.UI.UIPanel uiPanel1;
        private Sunny.UI.UIRadioButton rdbClose;
        private Sunny.UI.UIRadioButton rdbOpen;
        private Sunny.UI.UILabel uiLabel2;
        private Sunny.UI.UIComboBox cmbStateNode;
    }
}
