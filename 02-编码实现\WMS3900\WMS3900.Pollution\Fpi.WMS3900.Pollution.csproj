<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{F7391C4B-70B6-4830-84F6-9D45EC022320}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Fpi.WMS3000.Pollution</RootNamespace>
    <AssemblyName>Fpi.WMS3900.Pollution</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\..\Product\Debug\bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>..\..\Product\Release\bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="SunnyUI">
      <HintPath>E:\01-数采软件\01-WMS系列\01-WMS3900\02-编码实现\WMS3900\FpiDLL\SunnyUI.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
    <Reference Include="WinFormsUI, Version=2.3.3505.27065, Culture=neutral, PublicKeyToken=null" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="DB\DbConfig.cs" />
    <Compile Include="DB\DbCreator.cs" />
    <Compile Include="DB\SaveDataHelper.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Remote\FPISZHWRYProtocol\ConfigUI\FPISZHWRYConfigUC.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Remote\FPISZHWRYProtocol\ConfigUI\FPISZHWRYConfigUC.Designer.cs">
      <DependentUpon>FPISZHWRYConfigUC.cs</DependentUpon>
    </Compile>
    <Compile Include="Remote\FPISZHWRYProtocol\FPISZHWRYHelper.cs" />
    <Compile Include="Remote\FPISZHWRYProtocol\FPISZHWRYProtocol.cs" />
    <Compile Include="Remote\FPISZHWRYProtocol\FPISZHWRYProtocolDesc.cs" />
    <Compile Include="Remote\FPISZHWRYProtocol\FPISZHWRYReceiver.cs" />
    <Compile Include="Remote\FPISZHWRYProtocol\FPISZHWRYSender.cs" />
    <Compile Include="SystemConfig\CustomTask\PollutionDataStorageTask.cs" />
    <Compile Include="SystemOperation\OperationTemplate\Assist_Operation\Op_GetPreviousHourTotalFlow.cs" />
    <Compile Include="SystemOperation\OperationTemplate\Assist_Operation\Op_GetSampleTime.cs" />
    <Compile Include="SystemOperation\OperationTemplate\Compose_Operation\Op_WaterCollection.cs" />
    <Compile Include="SystemOperation\OperationTemplate\Sampler_Operation\Op_SampleDrainBucket.cs" />
    <Compile Include="SystemOperation\OperationTemplate\Sampler_Operation\Op_SampleRetain.cs" />
    <Compile Include="SystemOperation\OperationTemplate\Sampler_Operation\Op_SampleSupply.cs" />
    <Compile Include="SystemOperation\OperationTemplate\Sampler_Operation\Op_StartOneMixedSampleDeviceOper.cs" />
    <Compile Include="SystemOperation\OpTemplate_UC\UC_MixedSampleOperSelect.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="SystemOperation\OpTemplate_UC\UC_MixedSampleOperSelect.designer.cs">
      <DependentUpon>UC_MixedSampleOperSelect.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\DataQuery\Form\FrmQueryCheckData.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\DataQuery\Form\FrmQueryCheckData.Designer.cs">
      <DependentUpon>FrmQueryCheckData.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\DataQuery\Form\FrmQueryPollutionData.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\DataQuery\Form\FrmQueryPollutionData.Designer.cs">
      <DependentUpon>FrmQueryPollutionData.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\DataQuery\UC\UC_QueryDayPollutionData.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\DataQuery\UC\UC_QueryDayPollutionData.Designer.cs">
      <DependentUpon>UC_QueryDayPollutionData.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\DataQuery\UC\UC_QueryHourPollutionData.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\DataQuery\UC\UC_QueryHourPollutionData.Designer.cs">
      <DependentUpon>UC_QueryHourPollutionData.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\DataQuery\UC\UC_QueryTenMinutePollutionData.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\DataQuery\UC\UC_QueryTenMinutePollutionData.Designer.cs">
      <DependentUpon>UC_QueryTenMinutePollutionData.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\DataQuery\UC\UC_QueryMinutePollutionData.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\DataQuery\UC\UC_QueryMinutePollutionData.Designer.cs">
      <DependentUpon>UC_QueryMinutePollutionData.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\DataQuery\UC\UC_QuerySecondPollutionData.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\DataQuery\UC\UC_QuerySecondPollutionData.Designer.cs">
      <DependentUpon>UC_QuerySecondPollutionData.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\ExterDevices\ParamConfig\FrmPollutionDataSaveConfig.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\ExterDevices\ParamConfig\FrmPollutionDataSaveConfig.Designer.cs">
      <DependentUpon>FrmPollutionDataSaveConfig.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\ExterDevices\ParamConfig\FrmPollutionCollectionConfig.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\ExterDevices\ParamConfig\FrmPollutionCollectionConfig.Designer.cs">
      <DependentUpon>FrmPollutionCollectionConfig.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\ExterDevices\ParamShow\FrmExterDeviceParam.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="UI\ExterDevices\ParamShow\FrmExterDeviceParam.Designer.cs">
      <DependentUpon>FrmExterDeviceParam.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\ExterDevices\ParamShow\UC\UC_CollectionModuleState.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="UI\ExterDevices\ParamShow\UC\UC_CollectionModuleState.Designer.cs">
      <DependentUpon>UC_CollectionModuleState.cs</DependentUpon>
    </Compile>
    <Compile Include="UI\FuncServices\FuncService.cs" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <EmbeddedResource Include="Remote\FPISZHWRYProtocol\ConfigUI\FPISZHWRYConfigUC.resx">
      <DependentUpon>FPISZHWRYConfigUC.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SystemOperation\OpTemplate_UC\UC_MixedSampleOperSelect.resx">
      <DependentUpon>UC_MixedSampleOperSelect.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\DataQuery\Form\FrmQueryCheckData.resx">
      <DependentUpon>FrmQueryCheckData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\DataQuery\Form\FrmQueryPollutionData.resx">
      <DependentUpon>FrmQueryPollutionData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\DataQuery\UC\UC_QueryDayPollutionData.resx">
      <DependentUpon>UC_QueryDayPollutionData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\DataQuery\UC\UC_QueryHourPollutionData.resx">
      <DependentUpon>UC_QueryHourPollutionData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\DataQuery\UC\UC_QueryTenMinutePollutionData.resx">
      <DependentUpon>UC_QueryTenMinutePollutionData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\DataQuery\UC\UC_QueryMinutePollutionData.resx">
      <DependentUpon>UC_QueryMinutePollutionData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\DataQuery\UC\UC_QuerySecondPollutionData.resx">
      <DependentUpon>UC_QuerySecondPollutionData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\ExterDevices\ParamConfig\FrmPollutionDataSaveConfig.resx">
      <DependentUpon>FrmPollutionDataSaveConfig.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\ExterDevices\ParamConfig\FrmPollutionCollectionConfig.resx">
      <DependentUpon>FrmPollutionCollectionConfig.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\ExterDevices\ParamShow\FrmExterDeviceParam.resx">
      <DependentUpon>FrmExterDeviceParam.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="UI\ExterDevices\ParamShow\UC\UC_CollectionModuleState.resx">
      <DependentUpon>UC_CollectionModuleState.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Fpi.Communication\Fpi.Communication.csproj">
      <Project>{D95F58B1-2E07-4D52-BA26-3F9B6EEACF29}</Project>
      <Name>Fpi.Communication</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Data\Fpi.Data.csproj">
      <Project>{07b7e9d5-5d00-4815-9409-0d7466a09f96}</Project>
      <Name>Fpi.Data</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.DB\Fpi.DB.csproj">
      <Project>{89d85957-ba9e-4bd9-99fe-7b73b6176a6f}</Project>
      <Name>Fpi.DB</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Device\Fpi.Device.csproj">
      <Project>{88fef5d2-e039-4ac0-942b-442f23755978}</Project>
      <Name>Fpi.Device</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.HB.Business\Fpi.HB.Business.csproj">
      <Project>{13650425-1448-4df5-884f-b7cd466ecb24}</Project>
      <Name>Fpi.HB.Business</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Json\Fpi.Json.csproj">
      <Project>{958C97C1-360F-4434-9C37-6C6030EB5FCD}</Project>
      <Name>Fpi.Json</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Operation\Fpi.Operation.csproj">
      <Project>{1657672D-6FBA-47A5-8C40-0DA507D578F2}</Project>
      <Name>Fpi.Operation</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Timer\Fpi.Timer.csproj">
      <Project>{1DC3DD73-A4F5-4CA4-96D3-43712267C864}</Project>
      <Name>Fpi.Timer</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.UI.Common\Fpi.UI.Common.csproj">
      <Project>{c238e665-75b4-4eda-b574-a37f2794ba54}</Project>
      <Name>Fpi.UI.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.UI.PC\Fpi.UI.PC.csproj">
      <Project>{2D502016-B3B3-43FF-9BAE-AD1D2A18D42E}</Project>
      <Name>Fpi.UI.PC</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Util\Fpi.Util.csproj">
      <Project>{6e37d7b3-8d08-4ef3-a924-3b87982ab246}</Project>
      <Name>Fpi.Util</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Xml\Fpi.Xml.csproj">
      <Project>{3AF9654D-39EE-4BE9-8553-A9BB9B83A33B}</Project>
      <Name>Fpi.Xml</Name>
    </ProjectReference>
    <ProjectReference Include="..\WMS3900.DB\Fpi.WMS3900.DB.csproj">
      <Project>{4e6961d6-ba42-4cb1-89a7-c25557a2f82a}</Project>
      <Name>Fpi.WMS3900.DB</Name>
    </ProjectReference>
    <ProjectReference Include="..\WMS3900.Equipment\Fpi.WMS3900.Equipment.csproj">
      <Project>{63a37282-fea3-4f07-98f1-164045b58d8b}</Project>
      <Name>Fpi.WMS3900.Equipment</Name>
    </ProjectReference>
    <ProjectReference Include="..\WMS3900.Inspection\Fpi.WMS3900.Inspection.csproj">
      <Project>{a5e42fa2-c43e-421c-a0de-2f06f6efe255}</Project>
      <Name>Fpi.WMS3900.Inspection</Name>
    </ProjectReference>
    <ProjectReference Include="..\WMS3900.RemoteProtocol\Fpi.WMS3900.Remote.csproj">
      <Project>{0e2971b7-6727-4fbb-9de0-3ce857591ded}</Project>
      <Name>Fpi.WMS3900.Remote</Name>
    </ProjectReference>
    <ProjectReference Include="..\WMS3900.SystemConfig\Fpi.WMS3900.SystemConfig.csproj">
      <Project>{74b5bd59-a90b-4f35-8f41-74a54a290940}</Project>
      <Name>Fpi.WMS3900.SystemConfig</Name>
    </ProjectReference>
    <ProjectReference Include="..\WMS3900.SystemOperation\Fpi.WMS3900.SystemOperation.csproj">
      <Project>{e9dee184-5552-4e3a-90df-ea517ec7a99a}</Project>
      <Name>Fpi.WMS3900.SystemOperation</Name>
    </ProjectReference>
    <ProjectReference Include="..\WMS3900.UI\Fpi.WMS3900.UI.csproj">
      <Project>{10260bea-42e4-4a5b-88a2-f48380cc9a31}</Project>
      <Name>Fpi.WMS3900.UI</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>