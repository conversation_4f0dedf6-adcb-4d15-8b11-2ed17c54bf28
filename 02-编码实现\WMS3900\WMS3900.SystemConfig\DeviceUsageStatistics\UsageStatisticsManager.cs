﻿using System.Collections.Generic;
using System.Linq;
using Fpi.Camera;
using Fpi.Data.Config;
using Fpi.Devices;
using Fpi.Json;
using Fpi.Util.Interfaces.Initialize;

namespace Fpi.WMS3000.SystemConfig
{
    /// <summary>
    /// 器件使用情况统计
    /// </summary>
    public class UsageStatisticsManager : BaseJsonNode, IInitialization
    {
        #region 字段属性

        /// <summary>
        /// 器件使用情况列表
        /// </summary>
        public List<DeviceUsageInfo> DeviceUsageInfoList { get; private set; } = new List<DeviceUsageInfo>();

        /// <summary>
        /// 设备使用情况列表
        /// </summary>
        public List<EquipUsageInfo> EquipUsageInfoList { get; private set; } = new List<EquipUsageInfo>();

        /// <summary>
        /// 摄像机使用情况列表
        /// </summary>
        public List<EquipUsageInfo> CameraUsageInfoList { get; private set; } = new List<EquipUsageInfo>();

        #endregion

        #region 单例

        private static readonly object SyncObj = new object();
        private static UsageStatisticsManager _instance = null;
        public static UsageStatisticsManager GetInstance()
        {
            lock(SyncObj)
            {
                if(_instance == null)
                {
                    _instance = new UsageStatisticsManager();
                }
            }
            return _instance;
        }

        private UsageStatisticsManager()
        {
            // 加载之前的配置
            loadJson();

            //Save();
        }

        #endregion

        #region 公共方法

        #region 泵阀控制器

        /// <summary>
        /// 通过ID查找器件使用情况
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public DeviceUsageInfo GetDeviceUsageInfoById(string id)
        {
            return DeviceUsageInfoList.FirstOrDefault(x => x.Id == id);
        }

        /// <summary>
        /// 通过类型获取器件列表
        /// </summary>
        /// <param name="nodeType"></param>
        /// <returns></returns>
        public List<DeviceUsageInfo> GetDeviceUsageInfoById(eStateNodeType nodeType)
        {
            return DeviceUsageInfoList.Where(x => x.DeviceType == nodeType).ToList();
        }

        /// <summary>
        /// 获取启用的器件列表
        /// </summary>
        /// <param name="nodeType"></param>
        /// <returns></returns>
        public List<DeviceUsageInfo> GetUsedDeviceUsageInfoList()
        {
            // 筛选启用的器件，先按类型排序，再按编号排序
            return DeviceUsageInfoList.Where(x => x.IsUsed).OrderBy(x => x.DeviceType).ThenBy(x => x.Id).ToList();
        }

        #endregion

        #region 设备

        /// <summary>
        /// 通过ID查找设备使用情况
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public EquipUsageInfo GetEquipUsageInfoById(string id)
        {
            return EquipUsageInfoList.FirstOrDefault(x => x.Id == id);
        }

        /// <summary>
        /// 获取启用的设备列表
        /// </summary>
        /// <param name="nodeType"></param>
        /// <returns></returns>
        public List<EquipUsageInfo> GetUsedEquipUsageInfoList()
        {
            // 筛选启用的设备，按编号排序
            return EquipUsageInfoList.Where(x => x.IsUsed).OrderBy(x => x.Id).ToList();
        }

        #endregion

        #region 设备

        /// <summary>
        /// 通过ID查找设备使用情况
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public EquipUsageInfo GetCameraUsageInfoById(string id)
        {
            return CameraUsageInfoList.FirstOrDefault(x => x.Id == id);
        }

        /// <summary>
        /// 获取启用的设备列表
        /// </summary>
        /// <param name="nodeType"></param>
        /// <returns></returns>
        public List<EquipUsageInfo> GetUsedCameraUsageInfoList()
        {
            // 筛选启用的设备，按编号排序
            return CameraUsageInfoList.Where(x => x.IsUsed).OrderBy(x => x.Id).ToList();
        }

        #endregion

        /// <summary>
        /// 初始化
        /// </summary>
        public void Initialize()
        {
            bool hasAdd = false;

            // 遍历开关量，类型为泵、阀、控制器
            foreach(var stateNode in DataManager.GetInstance().GetAllStateNodes().Where(sn => sn.NodeType is eStateNodeType.Valve or eStateNodeType.Pump or eStateNodeType.Controller))
            {
                // 获取对应统计信息缓存
                var info = GetDeviceUsageInfoById(stateNode.id);

                // 因子类型或编码发生变更
                if(info != null && info.DeviceType != stateNode.NodeType)
                {
                    DeviceUsageInfoList.Remove(info);
                    info = null;
                }

                // 未添加过统计信息，或者统计信息不合格被删除，新建统计信息因子
                if(info == null)
                {
                    info = new DeviceUsageInfo(stateNode);
                    DeviceUsageInfoList.Add(info);
                    hasAdd = true;
                }

                // 更新因子名称
                info.Name = stateNode.name;

                // 启用统计因子
                info.IsUsed = true;

                //若因子取消勾选，则此处不启用对应统计因子，数据查询界面不显示因子对应器件统计信息
            }

            // 遍历启用的设备列表
            foreach(var device in DeviceManager.GetInstance().GetDeviceListUsed())
            {
                // 获取对应统计信息缓存
                var info = GetEquipUsageInfoById(device.id);

                // 未添加过统计信息，或者统计信息不合格被删除，新建统计信息因子
                if(info == null)
                {
                    info = new EquipUsageInfo(device);
                    EquipUsageInfoList.Add(info);
                    hasAdd = true;
                }

                // 更新设备名称
                info.Name = device.name;

                // 启用统计因子
                info.IsUsed = true;

                // 若设备取消勾选，则此处不启用对应统计因子，数据查询界面不显示设备统计信息
            }

            // 遍历摄像机列表
            foreach(var camera in NETCameraManager.GetInstance().GetAllCameras())
            {
                // 获取对应统计信息缓存
                var info = GetCameraUsageInfoById(camera.id);

                // 未添加过统计信息，或者统计信息不合格被删除，新建统计信息因子
                if(info == null)
                {
                    info = new EquipUsageInfo(camera);
                    CameraUsageInfoList.Add(info);
                    hasAdd = true;
                }

                // 更新摄像机名称
                info.Name = camera.name;

                // 启用统计因子
                info.IsUsed = true;

                // 若摄像机取消勾选，则此处不启用对应统计因子，数据查询界面不显示设备统计信息
            }

            if(hasAdd)
            {
                Save();
            }
        }

        #endregion
    }
}