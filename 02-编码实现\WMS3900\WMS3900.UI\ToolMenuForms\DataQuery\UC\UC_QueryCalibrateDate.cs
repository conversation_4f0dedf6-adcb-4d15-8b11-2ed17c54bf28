﻿using System.Data;
using System.Threading;
using System.Windows.Forms;
using Fpi.Json;
using Fpi.Util.StatusForm;
using Fpi.WMS3000.DB;
using Fpi.WMS3000.Equipment.SIA3900;
using Fpi.WMS3000.Equipment.UI;

namespace Fpi.WMS3000.UI.DataQuery
{
    public partial class UC_QueryCalibrateDate : UC_QueryDataBase
    {
        #region 构造

        public UC_QueryCalibrateDate()
        {
            InitializeComponent();

            TitleName = "标定系数数据";
            DBTableName = DbConfig.DEVICE_CALIBRATECOEFFICIENT_TABLE;
            SelectSqlStr = BuildSelectString();
            IsShowStateLabel = false;
        }

        #endregion

        #region 公共方法（重写）

        /// <summary>
        /// 设置表头，子类重写
        /// </summary>
        protected override void SetDataGridViewHead()
        {
            dgvData.ClearColumns();
            dgvData.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;

            var col = dgvData.AddColumn("序号", "num");
            col.FillWeight = 50;
            col = dgvData.AddColumn("测量时间", "datatime");
            col.FillWeight = 120;
            col = dgvData.AddColumn("标定类型", "calibratetype");
            col.FillWeight = 100;
            col = dgvData.AddButtonColumn("详细信息", "calibrateinfo");
            col.FillWeight = 100;

            // 事件绑定
            dgvData.CellClick += (object sender, DataGridViewCellEventArgs e) =>
            {
                if(e.ColumnIndex == 3 && e.RowIndex >= 0)
                {
                    SIA3900CalibrateCoefficient calibrateInfo = FpiJsonHelper.JsonToModel<SIA3900CalibrateCoefficient>(dgvData.CurrentRow?.Tag as string);
                    if(calibrateInfo != null)
                    {
                        string type = dgvData.Rows[e.RowIndex].Cells[2].Value as string;
                        new FrmSIA3900CalibrateCoefficientData(calibrateInfo, type).ShowDialog();
                    }
                }
            };
        }

        /// <summary>
        /// 表格中填充数据，子类重写
        /// </summary>
        protected override void FillDataGridViewData(IDataReader reader, int curPage, int onePageCount)
        {
            try
            {
                // 打开进度条界面
                FpiStatusFormService.ShowStatusForm(onePageCount, "数据渲染中，请稍候...");

                // 进度条序号值
                int currentStep = 1;
                // 进度条满值
                int currentPageCount = onePageCount;
                // 若当前是最后一页，则数据不足onePageCount
                if(curPage * onePageCount > pagination.TotalCount)
                {
                    currentPageCount = pagination.TotalCount - (curPage - 1) * onePageCount;
                }

                // 表格中数据序号值
                int rowIndex = (curPage - 1) * onePageCount + 1;

                while(reader.Read())
                {
                    int index = dgvData.Rows.Add();
                    DataGridViewRow dr = dgvData.Rows[index];
                    dr.Cells[0].Value = rowIndex++;
                    dr.Cells[1].Value = reader.GetDateTime(1).ToString(DbConfig.DATETIME_FORMAT);
                    string type = reader.GetString(3);
                    dr.Cells[2].Value = $"{type}";
                    dr.Cells[3].Value = "详细信息";
                    dr.Tag = reader.GetString(4);
                }

                FpiStatusFormService.SetDescription($"数据渲染中[{currentStep++}/{currentPageCount}]......");
                FpiStatusFormService.StepIt();
            }
            finally
            {
                // 线程切换，防止最终进度界面无法关闭
                Thread.Sleep(100);
                // 隐藏进度条界面
                FpiStatusFormService.HideStatusForm();
            }
        }

        #endregion

        #region 私有方法

        private string BuildSelectString()
        {
            return "select id,datatime,sourceid,calibratetype,calibrateinfo";
        }

        #endregion
    }
}
