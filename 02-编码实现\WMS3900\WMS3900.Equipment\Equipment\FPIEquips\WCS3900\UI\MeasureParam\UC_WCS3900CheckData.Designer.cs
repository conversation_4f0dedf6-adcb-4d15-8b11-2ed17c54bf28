﻿namespace Fpi.WMS3000.Equipment.UI
{
    partial class UC_WCS3900CheckData
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if(disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle1 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle2 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle3 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle4 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle5 = new System.Windows.Forms.DataGridViewCellStyle();
            this.gbCheckData = new Sunny.UI.UIGroupBox();
            this.pnlCheckData = new System.Windows.Forms.FlowLayoutPanel();
            this.gbDetailData = new Sunny.UI.UIGroupBox();
            this.btnExport = new Sunny.UI.UIButton();
            this.dgvDetailData = new Sunny.UI.UIDataGridView();
            this.uiPanel1 = new Sunny.UI.UIPanel();
            this.saveFileDialog = new System.Windows.Forms.SaveFileDialog();
            this.gbCheckData.SuspendLayout();
            this.gbDetailData.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvDetailData)).BeginInit();
            this.uiPanel1.SuspendLayout();
            this.SuspendLayout();
            // 
            // gbCheckData
            // 
            this.gbCheckData.Controls.Add(this.pnlCheckData);
            this.gbCheckData.Dock = System.Windows.Forms.DockStyle.Left;
            this.gbCheckData.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.gbCheckData.Location = new System.Drawing.Point(1, 1);
            this.gbCheckData.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.gbCheckData.MinimumSize = new System.Drawing.Size(1, 1);
            this.gbCheckData.Name = "gbCheckData";
            this.gbCheckData.Padding = new System.Windows.Forms.Padding(1, 32, 1, 1);
            this.gbCheckData.Size = new System.Drawing.Size(380, 852);
            this.gbCheckData.TabIndex = 4;
            this.gbCheckData.Text = "核查数据";
            this.gbCheckData.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // pnlCheckData
            // 
            this.pnlCheckData.AutoScroll = true;
            this.pnlCheckData.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(243)))), ((int)(((byte)(249)))), ((int)(((byte)(255)))));
            this.pnlCheckData.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pnlCheckData.Location = new System.Drawing.Point(1, 32);
            this.pnlCheckData.Name = "pnlCheckData";
            this.pnlCheckData.Padding = new System.Windows.Forms.Padding(5, 0, 5, 0);
            this.pnlCheckData.Size = new System.Drawing.Size(378, 819);
            this.pnlCheckData.TabIndex = 2;
            // 
            // gbDetailData
            // 
            this.gbDetailData.Controls.Add(this.btnExport);
            this.gbDetailData.Controls.Add(this.dgvDetailData);
            this.gbDetailData.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gbDetailData.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.gbDetailData.Location = new System.Drawing.Point(1, 0);
            this.gbDetailData.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.gbDetailData.MinimumSize = new System.Drawing.Size(1, 1);
            this.gbDetailData.Name = "gbDetailData";
            this.gbDetailData.Padding = new System.Windows.Forms.Padding(1, 32, 1, 1);
            this.gbDetailData.Size = new System.Drawing.Size(631, 852);
            this.gbDetailData.TabIndex = 5;
            this.gbDetailData.Text = "过程溯源数据";
            this.gbDetailData.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // btnExport
            // 
            this.btnExport.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnExport.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnExport.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnExport.Location = new System.Drawing.Point(507, 0);
            this.btnExport.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnExport.Name = "btnExport";
            this.btnExport.Size = new System.Drawing.Size(100, 35);
            this.btnExport.TabIndex = 2;
            this.btnExport.Text = "导出";
            this.btnExport.TipsFont = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnExport.Click += new System.EventHandler(this.btnExport_Click);
            // 
            // dgvDetailData
            // 
            this.dgvDetailData.AllowUserToAddRows = false;
            this.dgvDetailData.AllowUserToDeleteRows = false;
            this.dgvDetailData.AllowUserToOrderColumns = true;
            this.dgvDetailData.AllowUserToResizeRows = false;
            dataGridViewCellStyle1.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(243)))), ((int)(((byte)(249)))), ((int)(((byte)(255)))));
            this.dgvDetailData.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle1;
            this.dgvDetailData.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dgvDetailData.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(243)))), ((int)(((byte)(249)))), ((int)(((byte)(255)))));
            this.dgvDetailData.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.dgvDetailData.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText;
            this.dgvDetailData.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.Single;
            dataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(80)))), ((int)(((byte)(160)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle2.Font = new System.Drawing.Font("微软雅黑", 12F);
            dataGridViewCellStyle2.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle2.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(80)))), ((int)(((byte)(160)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgvDetailData.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle2;
            this.dgvDetailData.ColumnHeadersHeight = 32;
            this.dgvDetailData.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            dataGridViewCellStyle3.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle3.BackColor = System.Drawing.Color.White;
            dataGridViewCellStyle3.Font = new System.Drawing.Font("微软雅黑", 12F);
            dataGridViewCellStyle3.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            dataGridViewCellStyle3.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(236)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle3.SelectionForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            dataGridViewCellStyle3.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgvDetailData.DefaultCellStyle = dataGridViewCellStyle3;
            this.dgvDetailData.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dgvDetailData.EnableHeadersVisualStyles = false;
            this.dgvDetailData.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.dgvDetailData.GridColor = System.Drawing.Color.FromArgb(((int)(((byte)(80)))), ((int)(((byte)(160)))), ((int)(((byte)(255)))));
            this.dgvDetailData.Location = new System.Drawing.Point(1, 32);
            this.dgvDetailData.MultiSelect = false;
            this.dgvDetailData.Name = "dgvDetailData";
            this.dgvDetailData.ReadOnly = true;
            dataGridViewCellStyle4.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle4.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(243)))), ((int)(((byte)(249)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle4.Font = new System.Drawing.Font("微软雅黑", 12F);
            dataGridViewCellStyle4.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            dataGridViewCellStyle4.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(80)))), ((int)(((byte)(160)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle4.SelectionForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            dataGridViewCellStyle4.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgvDetailData.RowHeadersDefaultCellStyle = dataGridViewCellStyle4;
            this.dgvDetailData.RowHeadersVisible = false;
            dataGridViewCellStyle5.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle5.BackColor = System.Drawing.Color.White;
            dataGridViewCellStyle5.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            dataGridViewCellStyle5.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(236)))), ((int)(((byte)(255)))));
            dataGridViewCellStyle5.SelectionForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.dgvDetailData.RowsDefaultCellStyle = dataGridViewCellStyle5;
            this.dgvDetailData.RowTemplate.Height = 29;
            this.dgvDetailData.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.dgvDetailData.SelectedIndex = -1;
            this.dgvDetailData.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgvDetailData.Size = new System.Drawing.Size(629, 819);
            this.dgvDetailData.TabIndex = 1;
            // 
            // uiPanel1
            // 
            this.uiPanel1.Controls.Add(this.gbDetailData);
            this.uiPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uiPanel1.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiPanel1.Location = new System.Drawing.Point(381, 1);
            this.uiPanel1.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.uiPanel1.MinimumSize = new System.Drawing.Size(1, 1);
            this.uiPanel1.Name = "uiPanel1";
            this.uiPanel1.Padding = new System.Windows.Forms.Padding(1, 0, 0, 0);
            this.uiPanel1.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.uiPanel1.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.uiPanel1.Size = new System.Drawing.Size(632, 852);
            this.uiPanel1.TabIndex = 2;
            this.uiPanel1.Text = null;
            this.uiPanel1.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // saveFileDialog
            // 
            this.saveFileDialog.Filter = "excel文件|*.xlsx";
            this.saveFileDialog.Title = "数据报表导出";
            // 
            // UC_WCS3900CheckData
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.Controls.Add(this.uiPanel1);
            this.Controls.Add(this.gbCheckData);
            this.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.Name = "UC_WCS3900CheckData";
            this.Padding = new System.Windows.Forms.Padding(1);
            this.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.Size = new System.Drawing.Size(1014, 854);
            this.gbCheckData.ResumeLayout(false);
            this.gbDetailData.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dgvDetailData)).EndInit();
            this.uiPanel1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion
        private Sunny.UI.UIGroupBox gbCheckData;
        private System.Windows.Forms.FlowLayoutPanel pnlCheckData;
        private Sunny.UI.UIGroupBox gbDetailData;
        private Sunny.UI.UIDataGridView dgvDetailData;
        private Sunny.UI.UIPanel uiPanel1;
        private Sunny.UI.UIButton btnExport;
        private System.Windows.Forms.SaveFileDialog saveFileDialog;
    }
}
