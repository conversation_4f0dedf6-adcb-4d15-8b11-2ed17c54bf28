﻿using System;
using System.ComponentModel;
using System.Text;
using Fpi.Util.Extensions;
using Fpi.WMS3000.Equipment.Config;
using Fpi.WMS3000.Equipment.DS;
using Fpi.WMS3000.Equipment.JDRKRS;
using Fpi.WMS3000.Equipment.LXAM125;
using Fpi.WMS3000.Equipment.UPS;
using Fpi.WMS3000.SystemConfig.SmartPatrol.Helper;

namespace Fpi.WMS3000.SystemConfig.SmartPatrol.Config
{
    /// <summary>
    /// 辅助单元巡检结果
    /// </summary>
    public class FZUnitPatrolResult : SingleUnitPatrolResultBase
    {
        #region 字段属性

        /// <summary>
        /// UPS通讯状态
        /// </summary>
        [Description("UPS通讯状态")]
        public eModuleWorkingState UPSComState { get; set; }

        /// <summary>
        /// UPS设备参数
        /// </summary>
        [Description("UPS设备参数")]
        public YstEA900Param UPSParams { get; set; } = new YstEA900Param();

        /// <summary>
        /// UPS投入使用时间
        /// </summary>
        [Description("UPS投入使用时间")]
        public DateTime? UpsUsageState { get; set; }

        /// <summary>
        /// 智能电表通讯状态
        /// 待替换设备
        /// </summary>
        [Description("智能电表通讯状态")]
        public eModuleWorkingState ElectricMeterComState { get; set; }

        /// <summary>
        /// 智能电表设备参数
        /// </summary>
        [Description("智能电表设备参数")]
        public DS6CNZDESParam ElectricMeterParams { get; set; } = new DS6CNZDESParam();

        /// <summary>
        /// 防雷模块通讯状态
        /// </summary>
        [Description("防雷模块通讯状态")]
        public eModuleWorkingState ThunderComState { get; set; }

        ///// <summary>
        ///// 防雷模块设备参数
        ///// </summary>
        [Description("防雷模块设备参数")]
        public AM125Param ThunderParams { get; set; } = new AM125Param();

        /// <summary>
        /// PDU电源管理器通讯状态
        /// </summary>
        [Description("PDU电源管理器通讯状态")]
        public eModuleWorkingState PDUComState { get; set; }

        /// <summary>
        /// 空调控制器通讯状态
        /// </summary>
        [Description("空调控制器通讯状态")]
        public eModuleWorkingState KTComState { get; set; }

        /// <summary>
        /// 空调控制器设备参数
        /// </summary>
        [Description("空调控制器设备参数")]
        public JDRKRSParam KTParams { get; set; }

        /// <summary>
        /// 空调投入使用时间
        /// </summary>
        [Description("空调投入使用时间")]
        public DateTime? KTUsageState { get; set; }

        /// <summary>
        /// 臭氧发生器投入使用时间
        /// </summary>
        [Description("臭氧发生器投入使用时间")]
        public DateTime? OzoneUsageState { get; set; }

        /// <summary>
        /// 空压机投入使用时间
        /// </summary>
        [Description("空压机投入使用时间")]
        public DateTime? AirPressUsageState { get; set; }

        /// <summary>
        /// 换气扇投入使用时间
        /// </summary>
        [Description("换气扇投入使用时间")]
        public DateTime? AirVentilatorUsageState { get; set; }

        /// <summary>
        /// 环境温度(℃)
        /// </summary>
        [Description("环境温度(℃)")]
        public double? Temp { get; set; }

        /// <summary>
        /// 环境湿度(%)
        /// </summary>
        [Description("环境湿度(%)")]
        public double? Humidity { get; set; }

        /// <summary>
        /// 环境温度状态
        /// </summary>
        [Description("环境温度状态")]
        public eModuleWorkingState TempState { get; set; }

        /// <summary>
        /// 环境湿度状态
        /// </summary>
        [Description("环境湿度状态")]
        public eModuleWorkingState HumidityState { get; set; }

        /// <summary>
        /// 烟雾报警状态
        /// </summary>
        [Description("烟雾报警状态")]
        public eModuleWorkingState SmokeAlarmState { get; set; }

        /// <summary>
        /// 水浸报警状态
        /// </summary>
        [Description("水浸报警状态")]
        public eModuleWorkingState WaterLeakageAlarmState { get; set; }

        /// <summary>
        /// 门禁通讯状态
        /// </summary>
        [Description("门禁通讯状态")]
        public eModuleWorkingState DoorBanComState { get; set; }

        /// <summary>
        /// 门禁布防状态
        /// </summary>
        [Description("门禁布防状态")]
        public eModuleWorkingState DoorBanClothState { get; set; }

        /// <summary>
        /// 门禁报警状态
        /// </summary>
        [Description("门禁报警状态")]
        public eModuleWorkingState DoorBanAlarmState { get; set; }

        #endregion

        #region 构造

        public FZUnitPatrolResult()
        {
            UnitId = "Auxiliary";
            UnitName = "辅助单元";
        }

        #endregion

        #region 方法重写

        /// <summary>
        /// 打印巡检结果
        /// </summary>
        /// <returns></returns>
        public override string GetResultStr()
        {
            StringBuilder resultStr = new StringBuilder();

            resultStr.AppendLine(UnitName)
                .AppendLine($"巡检结果：{PatrolResult}")
                .AppendLine($"巡检详情：")
                .AppendLine($"UPS通讯状态：{UPSComState}")
                .AppendLine($"UPS投入使用时间：{UpsUsageState.ToDisplayData()}")
                .AppendLine($"UPS设备参数：{GetUPSParams(UPSParams)}");
            resultStr.AppendLine($"智能电表通讯状态：{ElectricMeterComState}")
                .AppendLine($"智能电表参数：{GetDS6CNZDESParams(ElectricMeterParams)}");
            resultStr.AppendLine($"防雷模块通讯状态：{ThunderComState}")
                .AppendLine($"防雷模块参数：{GetThunderParams(ThunderParams)}");
            resultStr.AppendLine($"PDU电源管理器通讯状态：{PDUComState}")
                .AppendLine($"空调控制器通讯状态：{KTComState}")
                .AppendLine($"空调投入使用时间：{KTUsageState}")
                .AppendLine($"臭氧发生器投入使用时间：{OzoneUsageState}")
                .AppendLine($"空压机投入使用时间：{AirPressUsageState}")
                .AppendLine($"换气扇投入使用时间：{AirVentilatorUsageState}")
                .AppendLine($"环境温度：{Temp.ToDisplayFormat(PatrolDataDisplayHelper.FloatFormat, "℃")}")
                .AppendLine($"环境湿度：{Humidity.ToDisplayFormat(PatrolDataDisplayHelper.FloatFormat, "℃")}")
                .AppendLine($"环境温度状态：{TempState}")
                .AppendLine($"环境湿度状态：{HumidityState}")
                .AppendLine($"烟雾报警状态：{SmokeAlarmState}")
                .AppendLine($"水浸报警状态：{WaterLeakageAlarmState}")
                .AppendLine($"门禁通讯状态：{DoorBanComState}")
                .AppendLine($"门禁布防状态：{DoorBanClothState}")
                .AppendLine($"门禁报警状态：{DoorBanAlarmState}");

            return resultStr.ToString();
        }

        /// <summary>
        /// 获取UPS参数描述
        /// </summary>
        /// <param name="uPSParams"></param>
        /// <returns></returns>
        private string GetUPSParams(YstEA900Param upsParams)
        {
            return upsParams == null
                ? string.Empty
                : $"输入电压{upsParams.InputVoltage.ToDisplayFormat(PatrolDataDisplayHelper.FloatFormat, "V")},输出电压{upsParams.OutputVoltage.ToDisplayFormat(PatrolDataDisplayHelper.FloatFormat, "V")},电池电量{upsParams.BatteryLevel.ToDisplayFormat(PatrolDataDisplayHelper.FloatFormat, "%")}";
        }

        /// <summary>
        /// 获取防雷模块参数描述
        /// </summary>
        /// <param name="thunderParams"></param>
        /// <returns></returns>
        private string GetThunderParams(AM125Param thunderParams)
        {
            return thunderParams == null
                ? string.Empty
                : $"温度报警-{thunderParams.TempAlarm},失效报警-{thunderParams.FailureAlarm},线路报警-{thunderParams.LineAlarm},更换预警-{thunderParams.ReplaceEarlyAlarm}";
        }

        /// <summary>
        /// 获取智能电表模块参数描述
        /// </summary>
        /// <param name="thunderParams"></param>
        /// <returns></returns>
        private string GetDS6CNZDESParams(DS6CNZDESParam electricMeterParams)
        {
            return electricMeterParams == null
                ? string.Empty
                : $"总功率-{electricMeterParams.TotalPower.ToDisplayFormat(PatrolDataDisplayHelper.FloatFormat, "kW")},A相电压-{electricMeterParams.AVoltage.ToDisplayFormat(PatrolDataDisplayHelper.FloatFormat, "V")},A相电流-{electricMeterParams.ACurrent.ToDisplayFormat(PatrolDataDisplayHelper.FloatFormat, "A")},B相电压-{electricMeterParams.BVoltage.ToDisplayFormat(PatrolDataDisplayHelper.FloatFormat, "V")},B相电流-{electricMeterParams.BCurrent.ToDisplayFormat(PatrolDataDisplayHelper.FloatFormat, "A")},A相电压-{electricMeterParams.CVoltage.ToDisplayFormat(PatrolDataDisplayHelper.FloatFormat, "V")},C相电流-{electricMeterParams.CCurrent.ToDisplayFormat(PatrolDataDisplayHelper.FloatFormat, "A")}";
        }

        #endregion
    }
}