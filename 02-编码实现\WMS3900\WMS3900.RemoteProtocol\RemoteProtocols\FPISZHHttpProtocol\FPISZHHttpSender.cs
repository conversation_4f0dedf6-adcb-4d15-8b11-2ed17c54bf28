﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading;
using Fpi.Communication.Interfaces;
using Fpi.Communication.Manager;
using Fpi.Communication.Protocols;
using Fpi.DB;
using Fpi.HB.Business.Protocols.Helper;
using Fpi.HB.Business.Protocols.Interface;
using Fpi.Json;
using Fpi.WMS3000.DB;
using Fpi.WMS3000.Equipment.Config;
using Fpi.WMS3000.Remote.FPISZHHttp.DataFrame;
using Fpi.WMS3000.SystemConfig.SmartPatrol.Config;
using Timer = System.Threading.Timer;

namespace Fpi.WMS3000.Remote.FPISZHHttp
{
    /// <summary>
    /// 谱育科技数智化水站数据传输协议（Http）（协议发送器）
    /// </summary>
    public class FPISZHHttpSender : Sender, IDataUpload, IGetSuppleData
    {
        #region 字段属性

        /// <summary>
        /// 当前协议描述
        /// </summary>
        private FPISZHHttpProtocolDesc _desc;

        /// <summary>
        /// 获取计划触发时间锁
        /// </summary>
        private readonly object _reportLockObj = new object();

        /// <summary>
        /// 统计数据上传定时器
        /// </summary>
        private Timer _timerReportCount;

        /// <summary>
        /// 智能巡检报告接口路径
        /// </summary>
        private const string SmartPatrolReportApiUrl = "/water-api/water/digital/inspection-data/save";

        /// <summary>
        /// 工况模拟数据接口路径
        /// </summary>
        private const string SimulatedDataApiUrl = "/water-api/water/digital/simulated-data/save";

        #endregion

        #region 方法重写

        /// <summary>
        /// 管道生效事件方法
        /// </summary>
        /// <param name="pip"></param>
        protected override void ActionPipe(Pipe pip)
        {
            base.ActionPipe(pip);
            _desc = pip.Protocol.ProtocolDesc as FPISZHHttpProtocolDesc;
            //  初始化定时器配置
            _timerReportCount = new Timer(ReportCountFunc, null, 1000, 3000);
        }

        /// <summary>
        /// 重写 上传实时数据
        /// </summary>
        /// <param name="currentPipe"></param>
        public override void SendData(Pipe currentPipe)
        {
            // 链接断开时，不传输
            if(!currentPipe.Connected)
            {
                return;
            }

            // 暂不做实际业务
        }

        /// <summary>
        /// //上传统计数据
        /// </summary>
        /// <param name="obj"></param>
        private void ReportCountFunc(object obj)
        {
            // 通道关闭，不传输
            if(!CurrentPipe.valid)
            {
                return;
            }
            // 链接断开时，仍运行，数据进入数据补传表

            #region 参数定义

            DateTime now = DateTime.Now;
            bool stateTrigger = false;

            #endregion

            #region 获取各类型数据上传触发时间点

            // 获取各类型数据上传触发时间点
            try
            {
                lock(_reportLockObj)
                {
                    stateTrigger = _desc.SingleCfg.IsTriggerStatePLan(now);
                }
            }
            catch(Exception e)
            {
                ProtocolLogHelper.ShowMsg($"获取数据自动上传计划出错:{e.Message}");
            }

            #endregion

            #region 根据触发情况上传数据

            // 工况模拟数据
            if(stateTrigger)
            {
                UploadData((int)eFpiHttpUploadDataType.工况模拟数据);
            }

            #endregion
        }

        #endregion

        #region IDisposable 成员

        public override void Dispose()
        {
            base.Dispose();
            if(_timerReportCount != null)
            {
                _timerReportCount.Change(Timeout.Infinite, Timeout.Infinite);
                _timerReportCount.Dispose();
                _timerReportCount = null;
            }
        }

        #endregion

        #region IDataUpload

        /// <summary>
        /// 上传数据类型
        /// </summary>
        public Type UploadDataType { get => typeof(eFpiHttpUploadDataType); }

        /// <summary>
        /// 上传数据接口
        /// </summary>
        /// <param name="type"></param>
        public void UploadData(int type)
        {
            if(_desc != null)
            {
                try
                {
                    if(!Enum.IsDefined(UploadDataType, type))
                    {
                        throw new Exception($"上传类型数值{type}不在本协议支持的类型{UploadDataType.Name}的定义范围内");
                    }
                    eFpiHttpUploadDataType uploadType = (eFpiHttpUploadDataType)type;

                    // 待发送数据表
                    var cmdList = new List<Tuple<FPISZHHttpCommand, string>>();
                    switch(uploadType)
                    {
                        case eFpiHttpUploadDataType.工况模拟数据:
                            cmdList.Add(new Tuple<FPISZHHttpCommand, string>(GetSimulatedData(), SimulatedDataApiUrl));
                            break;

                        case eFpiHttpUploadDataType.智能巡检报告:
                            cmdList.Add(new Tuple<FPISZHHttpCommand, string>(GetSmartPatrolReport(), SmartPatrolReportApiUrl));
                            break;
                    }

                    // 发送数据
                    foreach(var cmd in cmdList)
                    {
                        // 同步发送
                        _desc.PostData(cmd.Item1, cmd.Item2);
                    }
                }
                catch(Exception e)
                {
                    ProtocolLogHelper.ShowMsg($"上传{(eFpiHttpUploadDataType)type}类型数据出错:{e.Message}");
                }
            }
            else
            {
                throw new Exception("协议描述器为空！");
            }
        }

        #endregion

        #region IGetSuppleData

        public List<IByteStream> GetSuppleData(DateTime startTime, DateTime endTime, int type)
        {
            if(!Enum.IsDefined(UploadDataType, type))
            {
                throw new Exception($"上传类型数值{type}不在本协议支持的类型{UploadDataType.Name}的定义范围内");
            }
            eFpiHttpUploadDataType uploadType = (eFpiHttpUploadDataType)type;

            List<FPISZHHttpCommand> cmdLists;
            switch(uploadType)
            {
                case eFpiHttpUploadDataType.智能巡检报告:
                    cmdLists = GetHistorySmartPatrolReport(startTime, endTime);
                    // 发送数据
                    foreach(var cmd in cmdLists)
                    {
                        // 同步发送
                        _desc.PostData(cmd, SmartPatrolReportApiUrl);
                    }
                    break;

                default:
                    throw new Exception($"当前协议不支持补传 {uploadType} 类型数据！");
            }

            // 实际上不在补传界面做业务实现（数据已发送），只借助界面触发补传
            return cmdLists?.Cast<IByteStream>().ToList();
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 工况模拟数据组装
        /// </summary>
        private FPISZHHttpCommand GetSimulatedData()
        {
            FPISZHHttpCommand cmd = new FPISZHHttpCommand();
            cmd.mnCode = _desc.MN;
            cmd.jsonData = new StationStateInfo();

            return cmd;
        }

        /// <summary>
        /// 智能巡检报告组装
        /// </summary>
        private FPISZHHttpCommand GetSmartPatrolReport()
        {
            FPISZHHttpCommand cmd = new FPISZHHttpCommand();
            cmd.mnCode = _desc.MN;
            cmd.jsonData = SmartPatrolManager.GetInstance().LatestSmartPatrolResult;

            return cmd;
        }

        /// <summary>
        /// 历史智能巡检报告组装
        /// </summary>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <returns></returns>
        private List<FPISZHHttpCommand> GetHistorySmartPatrolReport(DateTime startTime, DateTime endTime)
        {
            try
            {
                List<FPISZHHttpCommand> resultList = new List<FPISZHHttpCommand>();
                var strSql = $"select id,datatime,patrolresult from {DbConfig.SMARTPATROL_RESULT_TABLE} where datatime>='{startTime.ToString(DbConfig.DATETIME_FORMAT)}' and datatime<='{endTime.ToString(DbConfig.DATETIME_FORMAT)}' order by datatime asc";
                IDataReader reader = DbAccess.ExecuteQueryReturnDataReader(strSql);

                while(reader.Read())
                {
                    string jsonData = reader.GetString(2);
                    SmartPatrolResult result = FpiJsonHelper.JsonToModel<SmartPatrolResult>(jsonData);
                    if(result != null)
                    {
                        FPISZHHttpCommand cmd = new FPISZHHttpCommand();
                        cmd.mnCode = _desc.MN;
                        cmd.jsonData = result;

                        resultList.Add(cmd);
                    }
                }
                return resultList;
            }
            catch(Exception)
            {
                return null;
            }
        }

        #endregion
    }
}