﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using Fpi.Communication.Interfaces;
using Fpi.Communication.Protocols;
using Fpi.HB.Business.Protocols.Helper;
using Fpi.HB.Business.Protocols.Interface;
using Fpi.WMS3000.Equipment.WCS3900;
using Fpi.WMS3000.Remote.GJDBS;
using Fpi.WMS3000.Remote.GJDBS.Config;
using Fpi.WMS3000.Remote.GJDBS.GJDBSDataFrame;

namespace Fpi.WMS3000.Remote.FPISZH
{
    /// <summary>
    /// 谱育科技数智化水站数据传输协议-数据发送器
    /// </summary>
    public class FPISZHSender : GJDBSSender, IDataUpload, IGetSuppleData
    {
        #region IDataUpload

        /// <summary>
        /// 重写部分数据类型上传逻辑
        /// </summary>
        /// <param name="type"></param>
        public override void UploadData(int type)
        {
            if(!Enum.IsDefined(UploadDataType, type))
            {
                throw new Exception($"上传类型数值{type}不在本协议支持的类型{UploadDataType.Name}的定义范围内");
            }
            eUploadDataType uploadType = (eUploadDataType)type;

            switch(uploadType)
            {
                // 定制部分数据类型，走自定义协议处理；
                case eUploadDataType.五参数核查数据:
                case eUploadDataType.五参数水样比对数据:
                case eUploadDataType.盲样测试:
                case eUploadDataType.空白测试:
                case eUploadDataType.任意浓度核查:
                case eUploadDataType.多点线性核查:
                    CustomUploadData(uploadType);
                    break;

                // 非定制部分数据类型，走标准协议处理；
                default:
                    base.UploadData(type);
                    return;
            }
        }

        /// <summary>
        /// 定制数据上传部分
        /// </summary>
        /// <param name="type"></param>
        private void CustomUploadData(eUploadDataType type)
        {
            if(_desc != null)
            {
                try
                {
                    // 待发送数据表
                    var cmdList = new List<GJDBSCommand>();
                    switch(type)
                    {
                        case eUploadDataType.五参数核查数据:
                            cmdList.AddRange(FPISZHHelper.BuildFiveParamCheckData(_desc, DateTime.Now.AddDays(-1), DateTime.Now, eWCS3900DetailDataType.核查数据, eGetDataCount.最新一条数据));
                            break;
                        case eUploadDataType.五参数水样比对数据:
                            cmdList.AddRange(FPISZHHelper.BuildFiveParamCheckData(_desc, DateTime.Now.AddDays(-1), DateTime.Now, eWCS3900DetailDataType.水样比对数据, eGetDataCount.最新一条数据));
                            break;
                        case eUploadDataType.盲样测试:
                            if(_desc.GjdbsSingleCfg.ReportEquipState)
                            {
                                cmdList.AddRange(GJDBSHelper.GetAllDevCurrentParamCmdList(_desc, DateTime.Now.AddDays(-1), DateTime.Now, eUploadDataType.跨度核查数据));
                                //防止生成同一个QN，要睡眠一下
                                Thread.Sleep(50);
                            }
                            cmdList.AddRange(FPISZHHelper.BuildBlindCheckData(_desc, DateTime.Now.AddDays(-1), DateTime.Now, eGetDataCount.最新一条数据));
                            break;
                        case eUploadDataType.空白测试:
                            if(_desc.GjdbsSingleCfg.ReportEquipState)
                            {
                                cmdList.AddRange(GJDBSHelper.GetAllDevCurrentParamCmdList(_desc, DateTime.Now.AddDays(-1), DateTime.Now, eUploadDataType.跨度核查数据));
                                //防止生成同一个QN，要睡眠一下
                                Thread.Sleep(50);
                            }
                            cmdList.AddRange(FPISZHHelper.BuildBlankTestData(_desc, DateTime.Now.AddDays(-1), DateTime.Now, eGetDataCount.最新一条数据));
                            break;
                        case eUploadDataType.任意浓度核查:
                            if(_desc.GjdbsSingleCfg.ReportEquipState)
                            {
                                cmdList.AddRange(GJDBSHelper.GetAllDevCurrentParamCmdList(_desc, DateTime.Now.AddDays(-1), DateTime.Now, eUploadDataType.跨度核查数据));
                                //防止生成同一个QN，要睡眠一下
                                Thread.Sleep(50);
                            }
                            cmdList.AddRange(FPISZHHelper.BuildArbitraryCheckData(_desc, DateTime.Now.AddDays(-1), DateTime.Now, eGetDataCount.最新一条数据));
                            break;
                        case eUploadDataType.多点线性核查:
                            cmdList.AddRange(FPISZHHelper.BuildMultiPointCheckData(_desc, DateTime.Now.AddDays(-1), DateTime.Now, eGetDataCount.最新一条数据));
                            break;

                        // 非定制部分数据类型，走标准协议处理；
                        default:
                            throw new Exception($"类型{type}非定制部分数据类型，请走标准协议处理");
                    }

                    // 发送数据
                    foreach(GJDBSCommand cmd in cmdList)
                    {
                        _desc.SendDataWithAddendum(cmd);
                    }
                }
                catch(Exception e)
                {
                    ProtocolLogHelper.ShowMsg($"上传{(eFpiHttpUploadDataType)type}类型数据出错:{e.Message}");
                }
            }
            else
            {
                //throw new Exception("协议描述器为空！");
            }
        }

        #endregion

        #region IGetSuppleData

        /// <summary>
        /// 定制数据补传部分
        /// </summary>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        public override List<IByteStream> GetSuppleData(DateTime startTime, DateTime endTime, int type)
        {
            if(!Enum.IsDefined(UploadDataType, type))
            {
                throw new Exception($"上传类型数值{type}不在本协议支持的类型{UploadDataType.Name}的定义范围内");
            }
            eUploadDataType uploadType = (eUploadDataType)type;

            List<GJDBSCommand> cmdLists;
            switch(uploadType)
            {
                // 定制部分数据类型，走自定义协议处理；
                case eUploadDataType.五参数核查数据:
                    cmdLists = FPISZHHelper.BuildFiveParamCheckData(_desc, startTime, endTime, eWCS3900DetailDataType.核查数据, eGetDataCount.所有数据);
                    break;
                case eUploadDataType.五参数水样比对数据:
                    cmdLists = FPISZHHelper.BuildFiveParamCheckData(_desc, startTime, endTime, eWCS3900DetailDataType.水样比对数据, eGetDataCount.所有数据);
                    break;
                case eUploadDataType.盲样测试:
                    cmdLists = FPISZHHelper.BuildBlindCheckData(_desc, startTime, endTime, eGetDataCount.所有数据);
                    break;
                case eUploadDataType.空白测试:
                    cmdLists = FPISZHHelper.BuildBlankTestData(_desc, startTime, endTime, eGetDataCount.所有数据);
                    break;
                case eUploadDataType.任意浓度核查:
                    cmdLists = FPISZHHelper.BuildArbitraryCheckData(_desc, startTime, endTime, eGetDataCount.所有数据);
                    break;
                case eUploadDataType.多点线性核查:
                    cmdLists = FPISZHHelper.BuildMultiPointCheckData(_desc, startTime, endTime, eGetDataCount.所有数据);
                    break;

                // 非定制部分数据类型，走标准协议处理；
                default:
                    return base.GetSuppleData(startTime, endTime, type);
            }

            return cmdLists?.Cast<IByteStream>().ToList();
        }

        #endregion
    }
}