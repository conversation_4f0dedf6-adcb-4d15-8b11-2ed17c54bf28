﻿namespace Fpi.WMS3000.Equipment.UI
{
    partial class UC_SIA3900DiagnosisRecords
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if(disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.gbInfo = new Sunny.UI.UIGroupBox();
            this.uiPanel2 = new Sunny.UI.UIPanel();
            this.uiPanel4 = new Sunny.UI.UIPanel();
            this.uiGroupBox4 = new Sunny.UI.UIGroupBox();
            this.btnSelfCheck = new Sunny.UI.UIButton();
            this.uiGroupBox2 = new Sunny.UI.UIGroupBox();
            this.cmbDeviceCheckType = new Sunny.UI.UIComboBox();
            this.btnDeviceCheckTypeSet = new Sunny.UI.UIButton();
            this.uiGroupBox1 = new Sunny.UI.UIGroupBox();
            this.lblCurrentResult = new Sunny.UI.UILabel();
            this.uiLabel5 = new Sunny.UI.UILabel();
            this.lblCurrentTime = new Sunny.UI.UILabel();
            this.uiLabel10 = new Sunny.UI.UILabel();
            this.uiLabel11 = new Sunny.UI.UILabel();
            this.lblCurrentTask = new Sunny.UI.UILabel();
            this.uiPanel3 = new Sunny.UI.UIPanel();
            this.uiPanel1 = new Sunny.UI.UIPanel();
            this.btnRefresh = new Sunny.UI.UISymbolButton();
            this.backgroundWorker1 = new System.ComponentModel.BackgroundWorker();
            this.uc_DiagnosisQueryData = new Fpi.WMS3000.Equipment.UI.UC_SIA3900DiagnosisQueryData();
            this.gbInfo.SuspendLayout();
            this.uiPanel2.SuspendLayout();
            this.uiPanel4.SuspendLayout();
            this.uiGroupBox4.SuspendLayout();
            this.uiGroupBox2.SuspendLayout();
            this.uiGroupBox1.SuspendLayout();
            this.uiPanel3.SuspendLayout();
            this.SuspendLayout();
            // 
            // gbInfo
            // 
            this.gbInfo.Controls.Add(this.uiPanel2);
            this.gbInfo.Controls.Add(this.uiPanel1);
            this.gbInfo.Controls.Add(this.btnRefresh);
            this.gbInfo.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gbInfo.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.gbInfo.Location = new System.Drawing.Point(0, 0);
            this.gbInfo.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.gbInfo.MinimumSize = new System.Drawing.Size(1, 1);
            this.gbInfo.Name = "gbInfo";
            this.gbInfo.Padding = new System.Windows.Forms.Padding(0, 32, 0, 0);
            this.gbInfo.Size = new System.Drawing.Size(1616, 896);
            this.gbInfo.TabIndex = 3;
            this.gbInfo.Text = "诊断信息";
            this.gbInfo.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // uiPanel2
            // 
            this.uiPanel2.Controls.Add(this.uiPanel4);
            this.uiPanel2.Controls.Add(this.uiPanel3);
            this.uiPanel2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uiPanel2.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiPanel2.Location = new System.Drawing.Point(0, 32);
            this.uiPanel2.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.uiPanel2.MinimumSize = new System.Drawing.Size(1, 1);
            this.uiPanel2.Name = "uiPanel2";
            this.uiPanel2.RectColor = System.Drawing.Color.Transparent;
            this.uiPanel2.Size = new System.Drawing.Size(1616, 864);
            this.uiPanel2.TabIndex = 2;
            this.uiPanel2.Text = "uiPanel2";
            this.uiPanel2.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // uiPanel4
            // 
            this.uiPanel4.Controls.Add(this.uiGroupBox4);
            this.uiPanel4.Controls.Add(this.uiGroupBox2);
            this.uiPanel4.Controls.Add(this.uiGroupBox1);
            this.uiPanel4.Dock = System.Windows.Forms.DockStyle.Left;
            this.uiPanel4.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiPanel4.Location = new System.Drawing.Point(0, 0);
            this.uiPanel4.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.uiPanel4.MinimumSize = new System.Drawing.Size(1, 1);
            this.uiPanel4.Name = "uiPanel4";
            this.uiPanel4.RectColor = System.Drawing.Color.Transparent;
            this.uiPanel4.Size = new System.Drawing.Size(455, 864);
            this.uiPanel4.TabIndex = 1;
            this.uiPanel4.Text = null;
            this.uiPanel4.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // uiGroupBox4
            // 
            this.uiGroupBox4.Controls.Add(this.btnSelfCheck);
            this.uiGroupBox4.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiGroupBox4.Location = new System.Drawing.Point(50, 221);
            this.uiGroupBox4.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.uiGroupBox4.MinimumSize = new System.Drawing.Size(1, 1);
            this.uiGroupBox4.Name = "uiGroupBox4";
            this.uiGroupBox4.Padding = new System.Windows.Forms.Padding(0, 32, 0, 0);
            this.uiGroupBox4.Size = new System.Drawing.Size(367, 150);
            this.uiGroupBox4.TabIndex = 134;
            this.uiGroupBox4.Text = "设备自诊断";
            this.uiGroupBox4.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // btnSelfCheck
            // 
            this.btnSelfCheck.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnSelfCheck.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnSelfCheck.Location = new System.Drawing.Point(116, 69);
            this.btnSelfCheck.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnSelfCheck.Name = "btnSelfCheck";
            this.btnSelfCheck.Size = new System.Drawing.Size(127, 29);
            this.btnSelfCheck.TabIndex = 108;
            this.btnSelfCheck.Text = "设备自诊断";
            this.btnSelfCheck.TipsFont = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnSelfCheck.Click += new System.EventHandler(this.btnSelfCheck_Click);
            // 
            // uiGroupBox2
            // 
            this.uiGroupBox2.Controls.Add(this.cmbDeviceCheckType);
            this.uiGroupBox2.Controls.Add(this.btnDeviceCheckTypeSet);
            this.uiGroupBox2.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiGroupBox2.Location = new System.Drawing.Point(50, 41);
            this.uiGroupBox2.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.uiGroupBox2.MinimumSize = new System.Drawing.Size(1, 1);
            this.uiGroupBox2.Name = "uiGroupBox2";
            this.uiGroupBox2.Padding = new System.Windows.Forms.Padding(0, 32, 0, 0);
            this.uiGroupBox2.Size = new System.Drawing.Size(367, 150);
            this.uiGroupBox2.TabIndex = 133;
            this.uiGroupBox2.Text = "器件自检";
            this.uiGroupBox2.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // cmbDeviceCheckType
            // 
            this.cmbDeviceCheckType.DataSource = null;
            this.cmbDeviceCheckType.DropDownStyle = Sunny.UI.UIDropDownStyle.DropDownList;
            this.cmbDeviceCheckType.FillColor = System.Drawing.Color.White;
            this.cmbDeviceCheckType.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.cmbDeviceCheckType.ItemHoverColor = System.Drawing.Color.FromArgb(((int)(((byte)(155)))), ((int)(((byte)(200)))), ((int)(((byte)(255)))));
            this.cmbDeviceCheckType.ItemSelectForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(243)))), ((int)(((byte)(255)))));
            this.cmbDeviceCheckType.Location = new System.Drawing.Point(66, 72);
            this.cmbDeviceCheckType.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.cmbDeviceCheckType.MinimumSize = new System.Drawing.Size(63, 0);
            this.cmbDeviceCheckType.Name = "cmbDeviceCheckType";
            this.cmbDeviceCheckType.Padding = new System.Windows.Forms.Padding(0, 0, 30, 2);
            this.cmbDeviceCheckType.Size = new System.Drawing.Size(155, 29);
            this.cmbDeviceCheckType.SymbolSize = 24;
            this.cmbDeviceCheckType.TabIndex = 16;
            this.cmbDeviceCheckType.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            this.cmbDeviceCheckType.Watermark = "";
            // 
            // btnDeviceCheckTypeSet
            // 
            this.btnDeviceCheckTypeSet.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnDeviceCheckTypeSet.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnDeviceCheckTypeSet.Location = new System.Drawing.Point(256, 72);
            this.btnDeviceCheckTypeSet.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnDeviceCheckTypeSet.Name = "btnDeviceCheckTypeSet";
            this.btnDeviceCheckTypeSet.Size = new System.Drawing.Size(76, 29);
            this.btnDeviceCheckTypeSet.TabIndex = 14;
            this.btnDeviceCheckTypeSet.Text = "触发";
            this.btnDeviceCheckTypeSet.TipsFont = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnDeviceCheckTypeSet.Click += new System.EventHandler(this.btnDeviceCheckTypeSet_Click);
            // 
            // uiGroupBox1
            // 
            this.uiGroupBox1.Controls.Add(this.lblCurrentResult);
            this.uiGroupBox1.Controls.Add(this.uiLabel5);
            this.uiGroupBox1.Controls.Add(this.lblCurrentTime);
            this.uiGroupBox1.Controls.Add(this.uiLabel10);
            this.uiGroupBox1.Controls.Add(this.uiLabel11);
            this.uiGroupBox1.Controls.Add(this.lblCurrentTask);
            this.uiGroupBox1.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiGroupBox1.Location = new System.Drawing.Point(50, 401);
            this.uiGroupBox1.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.uiGroupBox1.MinimumSize = new System.Drawing.Size(1, 1);
            this.uiGroupBox1.Name = "uiGroupBox1";
            this.uiGroupBox1.Padding = new System.Windows.Forms.Padding(0, 32, 0, 0);
            this.uiGroupBox1.Size = new System.Drawing.Size(367, 389);
            this.uiGroupBox1.TabIndex = 0;
            this.uiGroupBox1.Text = "最新诊断记录";
            this.uiGroupBox1.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // lblCurrentResult
            // 
            this.lblCurrentResult.AutoSize = true;
            this.lblCurrentResult.BackColor = System.Drawing.Color.Transparent;
            this.lblCurrentResult.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblCurrentResult.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.lblCurrentResult.Location = new System.Drawing.Point(182, 297);
            this.lblCurrentResult.Name = "lblCurrentResult";
            this.lblCurrentResult.Size = new System.Drawing.Size(115, 21);
            this.lblCurrentResult.TabIndex = 77;
            this.lblCurrentResult.Text = "— — — — —";
            // 
            // uiLabel5
            // 
            this.uiLabel5.AutoSize = true;
            this.uiLabel5.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel5.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel5.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel5.Location = new System.Drawing.Point(67, 81);
            this.uiLabel5.Name = "uiLabel5";
            this.uiLabel5.Size = new System.Drawing.Size(74, 21);
            this.uiLabel5.TabIndex = 72;
            this.uiLabel5.Text = "当前时间";
            // 
            // lblCurrentTime
            // 
            this.lblCurrentTime.AutoSize = true;
            this.lblCurrentTime.BackColor = System.Drawing.Color.Transparent;
            this.lblCurrentTime.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblCurrentTime.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.lblCurrentTime.Location = new System.Drawing.Point(182, 81);
            this.lblCurrentTime.Name = "lblCurrentTime";
            this.lblCurrentTime.Size = new System.Drawing.Size(115, 21);
            this.lblCurrentTime.TabIndex = 73;
            this.lblCurrentTime.Text = "— — — — —";
            // 
            // uiLabel10
            // 
            this.uiLabel10.AutoSize = true;
            this.uiLabel10.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel10.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel10.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel10.Location = new System.Drawing.Point(67, 189);
            this.uiLabel10.Name = "uiLabel10";
            this.uiLabel10.Size = new System.Drawing.Size(74, 21);
            this.uiLabel10.TabIndex = 74;
            this.uiLabel10.Text = "当前任务";
            // 
            // uiLabel11
            // 
            this.uiLabel11.AutoSize = true;
            this.uiLabel11.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel11.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel11.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel11.Location = new System.Drawing.Point(67, 297);
            this.uiLabel11.Name = "uiLabel11";
            this.uiLabel11.Size = new System.Drawing.Size(74, 21);
            this.uiLabel11.TabIndex = 76;
            this.uiLabel11.Text = "诊断结果";
            // 
            // lblCurrentTask
            // 
            this.lblCurrentTask.AutoSize = true;
            this.lblCurrentTask.BackColor = System.Drawing.Color.Transparent;
            this.lblCurrentTask.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblCurrentTask.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.lblCurrentTask.Location = new System.Drawing.Point(182, 189);
            this.lblCurrentTask.Name = "lblCurrentTask";
            this.lblCurrentTask.Size = new System.Drawing.Size(115, 21);
            this.lblCurrentTask.TabIndex = 75;
            this.lblCurrentTask.Text = "— — — — —";
            // 
            // uiPanel3
            // 
            this.uiPanel3.Controls.Add(this.uc_DiagnosisQueryData);
            this.uiPanel3.Dock = System.Windows.Forms.DockStyle.Right;
            this.uiPanel3.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiPanel3.Location = new System.Drawing.Point(463, 0);
            this.uiPanel3.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.uiPanel3.MinimumSize = new System.Drawing.Size(1, 1);
            this.uiPanel3.Name = "uiPanel3";
            this.uiPanel3.Padding = new System.Windows.Forms.Padding(3);
            this.uiPanel3.RectColor = System.Drawing.Color.Transparent;
            this.uiPanel3.Size = new System.Drawing.Size(1153, 864);
            this.uiPanel3.TabIndex = 0;
            this.uiPanel3.Text = "uiPanel3";
            this.uiPanel3.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // uiPanel1
            // 
            this.uiPanel1.BackColor = System.Drawing.Color.Transparent;
            this.uiPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uiPanel1.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiPanel1.Location = new System.Drawing.Point(0, 32);
            this.uiPanel1.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.uiPanel1.MinimumSize = new System.Drawing.Size(1, 1);
            this.uiPanel1.Name = "uiPanel1";
            this.uiPanel1.RectColor = System.Drawing.Color.Transparent;
            this.uiPanel1.Size = new System.Drawing.Size(1616, 864);
            this.uiPanel1.TabIndex = 1;
            this.uiPanel1.Text = null;
            this.uiPanel1.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // btnRefresh
            // 
            this.btnRefresh.BackColor = System.Drawing.Color.Transparent;
            this.btnRefresh.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnRefresh.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnRefresh.IsCircle = true;
            this.btnRefresh.Location = new System.Drawing.Point(214, 0);
            this.btnRefresh.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnRefresh.Name = "btnRefresh";
            this.btnRefresh.Size = new System.Drawing.Size(35, 35);
            this.btnRefresh.Symbol = 61473;
            this.btnRefresh.TabIndex = 3;
            this.btnRefresh.TipsFont = new System.Drawing.Font("微软雅黑", 11F);
            this.btnRefresh.TipsText = "刷新";
            this.btnRefresh.Click += new System.EventHandler(this.btnRefresh_Click);
            // 
            // uc_DiagnosisQueryData
            // 
            this.uc_DiagnosisQueryData.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uc_DiagnosisQueryData.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uc_DiagnosisQueryData.Location = new System.Drawing.Point(3, 3);
            this.uc_DiagnosisQueryData.MinimumSize = new System.Drawing.Size(1, 1);
            this.uc_DiagnosisQueryData.Name = "uc_DiagnosisQueryData";
            this.uc_DiagnosisQueryData.Size = new System.Drawing.Size(1147, 858);
            this.uc_DiagnosisQueryData.TabIndex = 0;
            this.uc_DiagnosisQueryData.Text = "uC_SIA3900DiagnosisQueryData1";
            this.uc_DiagnosisQueryData.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // UC_SIA3900DiagnosisRecords
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.Controls.Add(this.gbInfo);
            this.Name = "UC_SIA3900DiagnosisRecords";
            this.Size = new System.Drawing.Size(1616, 896);
            this.gbInfo.ResumeLayout(false);
            this.uiPanel2.ResumeLayout(false);
            this.uiPanel4.ResumeLayout(false);
            this.uiGroupBox4.ResumeLayout(false);
            this.uiGroupBox2.ResumeLayout(false);
            this.uiGroupBox1.ResumeLayout(false);
            this.uiGroupBox1.PerformLayout();
            this.uiPanel3.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private Sunny.UI.UIGroupBox gbInfo;
        private Sunny.UI.UIPanel uiPanel2;
        private Sunny.UI.UIPanel uiPanel1;
        private Sunny.UI.UISymbolButton btnRefresh;
        private Sunny.UI.UIPanel uiPanel4;
        private Sunny.UI.UIGroupBox uiGroupBox1;
        private Sunny.UI.UIPanel uiPanel3;
        private System.ComponentModel.BackgroundWorker backgroundWorker1;
        private Sunny.UI.UIGroupBox uiGroupBox2;
        private Sunny.UI.UIComboBox cmbDeviceCheckType;
        private Sunny.UI.UIButton btnDeviceCheckTypeSet;
        private Sunny.UI.UILabel lblCurrentResult;
        private Sunny.UI.UILabel uiLabel5;
        private Sunny.UI.UILabel lblCurrentTime;
        private Sunny.UI.UILabel uiLabel10;
        private Sunny.UI.UILabel uiLabel11;
        private Sunny.UI.UILabel lblCurrentTask;
        private Sunny.UI.UIGroupBox uiGroupBox4;
        private Sunny.UI.UIButton btnSelfCheck;
        private UI.UC_SIA3900DiagnosisQueryData uc_DiagnosisQueryData;
    }
}
