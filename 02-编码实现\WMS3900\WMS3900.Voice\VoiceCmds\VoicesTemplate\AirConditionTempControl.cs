﻿using System;
using System.Threading.Tasks;
using Fpi.WMS3000.Equipment;
using Fpi.WMS3000.Equipment.Config;
using Fpi.WMS3000.Voice.Helper;

namespace Fpi.WMS3000.Voice.VoicesTemplate
{
    /// <summary>
    /// 空调温度设置
    /// </summary>
    public class AirConditionTempControl : CustomVoiceCmd
    {
        #region 公共方法（重写）

        public override string ToString()
        {
            return "空调温度设置";
        }

        public override string CustomDo(int paran)
        {
            try
            {
                // 温度值检查
                if(paran == int.MinValue)
                {
                    throw new Exception("请输入合法温度值！");
                }

                // 找到空调设备
                if(ExterEquipConfigManager.GetInstance().DeviceSelect.AirControlDevice is not JDRKRSEquip airEquip)
                {
                    throw new Exception("系统内未配置空调设备！");
                }

                Task.Run(() =>
                {
                    try
                    {
                        airEquip.SetACTemp(paran);

                        VoiceLogHelper.Info($"[{CmdConfig.name}]命令执行成功，调节空调温度至{paran}℃。");
                    }
                    catch(Exception e)
                    {
                        VoiceLogHelper.Info($"[{CmdConfig.name}]命令执行出错：{e.Message}");
                    }
                });

                return $"好的，正在调节空调温度至{paran}℃";
            }
            catch(Exception ex)
            {
                VoiceLogHelper.Info($"[{CmdConfig.name}]命令执行出错: {ex.Message}");
                return $"抱歉，执行出错，{ex.Message}";
            }
        }

        #endregion
    }
}