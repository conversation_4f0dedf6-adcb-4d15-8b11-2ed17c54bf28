﻿using System;
using Fpi.UI.PC.Config;
using Fpi.WMS3000.Voice.Config;
using Fpi.WMS3000.Voice.Interface;
using Fpi.Xml;
using Sunny.UI;

namespace Fpi.WMS3000.Voice.VoicesTemplate
{
    /// <summary>
    /// 软件界面切换
    /// </summary>
    public partial class UC_SoftUISwitch : UIUserControl, IVoiceConfig
    {
        #region 字段属性

        /// <summary>
        /// 命令配置信息存储
        /// </summary>
        private VoiceControlCmd _voiceCmd;

        #endregion

        #region 构造

        public UC_SoftUISwitch()
        {
            InitializeComponent();

            // 初始化一级界面
            cmbTopLevelItem.Items.AddRange(VoiceHelper.GetTopLevelItem().ToArray());
        }

        #endregion

        #region 事件

        private void cmbTopLevelItem_SelectedIndexChanged(object sender, EventArgs e)
        {
            if(cmbTopLevelItem.SelectedItem is ToolItem toolItem)
            {
                var itemlist = VoiceHelper.GetSecondLevelItem(toolItem).ToArray();
                if(itemlist.Length > 0)
                {
                    SetDisplayLevel(2);
                    // 动态加载二级界面
                    cmbSecondLevelItem.Items.Clear();
                    cmbSecondLevelItem.Items.AddRange(itemlist);
                }
                else
                {
                    SetDisplayLevel(1);
                }
            }
            else
            {
                SetDisplayLevel(1);
            }
        }

        private void cmbSecondLevelItem_SelectedIndexChanged(object sender, EventArgs e)
        {
            if(cmbSecondLevelItem.SelectedItem is MenuGroup menuGroup)
            {
                var itemlist = VoiceHelper.GetThirdLevelItem(menuGroup).ToArray();
                if(itemlist.Length > 0)
                {
                    SetDisplayLevel(3);
                    // 动态加载三级界面
                    cmbThirdLevelItem.Items.Clear();
                    cmbThirdLevelItem.Items.AddRange(itemlist);
                }
                else
                {
                    SetDisplayLevel(2);
                }
            }
            else
            {
                SetDisplayLevel(2);
            }
        }

        #endregion

        #region IVoiceConfig接口

        public void Check()
        {
            if(cmbTopLevelItem.SelectedItem is not ToolItem toolItem)
            {
                throw new Exception("请选择一级界面！");
            }

            if(cmbSecondLevelItem.Visible && cmbSecondLevelItem.SelectedItem is not IdNameNode secondLevelItem)
            {
                throw new Exception("请选择二级界面！");
            }

            if(cmbThirdLevelItem.Visible && cmbThirdLevelItem.SelectedItem is not InLookItem inLookItem)
            {
                throw new Exception("请选择三级界面！");
            }
        }

        public void LoadConfig(VoiceControlCmd cmd)
        {
            _voiceCmd = cmd;

            string uiItemId = _voiceCmd.GetPropertyValue(GlobalNameDefine.PropertyName_UIItemId);
            // 当前配置项为一级菜单
            if(UIManager.GetInstance().toolBar.toolItems.FindNode(uiItemId) is ToolItem toolItem)
            {
                cmbTopLevelItem.SelectedItem = toolItem;
                SetDisplayLevel(1);
            }
            else
            {
                // 遍历一级菜单
                foreach(ToolItem topLevelItem in VoiceHelper.GetTopLevelItem())
                {
                    // 获取对应二级菜单节点
                    var secondLevelItemlist = VoiceHelper.GetSecondLevelItem(topLevelItem);
                    // 遍历二级菜单
                    foreach(var secondLevelItem in secondLevelItemlist)
                    {
                        // 当前配置项为二级菜单
                        if(secondLevelItem.id == uiItemId && secondLevelItem is SubToolItem)
                        {
                            cmbTopLevelItem.SelectedItem = topLevelItem;
                            cmbSecondLevelItem.SelectedItem = secondLevelItem;
                            SetDisplayLevel(2);
                            return;
                        }
                        else if(secondLevelItem is MenuGroup menuGroup)
                        {
                            // 获取对应三级菜单节点
                            var thirdLevelItemlist = VoiceHelper.GetThirdLevelItem(menuGroup);
                            // 遍历三级菜单
                            foreach(var thirdLevelItem in thirdLevelItemlist)
                            {
                                // 当前配置项为三级菜单
                                if(thirdLevelItem.id == uiItemId && thirdLevelItem is not null)
                                {
                                    cmbTopLevelItem.SelectedItem = topLevelItem;
                                    cmbSecondLevelItem.SelectedItem = secondLevelItem;
                                    cmbThirdLevelItem.SelectedItem = thirdLevelItem;
                                    SetDisplayLevel(3);
                                    return;
                                }
                            }
                        }
                    }
                }
            }
        }

        public VoiceControlCmd Save()
        {
            // 一级界面
            ToolItem toolItem = cmbTopLevelItem.SelectedItem as ToolItem;

            // 二级界面不可见，当前使用的一级界面
            if(!cmbSecondLevelItem.Visible)
            {
                _voiceCmd.SetProperty(GlobalNameDefine.PropertyName_UIItemId, toolItem?.id);
            }
            else
            {
                IdNameNode secondLevelItem = cmbSecondLevelItem.SelectedItem as IdNameNode;

                // 三级界面不可见，当前使用的二级界面
                if(!cmbThirdLevelItem.Visible)
                {
                    _voiceCmd.SetProperty(GlobalNameDefine.PropertyName_UIItemId, secondLevelItem?.id);
                }
                // 三级界面可见，当前使用的三级界面
                else
                {
                    InLookItem inLookItem = cmbThirdLevelItem.SelectedItem as InLookItem;
                    _voiceCmd.SetProperty(GlobalNameDefine.PropertyName_UIItemId, inLookItem?.id);
                }
            }

            return _voiceCmd;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 设置菜单显示层级
        /// </summary>
        private void SetDisplayLevel(int level)
        {
            switch(level)
            {
                case 2:
                    cmbSecondLevelItem.Visible = txtSecondLevelItem.Visible = true;
                    cmbThirdLevelItem.Visible = txtThirdLevelItem.Visible = false;
                    break;
                case 3:
                    cmbSecondLevelItem.Visible = txtSecondLevelItem.Visible = true;
                    cmbThirdLevelItem.Visible = txtThirdLevelItem.Visible = true;
                    break;
                default:
                    cmbSecondLevelItem.Visible = txtSecondLevelItem.Visible = false;
                    cmbThirdLevelItem.Visible = txtThirdLevelItem.Visible = false;
                    break;
            }
        }

        #endregion
    }
}