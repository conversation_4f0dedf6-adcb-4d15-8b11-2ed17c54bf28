﻿using System;
using Fpi.Util.Extensions;
using Fpi.WMS3000.Algorithm;
using Fpi.WMS3000.Equipment;
using Fpi.WMS3000.Equipment.Config;
using OpenCvSharp;

namespace Fpi.WMS3000.SystemConfig.ImagePatrol.Config
{
    /// <summary>
    /// 高指氨氮分析仪状态检测执行类
    /// </summary>
    public class CodMnNH4EquipPatrol : ImageUnitSmartPatrolBase
    {
        #region 构造

        public CodMnNH4EquipPatrol()
        {
            UnitId = "CodMnNH4Equip";
            UnitName = "高指氨氮分析仪状态检测";
        }

        #endregion

        #region 方法重写

        public override void ExecutePatrol(OldImagePatrolResult patrolResult)
        {
            if(ExterEquipConfigManager.GetInstance().CameraSelect.CodMnNH4EquipCamera == null)
            {
                throw new Exception("对应摄像机未配置！");
            }

            ExterEquipConfigManager.GetInstance().CameraSelect.CodMnNH4EquipCamera.ScreenShot(out string picPath);
            patrolResult.CodMnNH4EquipCameraImagePath = FileExtension.GetRelativePath(picPath);
            AlgorithmHelper.CheckSIA3900State(new Mat(picPath), out bool leftPipeSmutState, out bool leftReactionUnitSmutState, out bool rightPipeSmutState, out bool rightReactionUnitSmutState);
            patrolResult.CodMnPipeSmutState = leftPipeSmutState ? eSmutState.脏污 : eSmutState.正常;
            patrolResult.CodMnReactionUnitSmutState = leftReactionUnitSmutState ? eSmutState.脏污 : eSmutState.正常;
            patrolResult.NH4PipeSmutState = rightPipeSmutState ? eSmutState.脏污 : eSmutState.正常;
            patrolResult.NH4ReactionUnitSmutState = rightReactionUnitSmutState ? eSmutState.脏污 : eSmutState.正常;
        }

        #endregion
    }
}