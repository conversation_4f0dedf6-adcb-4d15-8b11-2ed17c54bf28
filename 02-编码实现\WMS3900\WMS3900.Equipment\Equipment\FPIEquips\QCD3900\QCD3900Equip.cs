﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using Fpi.Alarm;
using Fpi.Communication;
using Fpi.Communication.Converter;
using Fpi.Communication.Interfaces;
using Fpi.Devices;
using Fpi.Devices.DeviceProtocols;
using Fpi.Util.EnumRelated;
using Fpi.WMS3000.Equipment.Interface;
using Fpi.WMS3000.Equipment.QCD3900;
using Fpi.WMS3000.Equipment.UI;

namespace Fpi.WMS3000.Equipment
{
    /// <summary>
    /// 谱育科技QCD3900质控设备
    /// </summary>
    public class QCD3900Equip : Device, IQCDeviceOperation
    {
        #region 字段属性

        /// <summary>
        /// 设备状态相关参数
        /// </summary>
        public QCD3900DeviceStateParam DeviceStateParams { get; } = new QCD3900DeviceStateParam();

        /// <summary>
        /// 器件寿命信息
        /// </summary>
        public QCD3900ElementLifeInfo ElementLifeInfos { get; } = new QCD3900ElementLifeInfo();

        /// <summary>
        /// 测量相关参数
        /// </summary>
        public QCD3900MeasureParam MeasureParams { get; } = new QCD3900MeasureParam();

        /// <summary>
        /// 设备日志区
        /// </summary>
        public QCD3900LogArea LogArea { get; } = new();

        #endregion

        #region 构造

        public QCD3900Equip()
        {
            this.DeviceType = eDeviceType.AUXILI;

            if(string.IsNullOrEmpty(this.Description))
            {
                this.Description = "QCD3900质控设备。数据通道需要关联对应因子。";
            }

            if(this.id.Contains("DefaultID"))
            {
                this.id = "QCD3900_Equip";
            }

            if(this.name.Contains("DefaultName"))
            {
                this.name = "QCD3900质控设备";
            }

            if(string.IsNullOrEmpty(this.AlarmGroupId))
            {
                this.AlarmGroupId = "QCD3900_Equip";
            }

            if(string.IsNullOrEmpty(this.AlarmSourceId))
            {
                this.AlarmSourceId = "QCD3900_Equip";
            }

            if(string.IsNullOrEmpty(this.ProtocolImp))
            {
                this.ProtocolImp = typeof(ModbusProtocol).FullName;
            }
        }

        #endregion

        #region 公共（重写）方法

        public override string ToString()
        {
            return string.IsNullOrEmpty(this.name) || this.name.Contains("DefaultName") ? "谱育科技QCD3900质控设备" : this.name;
        }

        /// <summary>
        /// 报警设置
        /// </summary>
        public override void SetDeviceAlarmList()
        {
            base.SetDeviceAlarmList();
            this.DeviceAlarmList.Add("1", "RTC故障");
            this.DeviceAlarmList.Add("2", "外部Flash故障");
            this.DeviceAlarmList.Add("3", "柱塞泵溢出");
            this.DeviceAlarmList.Add("4", "柱塞泵通信异常");
            this.DeviceAlarmList.Add("5", "柱塞泵移动返回报文异常");
            this.DeviceAlarmList.Add("6", "柱塞泵上限位移动警告");
            this.DeviceAlarmList.Add("7", "柱塞泵校验失败");
            this.DeviceAlarmList.Add("8", "温度检测异常");
            this.DeviceAlarmList.Add("9", "流程操作无效（无流路程序）");
            this.DeviceAlarmList.Add("10", "流程命令无效（流路错误）");
            this.DeviceAlarmList.Add("11", "零点液余量不足");
            this.DeviceAlarmList.Add("12", "核查液余量不足");
            this.DeviceAlarmList.Add("13", "母液余量不足");
            this.DeviceAlarmList.Add("14", "纯水余量不足");
            this.DeviceAlarmList.Add("15", "零点液保质期不足");
            this.DeviceAlarmList.Add("16", "核查液保质期不足");
            this.DeviceAlarmList.Add("17", "母液保质期不足");
            this.DeviceAlarmList.Add("18", "标样杯液位有液检测报警");
            this.DeviceAlarmList.Add("19", "标样杯液位无液检测报警");
            this.DeviceAlarmList.Add("20", "样品杯液位有液检测报警");
            this.DeviceAlarmList.Add("21", "样品杯液位无液检测报警");
            this.DeviceAlarmList.Add("22", "废液体积超出阈值报警");
            this.DeviceAlarmList.Add("23", "电磁阀组1寿命报警");
            this.DeviceAlarmList.Add("24", "电磁阀组2寿命报警");
            this.DeviceAlarmList.Add("25", "纯水泵PU1寿命报警");
            this.DeviceAlarmList.Add("26", "定容泵PU2寿命报警");
            this.DeviceAlarmList.Add("27", "样品杯气泵PU3寿命报警");
            this.DeviceAlarmList.Add("28", "标样杯气泵PU4寿命报警");
            this.DeviceAlarmList.Add("29", "原水夹管阀SV1寿命报警");
            this.DeviceAlarmList.Add("30", "水样杯夹管阀SV2寿命报警");
            this.DeviceAlarmList.Add("31", "三通电磁阀SV3寿命报警");
            this.DeviceAlarmList.Add("32", "三通电磁阀SV4寿命报警");
            this.DeviceAlarmList.Add("33", "标样杯夹管阀SV5寿命报警");
            this.DeviceAlarmList.Add("34", "柱塞泵寿命报警");
            this.DeviceAlarmList.Add("35", "门禁开启异常报警");
            this.DeviceAlarmList.Add("36", "门禁关闭异常报警");
            this.DeviceAlarmList.Add("37", "水样管保质期不足");
            this.DeviceAlarmList.Add("38", "储液环保质期不足");
            this.DeviceAlarmList.Add("39", "液位检测1器件的保质期不足");
            this.DeviceAlarmList.Add("40", "液位检测2器件的保质期不足");
            this.DeviceAlarmList.Add("41", "电磁阀组1保质期不足");
            this.DeviceAlarmList.Add("42", "电磁阀组2保质期不足");
            this.DeviceAlarmList.Add("43", "纯水泵PU1保质期不足");
            this.DeviceAlarmList.Add("44", "定容泵PU2保质期不足");
            this.DeviceAlarmList.Add("45", "样品杯气泵PU3保质期不足");
            this.DeviceAlarmList.Add("46", "标样杯气泵PU4保质期不足");
            this.DeviceAlarmList.Add("47", "原水夹管阀SV1保质期不足");
            this.DeviceAlarmList.Add("48", "水样杯夹管阀SV2保质期不足");
            this.DeviceAlarmList.Add("49", "三通电磁阀SV3保质期不足");
            this.DeviceAlarmList.Add("50", "三通电磁阀SV4保质期不足");
            this.DeviceAlarmList.Add("51", "标样杯夹管阀SV5保质期不足");
            this.DeviceAlarmList.Add("52", "柱塞泵保质期不足");
        }

        /// <summary>
        /// 设备状态参数查看界面
        /// </summary>
        public override UserControl GetDeviceParamUC()
        {
            return new UC_QCD3900Param(this);
        }

        /// <summary>
        /// 获取设备报警及状态数据
        /// </summary>
        public override void GetDeviceData()
        {
            try
            {
                #region 状态参数读取

                {
                    byte[] dataSend = { byte.Parse(this.Addr), 0x03, 0x00, 0x00, 0x00, 0x26 };
                    IByteStream bs = new ByteArrayWrap(dataSend);
                    IByteStream bsRecv = SendToDevice(id, bs);
                    if(bsRecv == null)
                    {
                        throw new Exception("读取状态参数无数据回应!");
                    }
                    byte[] dataReceive = bsRecv.GetBytes();

                    if(dataReceive.Length > 2 && dataReceive[1] == 0x83)
                    {
                        throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                    }
                    if(dataReceive.Length < 79)
                    {
                        throw new Exception("读取状态参数回应数据长度不合法！");
                    }
                    if(dataReceive[2] != 0x4C)
                    {
                        throw new Exception("读取状态参数回应数据错位！");
                    }

                    // 更新状态参数
                    DeviceStateParams.UpdateValue(dataReceive, 3);

                    // 解析报警
                    ParseDeviceAlarm(dataReceive, 15);

                    // 更新测量参数
                    MeasureParams.UpdateValue(dataReceive, 67);
                }

                #endregion

                #region 试剂维护信息读取

                {
                    byte[] dataSend = { byte.Parse(this.Addr), 0x03, 0x00, 0x26, 0x00, 0x2B };
                    IByteStream bs = new ByteArrayWrap(dataSend);
                    IByteStream bsRecv = SendToDevice(id, bs);
                    if(bsRecv == null)
                    {
                        throw new Exception("读取试剂维护信息无数据回应!");
                    }
                    byte[] dataReceive = bsRecv.GetBytes();

                    if(dataReceive.Length > 2 && dataReceive[1] == 0x83)
                    {
                        throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                    }
                    if(dataReceive.Length < 89)
                    {
                        throw new Exception("读取试剂维护信息回应数据长度不合法！");
                    }
                    if(dataReceive[2] != 0x56)
                    {
                        throw new Exception("读取试剂维护信息回应数据错位！");
                    }

                    // 更新试剂维护信息
                    ElementLifeInfos.UpdateLiqueValue(dataReceive, 3);
                }

                // 分包
                {
                    byte[] dataSend = { byte.Parse(this.Addr), 0x03, 0x00, 0x51, 0x00, 0x5A };
                    IByteStream bs = new ByteArrayWrap(dataSend);
                    IByteStream bsRecv = SendToDevice(id, bs);
                    if(bsRecv == null)
                    {
                        throw new Exception("读取器件维护信息无数据回应!");
                    }
                    byte[] dataReceive = bsRecv.GetBytes();

                    if(dataReceive.Length > 2 && dataReceive[1] == 0x83)
                    {
                        throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                    }
                    if(dataReceive.Length < 183)
                    {
                        throw new Exception("读取器件维护信息回应数据长度不合法！");
                    }
                    if(dataReceive[2] != 0xB4)
                    {
                        throw new Exception("读取器件维护信息回应数据错位！");
                    }

                    // 更新器件维护信息
                    ElementLifeInfos.UpdateDetectorValue(dataReceive, 3);
                }

                #endregion

                #region 日志信息读取

                {
                    byte[] dataSend = { byte.Parse(this.Addr), 0x03, 0x01, 0x22, 0x00, 0x1E };
                    IByteStream bs = new ByteArrayWrap(dataSend);
                    IByteStream bsRecv = SendToDevice(id, bs);
                    if(bsRecv == null)
                    {
                        throw new Exception("读取日志信息无数据回应!");
                    }
                    byte[] dataReceive = bsRecv.GetBytes();

                    if(dataReceive.Length > 2 && dataReceive[1] == 0x83)
                    {
                        throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                    }
                    if(dataReceive.Length < 63)
                    {
                        throw new Exception("读取日志信息回应数据长度不合法！");
                    }
                    if(dataReceive[2] != 0x3C)
                    {
                        throw new Exception("读取日志信息回应数据错位！");
                    }

                    // 更新日志信息
                    LogArea.UpdataValue(dataReceive, 3, this.id);
                }

                #endregion

                //消除通信异常报警
                AlarmManager.GetInstance().RemoveAlarm(AlarmSourceId, ComErrorAlarmCodeId);
                this._communicationErrorCount = 0;
            }
            catch(Exception ex)
            {
                if(this._communicationErrorCount++ >= 3)
                {
                    //增加通信异常报警
                    AlarmManager.GetInstance().AddAlarm(AlarmSourceId, ComErrorAlarmCodeId);
                }

                throw new Exception($"[{name}]读数失败：{ex.Message}");
            }
        }

        #endregion

        #region 公共方法

        #region 不定参数读写

        #region 写

        /// <summary>
        /// 下发多个参数到设备
        /// </summary>
        /// <param name="paramList"></param>
        public void WriteParamToDevice(short startIndex, List<object> paramList)
        {
            // 组装数据段
            List<byte> dataArea = new List<byte>();
            foreach(var param in paramList)
            {
                if(param is uint)
                {
                    dataArea.AddRange(DataConverter.GetInstance().GetBytes((ulong)param));
                }
                else if(param is int)
                {
                    dataArea.AddRange(DataConverter.GetInstance().GetBytes((long)param));
                }
                else if(param is ushort)
                {
                    dataArea.AddRange(DataConverter.GetInstance().GetBytes((uint)param));
                }
                else if(param is short)
                {
                    dataArea.AddRange(DataConverter.GetInstance().GetBytes((int)param));
                }
                else
                {
                    dataArea.AddRange(DataConverter.GetInstance().GetBytes((float)param));
                }
            }

            // 拼接完整报文
            List<byte> dataSend = new List<byte> { byte.Parse(this.Addr), 0x10, (byte)(startIndex >> 8), (byte)startIndex, 0x00, (byte)(dataArea.Count / 2), (byte)dataArea.Count };
            dataSend.AddRange(dataArea);
            IByteStream bs = new ByteArrayWrap(dataSend.ToArray());
            IByteStream bsRecv = SendToDevice(id, bs);
            if(bsRecv == null)
            {
                throw new Exception("无数据回应!");
            }
            byte[] dataReceive = bsRecv.GetBytes();

            if(dataReceive.Length > 2 && dataReceive[1] == 0x90)
            {
                throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
            }
            if(dataReceive.Length < 7)
            {
                throw new Exception("回应数据长度不合法！");
            }
        }

        /// <summary>
        /// 下发Float类型参数到设备
        /// </summary>
        /// <param name="param"></param>
        public void WriteFloatParamToDevice(short startIndex, float param)
        {
            List<byte> dataSend = new List<byte> { byte.Parse(this.Addr), 0x10, (byte)(startIndex >> 8), (byte)startIndex, 0x00, 0x02, 0x04 };
            dataSend.AddRange(DataConverter.GetInstance().GetBytes(param));
            IByteStream bs = new ByteArrayWrap(dataSend.ToArray());
            IByteStream bsRecv = SendToDevice(id, bs);
            if(bsRecv == null)
            {
                throw new Exception("无数据回应!");
            }
            byte[] dataReceive = bsRecv.GetBytes();

            if(dataReceive.Length > 2 && dataReceive[1] == 0x90)
            {
                throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
            }
            if(dataReceive.Length < 7)
            {
                throw new Exception("回应数据长度不合法！");
            }
        }

        /// <summary>
        /// 下发Int16类型参数到设备
        /// </summary>
        /// <param name="param"></param>
        public void WriteInt16ParamToDevice(short startIndex, short param)
        {
            List<byte> dataSend = new List<byte> { byte.Parse(this.Addr), 0x10, (byte)(startIndex >> 8), (byte)startIndex, 0x00, 0x01, 0x02 };
            dataSend.AddRange(DataConverter.GetInstance().GetBytes(param));
            IByteStream bs = new ByteArrayWrap(dataSend.ToArray());
            IByteStream bsRecv = SendToDevice(id, bs);
            if(bsRecv == null)
            {
                throw new Exception("无数据回应!");
            }
            byte[] dataReceive = bsRecv.GetBytes();

            if(dataReceive.Length > 2 && dataReceive[1] == 0x90)
            {
                throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
            }
            if(dataReceive.Length < 7)
            {
                throw new Exception("回应数据长度不合法！");
            }
        }

        /// <summary>
        /// 下发Int32类型参数到设备
        /// </summary>
        /// <param name="param"></param>
        public void WriteInt32ParamToDevice(short startIndex, int param)
        {
            List<byte> dataSend = new List<byte> { byte.Parse(this.Addr), 0x10, (byte)(startIndex >> 8), (byte)startIndex, 0x00, 0x02, 0x04 };
            dataSend.AddRange(DataConverter.GetInstance().GetBytes((long)param));
            IByteStream bs = new ByteArrayWrap(dataSend.ToArray());
            IByteStream bsRecv = SendToDevice(id, bs);
            if(bsRecv == null)
            {
                throw new Exception("无数据回应!");
            }
            byte[] dataReceive = bsRecv.GetBytes();

            if(dataReceive.Length > 2 && dataReceive[1] == 0x90)
            {
                throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
            }
            if(dataReceive.Length < 7)
            {
                throw new Exception("回应数据长度不合法！");
            }
        }

        #endregion

        #region 读

        /// <summary>
        /// 从设备上读取Float类型参数
        /// </summary>
        /// <param name="param"></param>
        public float ReadFloatParamFromDevice(short startIndex)
        {
            byte[] dataSend = { byte.Parse(this.Addr), 0x03, (byte)(startIndex >> 8), (byte)startIndex, 0x00, 0x02 };
            IByteStream bs = new ByteArrayWrap(dataSend);
            IByteStream bsRecv = SendToDevice(id, bs);
            if(bsRecv == null)
            {
                throw new Exception("无数据回应!");
            }
            byte[] dataReceive = bsRecv.GetBytes();

            if(dataReceive.Length > 2 && dataReceive[1] == 0x83)
            {
                throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
            }
            if(dataReceive.Length < 7)
            {
                throw new Exception("回应数据长度不合法！");
            }
            if(dataReceive[2] != 0x04)
            {
                throw new Exception("回应数据错位！");
            }

            return (float)DataConverter.GetInstance().ToSingle(dataReceive, 3);
        }

        /// <summary>
        /// 从设备上读取Int16类型参数
        /// </summary>
        /// <param name="param"></param>
        public int ReadInt16ParamFromDevice(short startIndex)
        {
            byte[] dataSend = { byte.Parse(this.Addr), 0x03, (byte)(startIndex >> 8), (byte)startIndex, 0x00, 0x01 };
            IByteStream bs = new ByteArrayWrap(dataSend);
            IByteStream bsRecv = SendToDevice(id, bs);
            if(bsRecv == null)
            {
                throw new Exception("无数据回应!");
            }
            byte[] dataReceive = bsRecv.GetBytes();

            if(dataReceive.Length > 2 && dataReceive[1] == 0x83)
            {
                throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
            }
            if(dataReceive.Length < 7)
            {
                throw new Exception("回应数据长度不合法！");
            }

            if(dataReceive[2] != 0x02)
            {
                throw new Exception("回应数据错位！");
            }

            return DataConverter.GetInstance().ToInt32(dataReceive, 3);
        }

        /// <summary>
        /// 从设备上读取Int32类型参数
        /// </summary>
        /// <param name="param"></param>
        public int ReadInt32ParamFromDevice(short startIndex)
        {
            byte[] dataSend = { byte.Parse(this.Addr), 0x03, (byte)(startIndex >> 8), (byte)startIndex, 0x00, 0x02 };
            IByteStream bs = new ByteArrayWrap(dataSend);
            IByteStream bsRecv = SendToDevice(id, bs);
            if(bsRecv == null)
            {
                throw new Exception("无数据回应!");
            }
            byte[] dataReceive = bsRecv.GetBytes();

            if(dataReceive.Length > 2 && dataReceive[1] == 0x83)
            {
                throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
            }
            if(dataReceive.Length < 7)
            {
                throw new Exception("回应数据长度不合法！");
            }
            if(dataReceive[2] != 0x04)
            {
                throw new Exception("回应数据错位！");
            }

            return (int)DataConverter.GetInstance().ToInt64(dataReceive, 3);
        }

        #endregion

        #endregion

        #region 时间校准

        public void SetDeviceTime(DateTime time)
        {
            try
            {
                byte[] dataSend = { byte.Parse(Addr), 0x10, 0x11, 0x01, 0x00, 0x03, 0x06, (byte)(time.Year - 2000), (byte)time.Month, (byte)time.Day, (byte)time.Hour, (byte)time.Minute, (byte)time.Second };

                IByteStream bs = new ByteArrayWrap(dataSend);
                IByteStream bsRecv = SendToDevice(id, bs);
                if(bsRecv == null)
                {
                    throw new Exception("无数据回应!");
                }
                byte[] dataReceive = bsRecv.GetBytes();

                if(dataReceive.Length > 2 && dataReceive[1] == 0x90)
                {
                    throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                }
                if(dataReceive.Length < 7)
                {
                    throw new Exception("回应数据长度不合法！");
                }
            }
            catch(Exception ex)
            {
                throw new Exception($"[{name}]校准时间失败：{ex.Message}");
            }
        }

        #endregion

        #region 门禁控制

        #region 密码修改

        /// <summary>
        /// 修改门禁密码
        /// </summary>
        /// <param name="oldPassword"></param>
        /// <param name="newPassword"></param>
        public void ChangeDoorPassword(List<byte> oldPassword, List<byte> newPassword)
        {
            try
            {
                if(oldPassword.Count != 6)
                {
                    throw new Exception("原密码应为6位数字！");
                }

                if(newPassword.Count != 6)
                {
                    throw new Exception("新密码应为6位数字！");
                }

                List<byte> dataSend = new List<byte> { byte.Parse(Addr), 0x10, 0x11, 0x08, 0x00, 0x06, 0x12, };
                dataSend.AddRange(oldPassword);
                dataSend.AddRange(newPassword);
                IByteStream bs = new ByteArrayWrap(dataSend.ToArray());
                IByteStream bsRecv = SendToDevice(id, bs);
                if(bsRecv == null)
                {
                    throw new Exception("无数据回应!");
                }
                byte[] dataReceive = bsRecv.GetBytes();

                if(dataReceive.Length > 2)
                {
                    if(dataReceive[1] == 0x90)
                    {
                        if(dataReceive[2] == (byte)eControlErrorType.非法数据值)
                        {
                            throw new Exception("原密码错误，请重试！");
                        }
                        else
                        {
                            throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                        }
                    }
                }
                if(dataReceive.Length < 7)
                {
                    throw new Exception("回应数据长度不合法！");
                }
            }
            catch(Exception ex)
            {
                throw new Exception($"[{name}]修改门禁密码失败：{ex.Message}");
            }
        }

        #endregion

        #region 开关

        /// <summary>
        /// 门禁开关
        /// </summary>
        /// <param name="password"></param>
        /// <param name="state"></param>
        public void DoorControl(List<byte> password, bool state)
        {
            try
            {
                if(password.Count != 6)
                {
                    throw new Exception("密码应为6位数字！");
                }

                List<byte> dataSend = new List<byte> { byte.Parse(Addr), 0x10, 0x11, 0x04, 0x00, 0x04, 0x08, 0x00, (byte)(state ? 0x01 : 0x00) };
                dataSend.AddRange(password);
                IByteStream bs = new ByteArrayWrap(dataSend.ToArray());
                IByteStream bsRecv = SendToDevice(id, bs);
                if(bsRecv == null)
                {
                    throw new Exception("无数据回应!");
                }
                byte[] dataReceive = bsRecv.GetBytes();

                if(dataReceive.Length > 2 && dataReceive[1] == 0x90)
                {
                    if(dataReceive[2] == (byte)eControlErrorType.非法数据值)
                    {
                        throw new Exception("密码错误，请重试！");
                    }
                    else
                    {
                        throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                    }
                }
                if(dataReceive.Length < 7)
                {
                    throw new Exception("回应数据长度不合法！");
                }
            }
            catch(Exception ex)
            {
                throw new Exception($"[{name}]开关门禁失败：{ex.Message}");
            }
        }

        #endregion

        #endregion

        #region 版本信息读取

        public void GetVersionSNMN(out string version, out string sn, out string mn)
        {
            try
            {
                byte[] dataSend = { byte.Parse(this.Addr), 0x03, 0x01, 0x00, 0x00, 0x22 };
                IByteStream bs = new ByteArrayWrap(dataSend);
                IByteStream bsRecv = SendToDevice(id, bs);
                if(bsRecv == null)
                {
                    throw new Exception("无数据回应!");
                }
                byte[] dataReceive = bsRecv.GetBytes();

                if(dataReceive.Length > 2 && dataReceive[1] == 0x83)
                {
                    throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                }
                if(dataReceive.Length < 71)
                {
                    throw new Exception("回应数据长度不合法！");
                }
                if(dataReceive[2] != 0x44)
                {
                    throw new Exception("回应数据错位！");
                }

                version = Encoding.UTF8.GetString(dataReceive, 3, 32).Replace("\0", "");
                sn = Encoding.UTF8.GetString(dataReceive, 35, 11).Replace("\0", "");
                mn = Encoding.UTF8.GetString(dataReceive, 47, 24).Replace("\0", "");
            }
            catch(Exception ex)
            {
                throw new Exception($"[{name}]读取版本信息失败：{ex.Message}");
            }
        }

        #endregion

        #region 点位控制

        /// <summary>
        /// 器件控制
        /// </summary>
        /// <param name="elementType"></param>
        /// <param name="state"></param>
        public void ElementControl(eQCD3900ElementType elementType, bool state)
        {
            try
            {
                List<byte> dataSend = new List<byte> { byte.Parse(Addr), 0x10, 0x11, 0x00, 0x00, 0x01, 0x02,
                    (byte)elementType, state ? (byte)1 : (byte)0 };
                IByteStream bs = new ByteArrayWrap(dataSend.ToArray());
                IByteStream bsRecv = SendToDevice(id, bs);
                if(bsRecv == null)
                {
                    throw new Exception("无数据回应!");
                }
                byte[] dataReceive = bsRecv.GetBytes();

                if(dataReceive.Length > 2 && dataReceive[1] == 0x90)
                {
                    throw new Exception($"{(eControlErrorType)dataReceive[2]}!");
                }
                if(dataReceive.Length < 7)
                {
                    throw new Exception("回应数据长度不合法！");
                }
            }
            catch(Exception ex)
            {
                throw new Exception($"[{name}]器件控制失败：{ex.Message}");
            }
        }

        #endregion

        #region 带参数流程控制

        public void StartOperWithParam(eQCDOperType opType, List<float> paramList)
        {
            try
            {
                // 组装数据段
                List<byte> dataArea = new List<byte>();
                foreach(var param in paramList)
                {
                    dataArea.AddRange(DataConverter.GetInstance().GetBytes((float)param));
                }

                // 拼接完整报文
                List<byte> dataSend = new List<byte> { byte.Parse(this.Addr), 0x10, 0x12, 0x00, 0x00, (byte)(dataArea.Count / 2 + 1), (byte)(dataArea.Count + 2) };
                dataSend.AddRange(new byte[] { 0x00, (byte)opType });
                dataSend.AddRange(dataArea);

                IByteStream bs = new ByteArrayWrap(dataSend.ToArray());
                IByteStream bsRecv = SendToDevice(id, bs);
                if(bsRecv == null)
                {
                    throw new Exception("无数据回应！");
                }

                byte[] dataRecv = bsRecv.GetBytes();
                if(dataRecv.Length > 2 && dataRecv[1] == 0x90)
                {
                    throw new Exception($"{(eControlErrorType)dataRecv[2]}!");
                }

                if(dataRecv.Length < 7)
                {
                    throw new Exception("回应数据长度不合法！");
                }
            }
            catch(Exception e)
            {
                throw new Exception($"触发{this.name}执行{opType}流程出错：{e.Message}");
            }
        }

        #endregion

        #endregion

        #region IQCDeviceOperation

        public void StartOper(eQCDOperType opType)
        {
            try
            {
                List<byte> dataSend = new List<byte>();
                dataSend.AddRange(new byte[] { byte.Parse(Addr), 0x10, 0x12, 0x00 });
                switch(opType)
                {
                    case eQCDOperType.紧急停止:
                        dataSend.Clear();
                        dataSend.AddRange(new byte[] { byte.Parse(Addr), 0x10, 0x11, 0x0E, 0x00, 0x01, 0x02, 0x00, 0x01 });
                        break;
                    case eQCDOperType.试剂导入:
                    case eQCDOperType.储液环清洗:
                    case eQCDOperType.管路排空:
                    case eQCDOperType.纯水定容:
                    case eQCDOperType.零点核查:
                    case eQCDOperType.跨度核查:
                    case eQCDOperType.空白核查:
                    case eQCDOperType.原水样测试:
                    case eQCDOperType.水样杯排空:
                    case eQCDOperType.水样杯清洗排空:
                    case eQCDOperType.标样杯排空:
                    case eQCDOperType.标样杯清洗排空:
                    case eQCDOperType.复位清洗排空:
                    case eQCDOperType.终端自检:
                    case eQCDOperType.水样杯鼓气:
                        dataSend.AddRange(new byte[] { 0x00, 0x01, 0x02, 0x00, (byte)opType });
                        break;
                    case eQCDOperType.母液计量:
                        dataSend.AddRange(new byte[] { 0x00, 0x03, 0x06, 0x00, (byte)opType });
                        dataSend.AddRange(DataConverter.GetInstance().GetBytes((float)MeasureParams.MotherLiquorVolumn));
                        break;
                    case eQCDOperType.标样核查:
                        dataSend.AddRange(new byte[] { 0x00, 0x03, 0x06, 0x00, (byte)opType });
                        dataSend.AddRange(DataConverter.GetInstance().GetBytes((float)MeasureParams.SampleCheckConfigValue));
                        break;
                    case eQCDOperType.静态加标回收:
                        dataSend.AddRange(new byte[] { 0x00, 0x05, 0x0A, 0x00, (byte)opType });
                        dataSend.AddRange(DataConverter.GetInstance().GetBytes((float)MeasureParams.StaticAddValue));
                        dataSend.AddRange(DataConverter.GetInstance().GetBytes((float)MeasureParams.StaticAddBeforeValue));
                        break;
                    case eQCDOperType.动态加标回收:
                        dataSend.AddRange(new byte[] { 0x00, 0x07, 0x0E, 0x00, (byte)opType });
                        dataSend.AddRange(DataConverter.GetInstance().GetBytes((float)MeasureParams.DynamicAddRatio));
                        dataSend.AddRange(DataConverter.GetInstance().GetBytes((float)MeasureParams.DynamicAddValue));
                        dataSend.AddRange(DataConverter.GetInstance().GetBytes((float)MeasureParams.DynamicAddBeforeValue));
                        break;
                    case eQCDOperType.多点线性核查A:
                        dataSend.AddRange(new byte[] { 0x00, 0x03, 0x06, 0x00, (byte)opType });
                        dataSend.AddRange(DataConverter.GetInstance().GetBytes((float)MeasureParams.CheckAConfigValue));
                        break;
                    case eQCDOperType.多点线性核查B:
                        dataSend.AddRange(new byte[] { 0x00, 0x03, 0x06, 0x00, (byte)opType });
                        dataSend.AddRange(DataConverter.GetInstance().GetBytes((float)MeasureParams.CheckBConfigValue));
                        break;
                    case eQCDOperType.多点线性核查C:
                        dataSend.AddRange(new byte[] { 0x00, 0x03, 0x06, 0x00, (byte)opType });
                        dataSend.AddRange(DataConverter.GetInstance().GetBytes((float)MeasureParams.CheckCConfigValue));
                        break;
                    case eQCDOperType.多点线性核查D:
                        dataSend.AddRange(new byte[] { 0x00, 0x03, 0x06, 0x00, (byte)opType });
                        dataSend.AddRange(DataConverter.GetInstance().GetBytes((float)MeasureParams.CheckDConfigValue));
                        break;
                    case eQCDOperType.盲样测试:
                        dataSend.AddRange(new byte[] { 0x00, 0x03, 0x06, 0x00, (byte)opType });
                        dataSend.AddRange(DataConverter.GetInstance().GetBytes((float)MeasureParams.BlindCheckConfigValue));
                        break;
                    default:
                        throw new Exception($"[{name}]被控操作失败，操作类型[{EnumOperate.GetEnumDesc(opType)}未实现！]");
                }

                IByteStream bs = new ByteArrayWrap(dataSend.ToArray());
                IByteStream bsRecv = SendToDevice(id, bs);
                if(bsRecv == null)
                {
                    throw new Exception("无数据回应！");
                }

                byte[] dataRecv = bsRecv.GetBytes();
                if(dataRecv.Length > 2 && dataRecv[1] == 0x90)
                {
                    throw new Exception($"{(eControlErrorType)dataRecv[2]}!");
                }

                if(dataRecv.Length < 7)
                {
                    throw new Exception("回应数据长度不合法！");
                }
            }
            catch(Exception e)
            {
                throw new Exception($"触发{this.name}执行{opType}流程出错：{e.Message}");
            }
        }

        /// <summary>
        /// 获取测量相关参数对象
        /// </summary>
        /// <returns></returns>
        public object GetEquipParam()
        {
            return MeasureParams;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 解析报警
        /// </summary>
        /// <param name="dataReceive"></param>
        private void ParseDeviceAlarm(byte[] dataReceive, int startIndex)
        {
            if(dataReceive.Length < startIndex + 10)
            {
                throw new Exception("读取设备报警回应数据不完整！");
            }

            // 遍历索引startIndex开始的10个字节，5个寄存器
            for(int i = 0; i < 5; i++)
            {
                // 取一个寄存器的报警信息
                int alarmInfo = DataConverter.GetInstance().ToInt32(dataReceive, startIndex + 2 * i);
                // 遍历齐数据位
                for(int j = 1; j <= 16; j++)
                {
                    // 本位对应报警的报警码号
                    int alarmCode = j + i * 16;

                    // 当前上传的报警码只有36个，提高遍历效率，超过36时直接退出
                    if(alarmCode > 36)
                    {
                        return;
                    }

                    //  判断最低位是否为1
                    if((alarmInfo & 0x0001) == 0x0001)
                    {
                        AlarmManager.GetInstance().AddAlarm(AlarmSourceId, alarmCode.ToString());
                    }
                    else
                    {
                        AlarmManager.GetInstance().RemoveAlarm(AlarmSourceId, alarmCode.ToString());
                    }

                    // 移位
                    alarmInfo >>= 1;
                }
            }
        }

        #endregion
    }
}