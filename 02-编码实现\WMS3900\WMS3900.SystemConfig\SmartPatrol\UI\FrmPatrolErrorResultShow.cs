﻿using System;
using Fpi.WMS3000.SystemConfig.SmartPatrol.Config;
using Fpi.WMS3000.SystemConfig.SmartPatrol.Helper;
using Sunny.UI;

namespace Fpi.WMS3000.SystemConfig
{
    /// <summary>
    /// 巡检异常结果显示界面
    /// </summary>
    public partial class FrmPatrolErrorResultShow : UIForm
    {
        #region 字段属性

        /// <summary>
        /// 对应智能巡检结果
        /// </summary>
        private SmartPatrolResult _smartPatrolResult;

        #endregion

        #region 构造

        public FrmPatrolErrorResultShow()
        {
            InitializeComponent();
        }

        public FrmPatrolErrorResultShow(SmartPatrolResult smartPatrolResult) : this()
        {
            _smartPatrolResult = smartPatrolResult;
        }

        #endregion

        #region 事件

        private void FrmPatrolErrorResultShow_Load(object sender, EventArgs e)
        {
            try
            {
                if(_smartPatrolResult != null)
                {
                    // 开始时间
                    lblStartTime.Text = _smartPatrolResult.StartTime.ToString("yyyy-MM-dd HH:mm:ss");
                    // 触发方式
                    lblPatrolTriggerType.Text = _smartPatrolResult.PatrolTriggerType.ToString();
                    // 异常信息
                    txtErrorResultInfo.AppendText(InspectionStructureBuilder.GetPatrolErrorResult(_smartPatrolResult));
                }
            }
            catch(Exception ex)
            {
                txtErrorResultInfo.AppendText($"加载出错：{ex.Message}");
            }
        }

        #endregion
    }
}