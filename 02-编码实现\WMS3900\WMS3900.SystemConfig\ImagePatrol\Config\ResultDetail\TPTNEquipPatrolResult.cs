﻿using System.Collections.Generic;
using System.ComponentModel;
using Fpi.WMS3000.Equipment;

namespace Fpi.WMS3000.SystemConfig.ImagePatrol.Config
{
    /// <summary>
    /// 总磷总氮分析仪巡检结果
    /// </summary>
    public class TPTNEquipPatrolResult : ImageUnitPatrolResultBase
    {
        #region 字段属性

        #region 总磷分析仪

        /// <summary>
        /// 总磷管路脏污状态
        /// </summary>
        [Description("总磷管路脏污状态")]
        public eSmutState TPPipeSmutState { get; set; }

        /// <summary>
        /// 总磷反应单元脏污状态
        /// </summary>
        [Description("总磷反应单元脏污状态")]
        public eSmutState TPReactionUnitSmutState { get; set; }

        #endregion

        #region 总氮分析仪

        /// <summary>
        /// 总氮管路脏污状态
        /// </summary>
        [Description("总氮管路脏污状态")]
        public eSmutState TNPipeSmutState { get; set; }

        /// <summary>
        /// 总氮反应单元脏污状态
        /// </summary>
        [Description("总氮反应单元脏污状态")]
        public eSmutState TNReactionUnitSmutState { get; set; }

        #endregion

        #endregion

        #region 构造

        public TPTNEquipPatrolResult()
        {
            UnitId = "TPTNEquip";
            UnitName = "总磷总氮分析仪";
        }

        #endregion

        #region 方法重写

        /// <summary>
        /// 打印巡检结果
        /// </summary>
        /// <returns></returns>
        public override List<string> GetResultStr()
        {
            return new List<string>
            {
                $"总磷管路脏污状态：{TPPipeSmutState}",
                $"总磷反应单元脏污状态：{TPReactionUnitSmutState}",
                $"总氮管路脏污状态：{TNPipeSmutState}",
                $"总氮反应单元脏污状态：{TNReactionUnitSmutState}"
            };
        }

        #endregion
    }
}