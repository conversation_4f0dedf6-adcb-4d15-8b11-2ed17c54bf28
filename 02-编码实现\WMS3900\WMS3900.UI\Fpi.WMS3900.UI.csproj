﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{10260BEA-42E4-4A5B-88A2-F48380CC9A31}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Fpi.WMS3000.UI</RootNamespace>
    <AssemblyName>Fpi.WMS3900.UI</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
    <SccProjectName>
    </SccProjectName>
    <SccLocalPath>
    </SccLocalPath>
    <SccAuxPath>
    </SccAuxPath>
    <SccProvider>
    </SccProvider>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\..\Product\Debug\bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>..\..\Product\Release\bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="DevComponents.DotNetBar2, Version=*********, Culture=neutral, PublicKeyToken=c39c3242a43eee2b, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\FpiDLL\DevComponents.DotNetBar2.dll</HintPath>
    </Reference>
    <Reference Include="Ionic.Zip, Version=*******, Culture=neutral, PublicKeyToken=edbe51ad942a3f5c, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\FpiDLL\Ionic.Zip.dll</HintPath>
    </Reference>
    <Reference Include="log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\FpiDLL\log4net.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.VisualBasic" />
    <Reference Include="Newtonsoft.Json, Version=*******, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\FpiDLL\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="NPOI, Version=*******, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\FpiDLL\NPOI.dll</HintPath>
    </Reference>
    <Reference Include="SunnyUI, Version=*******, Culture=neutral, PublicKeyToken=27d7d2e821d97aeb, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\FpiDLL\SunnyUI.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Design" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="WinFormsUI, Version=2.3.3505.27065, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\FpiDLL\WinFormsUI.dll</HintPath>
    </Reference>
    <Reference Include="ZedGraph, Version=5.1.5.19703, Culture=neutral, PublicKeyToken=02a83cbd123fcd60, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\FpiDLL\ZedGraph.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="FlowControls\Equipment\OptionsValves.cs" />
    <Compile Include="FlowControls\Equipment\EleWindMill.cs" />
    <Compile Include="FlowControls\Equipment\QCEquip.cs" />
    <Compile Include="FlowControls\Equipment\MeasureEquip.cs" />
    <Compile Include="FlowControls\FuncServices\FuncService.cs" />
    <Compile Include="FlowControls\Module\Circle.cs" />
    <Compile Include="FlowControls\Module\FloaterRight.cs" />
    <Compile Include="FlowControls\Module\FloaterLeft.cs" />
    <Compile Include="FlowControls\Module\Grit.cs" />
    <Compile Include="FlowControls\Module\PressureGage.cs" />
    <Compile Include="FlowControls\Module\PressAlaimLeft.cs" />
    <Compile Include="FlowControls\Module\PressAlaimRight.cs" />
    <Compile Include="FlowControls\Module\MiningPool.cs" />
    <Compile Include="FlowControls\Pipe\PipeBendLeftDowns.cs" />
    <Compile Include="FlowControls\Pipe\PipeBendLeftUps.cs" />
    <Compile Include="FlowControls\Pipe\PipeBendRightDowns.cs" />
    <Compile Include="FlowControls\Pipe\PipeBendRightUps.cs" />
    <Compile Include="FlowControls\Pipe\PeristalticValves.cs" />
    <Compile Include="FlowControls\Module\Piezometer.cs" />
    <Compile Include="FlowControls\Pipe\PipeTeeUp.cs" />
    <Compile Include="FlowControls\Pipe\PipeTeeRight.cs" />
    <Compile Include="FlowControls\Pipe\PipeTeeLeft.cs" />
    <Compile Include="FlowControls\Pipe\PipeTeeDown.cs" />
    <Compile Include="FlowControls\Pipe\PumpDown.cs" />
    <Compile Include="FlowControls\Pipe\PumpLeft.cs" />
    <Compile Include="FlowControls\Pipe\PumpRight.cs" />
    <Compile Include="FlowControls\Pipe\PumpUp.cs" />
    <Compile Include="FlowControls\Pipe\ThreeWayVaiveDown.cs" />
    <Compile Include="FlowControls\Pipe\ThreeWayVaiveUp.cs" />
    <Compile Include="FlowControls\Pipe\ThreeWayVaiveRight.cs" />
    <Compile Include="FlowControls\Pipe\ThreeWayVaiveLeft.cs" />
    <Compile Include="FlowControls\Module\WasteBottle.cs" />
    <Compile Include="FlowControls\Module\DoubleGritChamber.cs" />
    <Compile Include="FlowControls\Module\ForcePumpDowns.cs" />
    <Compile Include="FlowControls\Module\ForcePumpLefts.cs" />
    <Compile Include="FlowControls\Module\ForcePumpRights.cs" />
    <Compile Include="FlowControls\Module\ForcePumpUp.cs" />
    <Compile Include="FlowControls\Module\ForcePumpDown.cs" />
    <Compile Include="FlowControls\Module\ForcePumpLeft.cs" />
    <Compile Include="FlowControls\Module\ForcePumpRight.cs" />
    <Compile Include="FlowControls\Module\ForcePumpUps.cs" />
    <Compile Include="FlowControls\Module\GasStorager.cs" />
    <Compile Include="FlowControls\Module\HeaterControl.cs" />
    <Compile Include="FlowControls\Module\HuffCafity.cs" />
    <Compile Include="FlowControls\Module\MicroPump.cs" />
    <Compile Include="FlowControls\Module\PiezometerDown.cs" />
    <Compile Include="FlowControls\Module\PiezometerLeft.cs" />
    <Compile Include="FlowControls\Module\PiezometerRight.cs" />
    <Compile Include="FlowControls\Module\PiezometerUp.cs" />
    <Compile Include="FlowControls\Module\PressureSensorDown.cs" />
    <Compile Include="FlowControls\Module\PressureSensorLeft.cs" />
    <Compile Include="FlowControls\Module\PressureSensorRight.cs" />
    <Compile Include="FlowControls\Module\PressureSensorUp.cs" />
    <Compile Include="FlowControls\Module\SelfPumpDown.cs" />
    <Compile Include="FlowControls\Module\SelfPumpLeft.cs" />
    <Compile Include="FlowControls\Module\SelfPumpRight.cs" />
    <Compile Include="FlowControls\Module\SelfPumpUp.cs" />
    <Compile Include="FlowControls\Module\FiveParamController.cs" />
    <Compile Include="FlowControls\Module\InflaterHorizontal.cs" />
    <Compile Include="FlowControls\Module\InflaterVertical.cs" />
    <Compile Include="FlowControls\Module\WasterWaterReservoir.cs" />
    <Compile Include="FlowControls\Module\UPSpower.cs" />
    <Compile Include="FlowControls\Module\UlTrasonicMac.cs" />
    <Compile Include="FlowControls\Module\FilterDevice.cs" />
    <Compile Include="FlowControls\Module\AutoLiquidPump.cs" />
    <Compile Include="FlowControls\Module\WaterFlot.cs" />
    <Compile Include="FlowControls\Pipe\BendLeftDowns.cs" />
    <Compile Include="FlowControls\Pipe\BendLeftUps.cs" />
    <Compile Include="FlowControls\Pipe\BendRightDowns.cs" />
    <Compile Include="FlowControls\Pipe\BendRightUps.cs" />
    <Compile Include="FlowControls\Pipe\PeristalticValveUp.cs" />
    <Compile Include="FlowControls\Pipe\PeristalticValveDown.cs" />
    <Compile Include="FlowControls\Pipe\WaterPipeHorizontals.cs" />
    <Compile Include="FlowControls\Pipe\WaterPipeVerticals.cs" />
    <Compile Include="FlowControls\Pipe\PeristalticValveLeft.cs" />
    <Compile Include="FlowControls\Pipe\PeristalticValveRight.cs" />
    <Compile Include="FlowControls\Pipe\PipeVerticals.cs" />
    <Compile Include="FlowControls\Pipe\PipeHorizontals.cs" />
    <Compile Include="FlowControls\Pipe\SolenoidValveDown.cs" />
    <Compile Include="FlowControls\Pipe\SolenoidValveLeft.cs" />
    <Compile Include="FlowControls\Pipe\SolenoidValveRight.cs" />
    <Compile Include="FlowControls\Pipe\SolenoidValveUp.cs" />
    <Compile Include="FlowControls\Module\PiezometerVertical.cs" />
    <Compile Include="FlowControls\Module\PiezometerHorizontal.cs" />
    <Compile Include="FlowControls\Pipe\EscapeDirec.cs" />
    <Compile Include="FlowControls\Pipe\EscapeTrans.cs" />
    <Compile Include="FlowControls\System\LiquidLevelSwitch.cs" />
    <Compile Include="FlowControls\System\AlarmStateImages.cs" />
    <Compile Include="FlowControls\System\StateImages.cs" />
    <Compile Include="FlowControls\Pipe\TeeUp.cs" />
    <Compile Include="FlowControls\Pipe\TeeDown.cs" />
    <Compile Include="FlowControls\Pipe\TeeLeft.cs" />
    <Compile Include="FlowControls\Pipe\TeeRight.cs" />
    <Compile Include="FlowControls\System\SystemAlarmButton.cs" />
    <Compile Include="FlowControls\System\SystemFlowNotice.cs" />
    <Compile Include="FlowControls\System\SystemStopButtons.cs" />
    <Compile Include="FlowControls\System\ValueNodeText.cs" />
    <Compile Include="FlowControls\System\SystemAlarmState.cs" />
    <Compile Include="FlowControls\System\SystemState.cs" />
    <Compile Include="FlowControls\Module\SampleCell.cs" />
    <Compile Include="FlowControls\Module\OzoneGenerator.cs" />
    <Compile Include="FlowControls\Module\AirCompressor.cs" />
    <Compile Include="FlowControls\Module\WaterReservoir.cs" />
    <Compile Include="FlowControls\Module\FiveParamSlot.cs" />
    <Compile Include="FlowControls\System\LiquidLevelState.cs" />
    <Compile Include="FlowControls\System\TextRegion.cs" />
    <Compile Include="FlowControls\System\DefaultBackGround.cs" />
    <Compile Include="FlowControls\System\SystemRunMode.cs" />
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\Form\FrmCheckDataDetail.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\Form\FrmCheckDataDetail.Designer.cs">
      <DependentUpon>FrmCheckDataDetail.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\Form\FrmQueryEntranceRecordData.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\Form\FrmQueryEntranceRecordData.Designer.cs">
      <DependentUpon>FrmQueryEntranceRecordData.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\Form\FrmQueryPollutionOpLogData.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\Form\FrmQueryPollutionOpLogData.Designer.cs">
      <DependentUpon>FrmQueryPollutionOpLogData.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\Form\FrmQueryOpLogData.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\Form\FrmQueryOpLogData.Designer.cs">
      <DependentUpon>FrmQueryOpLogData.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\Form\FrmQuerySystemOpLogData.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\Form\FrmQuerySystemOpLogData.Designer.cs">
      <DependentUpon>FrmQuerySystemOpLogData.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\Form\FrmQueryCalibrateDate.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\Form\FrmQueryCalibrateDate.Designer.cs">
      <DependentUpon>FrmQueryCalibrateDate.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\Form\FrmQueryImagePatrolData.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\Form\FrmQueryImagePatrolData.Designer.cs">
      <DependentUpon>FrmQueryImagePatrolData.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\Form\FrmQuerySmartPatrolData.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\Form\FrmQuerySmartPatrolData.Designer.cs">
      <DependentUpon>FrmQuerySmartPatrolData.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\Form\FrmQuerySampleRecordData.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\Form\FrmQuerySampleRecordData.Designer.cs">
      <DependentUpon>FrmQuerySampleRecordData.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\Form\FrmQueryParallelData.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\Form\FrmQueryParallelData.Designer.cs">
      <DependentUpon>FrmQueryParallelData.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\Form\FrmQueryCheckData.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\Form\FrmQueryCheckData.Designer.cs">
      <DependentUpon>FrmQueryCheckData.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\Form\FrmQueryCycleData.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\Form\FrmQueryCycleData.Designer.cs">
      <DependentUpon>FrmQueryCycleData.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\Form\FrmQueryAddData.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\Form\FrmQueryAddData.Designer.cs">
      <DependentUpon>FrmQueryAddData.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\Form\FrmQueryMinutesData.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\Form\FrmQueryMinutesData.Designer.cs">
      <DependentUpon>FrmQueryMinutesData.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QueryArbitraryConCheckData.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QueryArbitraryConCheckData.Designer.cs">
      <DependentUpon>UC_QueryArbitraryConCheckData.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QueryBlankCheckData.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QueryBlankCheckData.Designer.cs">
      <DependentUpon>UC_QueryBlankCheckData.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QueryCalibrateDate.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QueryCalibrateDate.Designer.cs">
      <DependentUpon>UC_QueryCalibrateDate.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QueryEntranceRecordData.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QueryEntranceRecordData.Designer.cs">
      <DependentUpon>UC_QueryEntranceRecordData.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QueryImagePatrolData.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QueryImagePatrolData.Designer.cs">
      <DependentUpon>UC_QueryImagePatrolData.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QueryPollutionWaterCollectionData.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QueryPollutionWaterCollectionData.Designer.cs">
      <DependentUpon>UC_QueryPollutionWaterCollectionData.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QueryWaterPretreatmentData.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QueryWaterPretreatmentData.Designer.cs">
      <DependentUpon>UC_QueryWaterPretreatmentData.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QueryWaterDistributeData.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QueryWaterDistributeData.Designer.cs">
      <DependentUpon>UC_QueryWaterDistributeData.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QueryWaterCollectionData.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QueryWaterCollectionData.Designer.cs">
      <DependentUpon>UC_QueryWaterCollectionData.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QuerySystemOpLogData.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QuerySystemOpLogData.Designer.cs">
      <DependentUpon>UC_QuerySystemOpLogData.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QuerySmartPatrolData.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QuerySmartPatrolData.Designer.cs">
      <DependentUpon>UC_QuerySmartPatrolData.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QueryFiveParamSampleData.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QueryFiveParamSampleData.Designer.cs">
      <DependentUpon>UC_QueryFiveParamSampleData.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QueryMultiCheckDetailData.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QueryMultiCheckDetailData.Designer.cs">
      <DependentUpon>UC_QueryMultiCheckDetailData.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QueryMultiCheckData.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QueryMultiCheckData.Designer.cs">
      <DependentUpon>UC_QueryMultiCheckData.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QueryFiveParamCheckData.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QueryFiveParamCheckData.Designer.cs">
      <DependentUpon>UC_QueryFiveParamCheckData.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QuerySampleRecordData.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QuerySampleRecordData.Designer.cs">
      <DependentUpon>UC_QuerySampleRecordData.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QueryZeroCheckData.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QueryZeroCheckData.Designer.cs">
      <DependentUpon>UC_QueryZeroCheckData.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QueryBlindCheckData.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QueryBlindCheckData.Designer.cs">
      <DependentUpon>UC_QueryBlindCheckData.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QueryRangeCheckData.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QueryRangeCheckData.Designer.cs">
      <DependentUpon>UC_QueryRangeCheckData.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QuerySampleCheckData.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QuerySampleCheckData.Designer.cs">
      <DependentUpon>UC_QuerySampleCheckData.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QueryParallelData.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QueryParallelData.Designer.cs">
      <DependentUpon>UC_QueryParallelData.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QueryDataBase.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QueryDataBase.designer.cs">
      <DependentUpon>UC_QueryDataBase.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QueryCycleData.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QueryCycleData.Designer.cs">
      <DependentUpon>UC_QueryCycleData.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QueryAddData.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QueryAddData.Designer.cs">
      <DependentUpon>UC_QueryAddData.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QueryMinutesData.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\UC\UC_QueryMinutesData.Designer.cs">
      <DependentUpon>UC_QueryMinutesData.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\ExterDevices\ParamConfig\FrmAirVentilatorConfig.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\ExterDevices\ParamConfig\FrmAirVentilatorConfig.Designer.cs">
      <DependentUpon>FrmAirVentilatorConfig.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\ExterDevices\ParamConfig\FrmCameraSelectConfig.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\ExterDevices\ParamConfig\FrmCameraSelectConfig.Designer.cs">
      <DependentUpon>FrmCameraSelectConfig.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\ExterDevices\ParamConfig\FrmEntranceSelectConfig.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\ExterDevices\ParamConfig\FrmEntranceSelectConfig.Designer.cs">
      <DependentUpon>FrmEntranceSelectConfig.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\ExterDevices\ParamConfig\FrmPretreatmentConfig.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\ExterDevices\ParamConfig\FrmPretreatmentConfig.Designer.cs">
      <DependentUpon>FrmPretreatmentConfig.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\ExterDevices\ParamConfig\FrmAirConditioningConfig.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\ExterDevices\ParamConfig\FrmAirConditioningConfig.Designer.cs">
      <DependentUpon>FrmAirConditioningConfig.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\ExterDevices\ParamConfig\FrmDehumidifierConfig.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\ExterDevices\ParamConfig\FrmDehumidifierConfig.Designer.cs">
      <DependentUpon>FrmDehumidifierConfig.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\ExterDevices\ParamConfig\FrmDeviceSelectConfig.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\ExterDevices\ParamConfig\FrmDeviceSelectConfig.Designer.cs">
      <DependentUpon>FrmDeviceSelectConfig.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\ExterDevices\ParamConfig\FrmNodeAlarmConfig.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\ExterDevices\ParamConfig\FrmNodeAlarmConfig.Designer.cs">
      <DependentUpon>FrmNodeAlarmConfig.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\ExterDevices\ParamConfig\FrmStationEnvConfigInfo.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\ExterDevices\ParamConfig\FrmStationEnvConfigInfo.Designer.cs">
      <DependentUpon>FrmStationEnvConfigInfo.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\ExterDevices\ParamConfig\FrmDistributeConfig.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\ExterDevices\ParamConfig\FrmDistributeConfig.Designer.cs">
      <DependentUpon>FrmDistributeConfig.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\ExterDevices\ParamConfig\UC\UC_OneTimeSlotConfig.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\ExterDevices\ParamConfig\UC\UC_OneTimeSlotConfig.Designer.cs">
      <DependentUpon>UC_OneTimeSlotConfig.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\ExterDevices\ParamConfig\UC\UC_OneNodeAlarmLimitConfig.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\ExterDevices\ParamConfig\UC\UC_OneNodeAlarmLimitConfig.Designer.cs">
      <DependentUpon>UC_OneNodeAlarmLimitConfig.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\ExterDevices\ParamConfig\UC\UC_OneTurbTimeConfig.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\ExterDevices\ParamConfig\UC\UC_OneTurbTimeConfig.Designer.cs">
      <DependentUpon>UC_OneTurbTimeConfig.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\ExterDevices\ParamShow\FrmExterDeviceParam.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\ExterDevices\ParamShow\FrmExterDeviceParam.Designer.cs">
      <DependentUpon>FrmExterDeviceParam.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\ExterDevices\ParamConfig\FrmCollectionConfig.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\ExterDevices\ParamConfig\FrmCollectionConfig.Designer.cs">
      <DependentUpon>FrmCollectionConfig.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\ExterDevices\ParamShow\UC\UC_DoorBanModuleState.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\ExterDevices\ParamShow\UC\UC_DoorBanModuleState.Designer.cs">
      <DependentUpon>UC_DoorBanModuleState.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\ExterDevices\ParamShow\UC\UC_PretreatmentModuleState.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\ExterDevices\ParamShow\UC\UC_PretreatmentModuleState.Designer.cs">
      <DependentUpon>UC_PretreatmentModuleState.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\ExterDevices\ParamShow\UC\UC_StationModuleState.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\ExterDevices\ParamShow\UC\UC_StationModuleState.Designer.cs">
      <DependentUpon>UC_StationModuleState.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\ExterDevices\ParamShow\UC\UC_WaterCleanerState.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\ExterDevices\ParamShow\UC\UC_WaterCleanerState.Designer.cs">
      <DependentUpon>UC_WaterCleanerState.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\ExterDevices\ParamShow\UC\UC_WaterMeterState.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\ExterDevices\ParamShow\UC\UC_WaterMeterState.Designer.cs">
      <DependentUpon>UC_WaterMeterState.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\ExterDevices\ParamShow\UC\UC_VssModuleState.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\ExterDevices\ParamShow\UC\UC_VssModuleState.Designer.cs">
      <DependentUpon>UC_VssModuleState.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\ExterDevices\ParamShow\UC\UC_DistributeModuleState.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\ExterDevices\ParamShow\UC\UC_DistributeModuleState.Designer.cs">
      <DependentUpon>UC_DistributeModuleState.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\ExterDevices\ParamShow\UC\UC_CollectionModuleState.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\ExterDevices\ParamShow\UC\UC_CollectionModuleState.Designer.cs">
      <DependentUpon>UC_CollectionModuleState.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\LogListener\FrmCurrentLog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\LogListener\FrmCurrentLog.Designer.cs">
      <DependentUpon>FrmCurrentLog.cs</DependentUpon>
    </Compile>
    <Compile Include="MainForm\VirtualMainFlowForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MainForm\VirtualMainFlowForm.Designer.cs">
      <DependentUpon>VirtualMainFlowForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="ToolMenuForms\DataQuery\Curve\UC_RealDataCurve .cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\Curve\UC_RealDataCurve .Designer.cs">
      <DependentUpon>UC_RealDataCurve .cs</DependentUpon>
    </Compile>
    <Compile Include="MainForm\MainFlowForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MainForm\MainFlowForm.Designer.cs">
      <DependentUpon>MainFlowForm.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\Curve\UC_CycleDataCurve.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\DataQuery\Curve\UC_CycleDataCurve.Designer.cs">
      <DependentUpon>UC_CycleDataCurve.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\SystemOper\ModelSwitch\FrmSysModelSwitch.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\SystemOper\ModelSwitch\FrmSysModelSwitch.Designer.cs">
      <DependentUpon>FrmSysModelSwitch.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\SystemConfig\FrmConfigOperation.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\SystemConfig\FrmConfigOperation.designer.cs">
      <DependentUpon>FrmConfigOperation.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\SystemConfig\FrmLogoConfig.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\SystemConfig\FrmLogoConfig.Designer.cs">
      <DependentUpon>FrmLogoConfig.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\SystemConfig\FrmUserManager.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\SystemConfig\FrmUserManager.Designer.cs">
      <DependentUpon>FrmUserManager.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\SystemOper\ManualOperation\FrmManualOperation.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\SystemOper\ManualOperation\FrmManualOperation.Designer.cs">
      <DependentUpon>FrmManualOperation.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\SystemOper\PumpCtrl\FrmPumpCtrl.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\SystemOper\PumpCtrl\FrmPumpCtrl.Designer.cs">
      <DependentUpon>FrmPumpCtrl.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolMenuForms\SystemOper\PumpCtrl\UC\UC_SingleSwitch.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="ToolMenuForms\SystemOper\PumpCtrl\UC\UC_SingleSwitch.Designer.cs">
      <DependentUpon>UC_SingleSwitch.cs</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="ToolMenuForms\DataQuery\Form\FrmCheckDataDetail.resx">
      <DependentUpon>FrmCheckDataDetail.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\DataQuery\Form\FrmQueryEntranceRecordData.resx">
      <DependentUpon>FrmQueryEntranceRecordData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\DataQuery\Form\FrmQueryPollutionOpLogData.resx">
      <DependentUpon>FrmQueryPollutionOpLogData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\DataQuery\Form\FrmQueryOpLogData.resx">
      <DependentUpon>FrmQueryOpLogData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\DataQuery\Form\FrmQuerySystemOpLogData.resx">
      <DependentUpon>FrmQuerySystemOpLogData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\DataQuery\Form\FrmQueryCalibrateDate.resx">
      <DependentUpon>FrmQueryCalibrateDate.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\DataQuery\Form\FrmQueryImagePatrolData.resx">
      <DependentUpon>FrmQueryImagePatrolData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\DataQuery\Form\FrmQuerySmartPatrolData.resx">
      <DependentUpon>FrmQuerySmartPatrolData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\DataQuery\Form\FrmQuerySampleRecordData.resx">
      <DependentUpon>FrmQuerySampleRecordData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\DataQuery\Form\FrmQueryParallelData.resx">
      <DependentUpon>FrmQueryParallelData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\DataQuery\Form\FrmQueryCheckData.resx">
      <DependentUpon>FrmQueryCheckData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\DataQuery\Form\FrmQueryCycleData.resx">
      <DependentUpon>FrmQueryCycleData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\DataQuery\Form\FrmQueryAddData.resx">
      <DependentUpon>FrmQueryAddData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\DataQuery\Form\FrmQueryMinutesData.resx">
      <DependentUpon>FrmQueryMinutesData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\DataQuery\UC\UC_QueryArbitraryConCheckData.resx">
      <DependentUpon>UC_QueryArbitraryConCheckData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\DataQuery\UC\UC_QueryBlankCheckData.resx">
      <DependentUpon>UC_QueryBlankCheckData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\DataQuery\UC\UC_QueryCalibrateDate.resx">
      <DependentUpon>UC_QueryCalibrateDate.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\DataQuery\UC\UC_QueryEntranceRecordData.resx">
      <DependentUpon>UC_QueryEntranceRecordData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\DataQuery\UC\UC_QueryImagePatrolData.resx">
      <DependentUpon>UC_QueryImagePatrolData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\DataQuery\UC\UC_QueryPollutionWaterCollectionData.resx">
      <DependentUpon>UC_QueryPollutionWaterCollectionData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\DataQuery\UC\UC_QueryWaterPretreatmentData.resx">
      <DependentUpon>UC_QueryWaterPretreatmentData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\DataQuery\UC\UC_QueryWaterDistributeData.resx">
      <DependentUpon>UC_QueryWaterDistributeData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\DataQuery\UC\UC_QueryWaterCollectionData.resx">
      <DependentUpon>UC_QueryWaterCollectionData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\DataQuery\UC\UC_QuerySystemOpLogData.resx">
      <DependentUpon>UC_QuerySystemOpLogData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\DataQuery\UC\UC_QuerySmartPatrolData.resx">
      <DependentUpon>UC_QuerySmartPatrolData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\DataQuery\UC\UC_QueryFiveParamSampleData.resx">
      <DependentUpon>UC_QueryFiveParamSampleData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\DataQuery\UC\UC_QueryMultiCheckDetailData.resx">
      <DependentUpon>UC_QueryMultiCheckDetailData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\DataQuery\UC\UC_QueryMultiCheckData.resx">
      <DependentUpon>UC_QueryMultiCheckData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\DataQuery\UC\UC_QueryFiveParamCheckData.resx">
      <DependentUpon>UC_QueryFiveParamCheckData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\DataQuery\UC\UC_QuerySampleRecordData.resx">
      <DependentUpon>UC_QuerySampleRecordData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\DataQuery\UC\UC_QueryZeroCheckData.resx">
      <DependentUpon>UC_QueryZeroCheckData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\DataQuery\UC\UC_QueryBlindCheckData.resx">
      <DependentUpon>UC_QueryBlindCheckData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\DataQuery\UC\UC_QueryRangeCheckData.resx">
      <DependentUpon>UC_QueryRangeCheckData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\DataQuery\UC\UC_QuerySampleCheckData.resx">
      <DependentUpon>UC_QuerySampleCheckData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\DataQuery\UC\UC_QueryParallelData.resx">
      <DependentUpon>UC_QueryParallelData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\DataQuery\UC\UC_QueryDataBase.resx">
      <DependentUpon>UC_QueryDataBase.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\DataQuery\UC\UC_QueryCycleData.resx">
      <DependentUpon>UC_QueryCycleData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\DataQuery\UC\UC_QueryAddData.resx">
      <DependentUpon>UC_QueryAddData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\DataQuery\UC\UC_QueryMinutesData.resx">
      <DependentUpon>UC_QueryMinutesData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\ExterDevices\ParamConfig\FrmAirVentilatorConfig.resx">
      <DependentUpon>FrmAirVentilatorConfig.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\ExterDevices\ParamConfig\FrmCameraSelectConfig.resx">
      <DependentUpon>FrmCameraSelectConfig.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\ExterDevices\ParamConfig\FrmEntranceSelectConfig.resx">
      <DependentUpon>FrmEntranceSelectConfig.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\ExterDevices\ParamConfig\FrmPretreatmentConfig.resx">
      <DependentUpon>FrmPretreatmentConfig.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\ExterDevices\ParamConfig\FrmAirConditioningConfig.resx">
      <DependentUpon>FrmAirConditioningConfig.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\ExterDevices\ParamConfig\FrmDehumidifierConfig.resx">
      <DependentUpon>FrmDehumidifierConfig.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\ExterDevices\ParamConfig\FrmDeviceSelectConfig.resx">
      <DependentUpon>FrmDeviceSelectConfig.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\ExterDevices\ParamConfig\FrmNodeAlarmConfig.resx">
      <DependentUpon>FrmNodeAlarmConfig.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\ExterDevices\ParamConfig\FrmStationEnvConfigInfo.resx">
      <DependentUpon>FrmStationEnvConfigInfo.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\ExterDevices\ParamConfig\FrmDistributeConfig.resx">
      <DependentUpon>FrmDistributeConfig.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\ExterDevices\ParamConfig\UC\UC_OneTimeSlotConfig.resx">
      <DependentUpon>UC_OneTimeSlotConfig.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\ExterDevices\ParamConfig\UC\UC_OneNodeAlarmLimitConfig.resx">
      <DependentUpon>UC_OneNodeAlarmLimitConfig.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\ExterDevices\ParamConfig\UC\UC_OneTurbTimeConfig.resx">
      <DependentUpon>UC_OneTurbTimeConfig.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\ExterDevices\ParamShow\FrmExterDeviceParam.resx">
      <DependentUpon>FrmExterDeviceParam.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\ExterDevices\ParamConfig\FrmCollectionConfig.resx">
      <DependentUpon>FrmCollectionConfig.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\ExterDevices\ParamShow\UC\UC_DoorBanModuleState.resx">
      <DependentUpon>UC_DoorBanModuleState.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\ExterDevices\ParamShow\UC\UC_PretreatmentModuleState.resx">
      <DependentUpon>UC_PretreatmentModuleState.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\ExterDevices\ParamShow\UC\UC_StationModuleState.resx">
      <DependentUpon>UC_StationModuleState.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\ExterDevices\ParamShow\UC\UC_WaterCleanerState.resx">
      <DependentUpon>UC_WaterCleanerState.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\ExterDevices\ParamShow\UC\UC_WaterMeterState.resx">
      <DependentUpon>UC_WaterMeterState.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\ExterDevices\ParamShow\UC\UC_VssModuleState.resx">
      <DependentUpon>UC_VssModuleState.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\ExterDevices\ParamShow\UC\UC_DistributeModuleState.resx">
      <DependentUpon>UC_DistributeModuleState.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\ExterDevices\ParamShow\UC\UC_CollectionModuleState.resx">
      <DependentUpon>UC_CollectionModuleState.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\LogListener\FrmCurrentLog.en-US.resx">
      <DependentUpon>FrmCurrentLog.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\LogListener\FrmCurrentLog.resx">
      <DependentUpon>FrmCurrentLog.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="MainForm\VirtualMainFlowForm.resx">
      <DependentUpon>VirtualMainFlowForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\DataQuery\Curve\UC_RealDataCurve .resx">
      <DependentUpon>UC_RealDataCurve .cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="MainForm\MainFlowForm.resx">
      <DependentUpon>MainFlowForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\DataQuery\Curve\UC_CycleDataCurve.resx">
      <DependentUpon>UC_CycleDataCurve.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\SystemOper\ModelSwitch\FrmSysModelSwitch.resx">
      <DependentUpon>FrmSysModelSwitch.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\SystemConfig\FrmConfigOperation.en-US.resx">
      <DependentUpon>FrmConfigOperation.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\SystemConfig\FrmConfigOperation.resx">
      <DependentUpon>FrmConfigOperation.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\SystemConfig\FrmLogoConfig.resx">
      <DependentUpon>FrmLogoConfig.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\SystemConfig\FrmUserManager.resx">
      <DependentUpon>FrmUserManager.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\SystemOper\ManualOperation\FrmManualOperation.resx">
      <DependentUpon>FrmManualOperation.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\SystemOper\PumpCtrl\FrmPumpCtrl.resx">
      <DependentUpon>FrmPumpCtrl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ToolMenuForms\SystemOper\PumpCtrl\UC\UC_SingleSwitch.resx">
      <DependentUpon>UC_SingleSwitch.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Fpi.Alarm\Fpi.Alarm.csproj">
      <Project>{E714875C-0EC1-4C0F-8571-D0F631430C82}</Project>
      <Name>Fpi.Alarm</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Camera\Fpi.Camera.csproj">
      <Project>{1007e2b0-01aa-4bc4-9753-29e75615c1a0}</Project>
      <Name>Fpi.Camera</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Communication\Fpi.Communication.csproj">
      <Project>{D95F58B1-2E07-4D52-BA26-3F9B6EEACF29}</Project>
      <Name>Fpi.Communication</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Data\Fpi.Data.csproj">
      <Project>{07B7E9D5-5D00-4815-9409-0D7466A09F96}</Project>
      <Name>Fpi.Data</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.DB\Fpi.DB.csproj">
      <Project>{89D85957-BA9E-4BD9-99FE-7B73B6176A6F}</Project>
      <Name>Fpi.DB</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Device\Fpi.Device.csproj">
      <Project>{88FEF5D2-E039-4AC0-942B-442F23755978}</Project>
      <Name>Fpi.Device</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Entrance\Fpi.Entrance.csproj">
      <Project>{1A61D7D4-AEDD-487F-84D9-7AE75B3B1AEF}</Project>
      <Name>Fpi.Entrance</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.HB.Business\Fpi.HB.Business.csproj">
      <Project>{13650425-1448-4DF5-884F-B7CD466ECB24}</Project>
      <Name>Fpi.HB.Business</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.HB.UI\Fpi.HB.UI.csproj">
      <Project>{25031008-3C88-4D40-9172-F4FE61B1C993}</Project>
      <Name>Fpi.HB.UI</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Instrument\Fpi.Instrument.csproj">
      <Project>{E8D1EB85-2B23-4622-8CDE-80D5F850CC74}</Project>
      <Name>Fpi.Instrument</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Json\Fpi.Json.csproj">
      <Project>{958C97C1-360F-4434-9C37-6C6030EB5FCD}</Project>
      <Name>Fpi.Json</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Log\Fpi.Log.csproj">
      <Project>{C7C2425F-8926-43C6-996E-47205531C604}</Project>
      <Name>Fpi.Log</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Operation\Fpi.Operation.csproj">
      <Project>{1657672D-6FBA-47A5-8C40-0DA507D578F2}</Project>
      <Name>Fpi.Operation</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Timer\Fpi.Timer.csproj">
      <Project>{1DC3DD73-A4F5-4CA4-96D3-43712267C864}</Project>
      <Name>Fpi.Timer</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.UI.Common\Fpi.UI.Common.csproj">
      <Project>{c238e665-75b4-4eda-b574-a37f2794ba54}</Project>
      <Name>Fpi.UI.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.UI.FlowChart\Fpi.UI.FlowChart.csproj">
      <Project>{14FCA54C-BEA4-489F-8E5F-6BDFD9F13619}</Project>
      <Name>Fpi.UI.FlowChart</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.UI.PC\Fpi.UI.PC.csproj">
      <Project>{2d502016-b3b3-43ff-9bae-ad1d2a18d42e}</Project>
      <Name>Fpi.UI.PC</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.User\Fpi.User.csproj">
      <Project>{7D3E1D03-9F81-4B4A-B6B3-334D829DB1F4}</Project>
      <Name>Fpi.User</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Util\Fpi.Util.csproj">
      <Project>{6E37D7B3-8D08-4EF3-A924-3B87982AB246}</Project>
      <Name>Fpi.Util</Name>
    </ProjectReference>
    <ProjectReference Include="..\Fpi.Xml\Fpi.Xml.csproj">
      <Project>{3AF9654D-39EE-4BE9-8553-A9BB9B83A33B}</Project>
      <Name>Fpi.Xml</Name>
    </ProjectReference>
    <ProjectReference Include="..\WMS3900.DB\Fpi.WMS3900.DB.csproj">
      <Project>{4E6961D6-BA42-4CB1-89A7-C25557A2F82A}</Project>
      <Name>Fpi.WMS3900.DB</Name>
    </ProjectReference>
    <ProjectReference Include="..\WMS3900.Equipment\Fpi.WMS3900.Equipment.csproj">
      <Project>{63a37282-fea3-4f07-98f1-164045b58d8b}</Project>
      <Name>Fpi.WMS3900.Equipment</Name>
    </ProjectReference>
    <ProjectReference Include="..\WMS3900.RemoteProtocol\Fpi.WMS3900.Remote.csproj">
      <Project>{0e2971b7-6727-4fbb-9de0-3ce857591ded}</Project>
      <Name>Fpi.WMS3900.Remote</Name>
    </ProjectReference>
    <ProjectReference Include="..\WMS3900.SystemConfig\Fpi.WMS3900.SystemConfig.csproj">
      <Project>{74B5BD59-A90B-4F35-8F41-74A54A290940}</Project>
      <Name>Fpi.WMS3900.SystemConfig</Name>
    </ProjectReference>
    <ProjectReference Include="..\WMS3900.SystemOperation\Fpi.WMS3900.SystemOperation.csproj">
      <Project>{E9DEE184-5552-4E3A-90DF-EA517EC7A99A}</Project>
      <Name>Fpi.WMS3900.SystemOperation</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\red.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\管道_横.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\管道_纵.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\green.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\管道弯角_左上.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\管道弯角_右上.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\管道弯角_左下.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\管道弯角_右下.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\激活.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\未激活.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\清水池.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\五参数控制器.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\测量杯.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\沉沙池高.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\臭氧发生器.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\空压机.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\压力泵_上.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\压力泵_下.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\压力泵_右.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\压力泵_左.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\压力表_左.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\压力表_上.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\压力表_下.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\压力表_右.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\压力传感器_左.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\压力传感器_上.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\压力传感器_下.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\泵_左.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\泵_上.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\泵_下.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\泵_右.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\液位正常.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\液位异常.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\取水泵_上.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\取水泵_下.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\取水泵_右.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\取水泵_左.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\五参数槽.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\增压泵_纵.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\增压泵_横.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\文本区域.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\默认背景.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\储气瓶.png" />
    <None Include="Resources\UPS电源.png" />
    <None Include="Resources\imageAlarm.png" />
    <None Include="Resources\imageNoAlarm.png" />
    <None Include="Resources\仪表3.png" />
    <None Include="Resources\仪表1.png" />
    <None Include="Resources\仪表2.png" />
    <None Include="Resources\五参池低液位.png" />
    <None Include="Resources\三通阀左.png" />
    <None Include="Resources\三通阀上.png" />
    <None Include="Resources\三通阀下.png" />
    <None Include="Resources\三通阀右.png" />
    <None Include="Resources\三通上%28浅蓝%29.png" />
    <None Include="Resources\三通上%28深蓝%29.png" />
    <None Include="Resources\三通上.png" />
    <None Include="Resources\三通下%28浅蓝%29.png" />
    <None Include="Resources\三通下%28深蓝%29.png" />
    <None Include="Resources\三通下.png" />
    <None Include="Resources\三通右%28浅蓝%29.png" />
    <None Include="Resources\三通右%28深蓝%29.png" />
    <None Include="Resources\三通右.png" />
    <None Include="Resources\三通左%28浅蓝%29.png" />
    <None Include="Resources\三通左%28深蓝%29.png" />
    <None Include="Resources\三通左.png" />
    <None Include="Resources\UpgradeReport_Error.png" />
    <None Include="Resources\UpgradeReport_Information.png" />
    <None Include="Resources\UpgradeReport_Success.png" />
    <None Include="Resources\任务调试1.png" />
    <None Include="Resources\任务调试按钮.png" />
    <Content Include="Resources\加热器.png" />
    <None Include="Resources\压力表横.png" />
    <None Include="Resources\压力表竖.png" />
    <None Include="Resources\压力泵_上_红.png" />
    <None Include="Resources\压力泵_上_绿.png" />
    <None Include="Resources\压力泵_下_红.png" />
    <None Include="Resources\压力泵_下_绿.png" />
    <None Include="Resources\压力泵_右_红.png" />
    <None Include="Resources\压力泵_右_绿.png" />
    <None Include="Resources\压力泵_左_红.png" />
    <None Include="Resources\压力泵_左_绿.png" />
    <None Include="Resources\压力传感器_右.png" />
    <None Include="Resources\压力计.png" />
    <None Include="Resources\压力表.png" />
    <None Include="Resources\压力报警右.png" />
    <None Include="Resources\压力报警左.png" />
    <Content Include="Resources\双池沉沙池.png" />
    <None Include="Resources\吹扫腔.png" />
    <None Include="Resources\底座.png" />
    <None Include="Resources\取水点.png" />
    <None Include="Resources\增压泵_横_红.png" />
    <None Include="Resources\增压泵_横_绿.png" />
    <None Include="Resources\增压泵_纵_红.png" />
    <None Include="Resources\增压泵_纵_绿.png" />
    <Content Include="Resources\废液池.png" />
    <None Include="Resources\手动阀横关.png" />
    <None Include="Resources\手动阀横开.png" />
    <None Include="Resources\手动阀竖关.png" />
    <None Include="Resources\手动阀竖开.png" />
    <None Include="Resources\微型隔膜泵.png" />
    <None Include="Resources\微型隔膜泵_红.png" />
    <None Include="Resources\微型隔膜泵_绿.png" />
    <None Include="Resources\废液瓶.png" />
    <None Include="Resources\急停按钮.png" />
    <None Include="Resources\指示灯红色.png" />
    <Content Include="Resources\指示灯绿色.png" />
    <None Include="Resources\超声波匀化装置.png" />
    <None Include="Resources\自吸泵.png" />
    <None Include="Resources\橙色淡.png" />
    <None Include="Resources\橙色框.png" />
    <None Include="Resources\橙色浓.png" />
    <None Include="Resources\电磁阀_上关.png" />
    <None Include="Resources\电磁阀_上开.png" />
    <None Include="Resources\电磁阀_下关.png" />
    <None Include="Resources\电磁阀_下开.png" />
    <None Include="Resources\电磁阀_右关.png" />
    <None Include="Resources\电磁阀_右开.png" />
    <None Include="Resources\电磁阀_左关.png" />
    <None Include="Resources\电磁阀_左开.png" />
    <None Include="Resources\浮子式液位开关.png" />
    <None Include="Resources\浮子式液位开关_低.png" />
    <None Include="Resources\浮子式液位开关_高.png" />
    <None Include="Resources\浮子式液位开关_中.png" />
    <None Include="Resources\管道_红_横.png" />
    <None Include="Resources\管道_红_三通_上.png" />
    <None Include="Resources\管道_红_三通_下.png" />
    <None Include="Resources\管道_红_三通_右.png" />
    <None Include="Resources\管道_红_三通_左.png" />
    <None Include="Resources\管道_红_弯角_右上.png" />
    <None Include="Resources\管道_红_弯角_右下.png" />
    <None Include="Resources\管道_红_弯角_左上.png" />
    <None Include="Resources\管道_红_弯角_左下.png" />
    <None Include="Resources\管道_红_纵.png" />
    <None Include="Resources\管道_黄_横.png" />
    <None Include="Resources\管道_黄_三通_上.png" />
    <None Include="Resources\管道_黄_三通_下.png" />
    <None Include="Resources\管道_黄_三通_右.png" />
    <None Include="Resources\管道_黄_三通_左.png" />
    <None Include="Resources\管道_黄_弯角_右上.png" />
    <None Include="Resources\管道_黄_弯角_右下.png" />
    <None Include="Resources\管道_黄_弯角_左上.png" />
    <None Include="Resources\管道_黄_弯角_左下.png" />
    <None Include="Resources\管道_黄_纵.png" />
    <None Include="Resources\管道_金属_横.png" />
    <None Include="Resources\管道_金属_三通_上.png" />
    <None Include="Resources\管道_金属_三通_下.png" />
    <None Include="Resources\管道_金属_三通_右.png" />
    <None Include="Resources\管道_金属_三通_左.png" />
    <None Include="Resources\管道_金属_弯角_右上.png" />
    <None Include="Resources\管道_金属_弯角_右下.png" />
    <None Include="Resources\管道_金属_弯角_左上.png" />
    <None Include="Resources\管道_金属_弯角_左下.png" />
    <None Include="Resources\管道_金属_纵.png" />
    <None Include="Resources\管道_蓝_横.png" />
    <None Include="Resources\管道_蓝_三通_上.png" />
    <None Include="Resources\管道_蓝_三通_下.png" />
    <None Include="Resources\管道_蓝_三通_右.png" />
    <None Include="Resources\管道_蓝_三通_左.png" />
    <None Include="Resources\管道_蓝_弯角_右上.png" />
    <None Include="Resources\管道_蓝_弯角_右下.png" />
    <None Include="Resources\管道_蓝_弯角_左上.png" />
    <None Include="Resources\管道_蓝_弯角_左下.png" />
    <None Include="Resources\管道_蓝_纵.png" />
    <None Include="Resources\管道_绿_横.png" />
    <None Include="Resources\管道_绿_三通_上.png" />
    <None Include="Resources\管道_绿_三通_下.png" />
    <None Include="Resources\管道_绿_三通_右.png" />
    <None Include="Resources\管道_绿_三通_左.png" />
    <None Include="Resources\管道_绿_弯角_右上.png" />
    <None Include="Resources\管道_绿_弯角_右下.png" />
    <None Include="Resources\管道_绿_弯角_左上.png" />
    <None Include="Resources\管道_绿_弯角_左下.png" />
    <None Include="Resources\管道_绿_纵.png" />
    <None Include="Resources\管道_银_横.png" />
    <None Include="Resources\管道_银_三通_上.png" />
    <None Include="Resources\管道_银_三通_下.png" />
    <None Include="Resources\管道_银_三通_右.png" />
    <None Include="Resources\管道_银_三通_左.png" />
    <None Include="Resources\管道_银_弯角_右上.png" />
    <None Include="Resources\管道_银_弯角_右下.png" />
    <None Include="Resources\管道_银_弯角_左上.png" />
    <None Include="Resources\管道_银_弯角_左下.png" />
    <None Include="Resources\管道_银_纵.png" />
    <None Include="Resources\管道_紫_横.png" />
    <None Include="Resources\管道_紫_三通_上.png" />
    <None Include="Resources\管道_紫_三通_下.png" />
    <None Include="Resources\管道_紫_三通_右.png" />
    <None Include="Resources\管道_紫_三通_左.png" />
    <None Include="Resources\管道_紫_弯角_右上.png" />
    <None Include="Resources\管道_紫_弯角_右下.png" />
    <None Include="Resources\管道_紫_弯角_左上.png" />
    <None Include="Resources\管道_紫_弯角_左下.png" />
    <None Include="Resources\管道_紫_纵.png" />
    <None Include="Resources\蓝色淡.png" />
    <None Include="Resources\蓝色框.png" />
    <None Include="Resources\蓝色浓.png" />
    <None Include="Resources\蠕动泵1.png" />
    <None Include="Resources\蠕动泵2.png" />
    <None Include="Resources\蠕动泵3.png" />
    <None Include="Resources\蠕动泵4.png" />
    <None Include="Resources\紫色淡.png" />
    <None Include="Resources\紫色框.png" />
    <None Include="Resources\紫色浓.png" />
    <None Include="Resources\自吸泵_红.png" />
    <None Include="Resources\自吸泵_绿.png" />
    <None Include="Resources\质控单元.png" />
    <None Include="Resources\蠕动泵开右.png" />
    <None Include="Resources\蠕动泵关右.png" />
    <None Include="Resources\横线橙色.png" />
    <None Include="Resources\横线浅蓝色.png" />
    <None Include="Resources\横线深蓝色.png" />
    <None Include="Resources\竖线深蓝色.png" />
    <None Include="Resources\竖线橙色.png" />
    <None Include="Resources\竖线浅蓝色.png" />
    <None Include="Resources\蠕动泵开左.png" />
    <None Include="Resources\蠕动泵关左.png" />
    <None Include="Resources\蠕动泵开上.png" />
    <None Include="Resources\蠕动泵关上.png" />
    <None Include="Resources\蠕动泵开下.png" />
    <None Include="Resources\蠕动泵关下.png" />
    <None Include="Resources\浮球开关.png" />
    <None Include="Resources\浮球开关%28低%29左.png" />
    <None Include="Resources\浮球开关%28右%29.png" />
    <None Include="Resources\浮球开关%28低右%29.png" />
    <None Include="Resources\直角弯头右上%28橙色%29.png" />
    <None Include="Resources\直角弯头右上%28浅蓝%29.png" />
    <None Include="Resources\直角弯头右上.png" />
    <None Include="Resources\直角弯头右下%28橙色%29.png" />
    <None Include="Resources\直角弯头右下%28浅蓝%29.png" />
    <None Include="Resources\直角弯头右下.png" />
    <None Include="Resources\直角弯头左上%28橙色%29.png" />
    <None Include="Resources\直角弯头左上%28浅蓝%29.png" />
    <None Include="Resources\直角弯头左上.png" />
    <None Include="Resources\直角弯头左下%28橙色%29.png" />
    <None Include="Resources\直角弯头左下%28浅蓝%29.png" />
    <None Include="Resources\直角弯头左下.png" />
    <None Include="Resources\蠕动泵上.png" />
    <None Include="Resources\蠕动泵下.png" />
    <None Include="Resources\蠕动泵右.png" />
    <None Include="Resources\蠕动泵左.png" />
    <None Include="Resources\绿点.png" />
    <None Include="Resources\红点.png" />
    <None Include="Resources\质控单元低.png" />
    <None Include="Resources\按钮.png" />
    <None Include="Resources\数据查询 %286%291.png" />
    <None Include="Resources\模式 %281%29.png" />
    <None Include="Resources\模式 %282%29.png" />
    <None Include="Resources\模式 %283%29.png" />
    <None Include="Resources\泵阀 %285%29.png" />
    <None Include="Resources\泵阀 %286%29.png" />
    <None Include="Resources\泵阀2.png" />
    <None Include="Resources\系统报警.png" />
    <None Include="Resources\系统正常.png" />
    <None Include="Resources\沉沙池低.png" />
    <Content Include="Resources\转动1.png" />
    <Content Include="Resources\转动2.png" />
    <Content Include="Resources\转动3.png" />
    <None Include="Resources\过滤装置.png" />
    <None Include="Resources\采水泵_上关.png" />
    <None Include="Resources\采水泵_下关.png" />
    <None Include="Resources\采水泵_右关.png" />
    <None Include="Resources\采水泵_左关.png" />
    <None Include="Resources\风扇1.png" />
    <None Include="Resources\风扇2.png" />
    <None Include="Resources\风扇3.png" />
    <None Include="Resources\风扇4.png" />
    <None Include="Resources\风扇5.png" />
    <None Include="Resources\风扇6.png" />
    <None Include="Resources\风扇7.png" />
    <None Include="Resources\风扇8.png" />
    <None Include="Resources\风扇9.png" />
    <None Include="Resources\风扇10.png" />
    <None Include="Resources\风扇11.png" />
    <None Include="Resources\风扇12.png" />
    <None Include="Resources\采水池.png" />
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>