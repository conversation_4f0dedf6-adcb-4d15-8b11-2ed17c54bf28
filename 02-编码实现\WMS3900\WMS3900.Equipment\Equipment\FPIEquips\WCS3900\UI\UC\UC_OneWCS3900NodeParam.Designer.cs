﻿namespace Fpi.WMS3000.Equipment.UI
{
    partial class UC_OneWCS3900NodeParam
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if(disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.gbStateInfo = new Sunny.UI.UIGroupBox();
            this.uiPanel1 = new Sunny.UI.UIPanel();
            this.btnRefresh = new Sunny.UI.UISymbolButton();
            this.uiTableLayoutPanel1 = new Sunny.UI.UITableLayoutPanel();
            this.gbLevelState = new Sunny.UI.UIGroupBox();
            this.uC_ReagentInfo3 = new Fpi.WMS3000.Equipment.UI.UC_OneWMS3900ReagentInfo();
            this.uC_ReagentInfo2 = new Fpi.WMS3000.Equipment.UI.UC_OneWMS3900ReagentInfo();
            this.uC_ReagentInfo1 = new Fpi.WMS3000.Equipment.UI.UC_OneWMS3900ReagentInfo();
            this.gbElementControl = new Sunny.UI.UIGroupBox();
            this.pnlElementControl = new System.Windows.Forms.FlowLayoutPanel();
            this.uiGroupBox1 = new Sunny.UI.UIGroupBox();
            this.lblElectrodeLongstTime = new Sunny.UI.UILabel();
            this.lblElectrodeChangeTime = new Sunny.UI.UILabel();
            this.uiLabel2 = new Sunny.UI.UILabel();
            this.uiLabel1 = new Sunny.UI.UILabel();
            this.pnlLevelState = new System.Windows.Forms.FlowLayoutPanel();
            this.uiLine1 = new Sunny.UI.UILine();
            this.uiLine3 = new Sunny.UI.UILine();
            this.uiLabel6 = new Sunny.UI.UILabel();
            this.lblTotalStepCount = new Sunny.UI.UILabel();
            this.proBarFlow = new Sunny.UI.UIProcessBar();
            this.uiLabel3 = new Sunny.UI.UILabel();
            this.lblCurrentStep = new Sunny.UI.UILabel();
            this.uiLine2 = new Sunny.UI.UILine();
            this.gbStateInfo.SuspendLayout();
            this.uiPanel1.SuspendLayout();
            this.uiTableLayoutPanel1.SuspendLayout();
            this.gbLevelState.SuspendLayout();
            this.gbElementControl.SuspendLayout();
            this.uiGroupBox1.SuspendLayout();
            this.SuspendLayout();
            // 
            // gbStateInfo
            // 
            this.gbStateInfo.Controls.Add(this.uiPanel1);
            this.gbStateInfo.Controls.Add(this.btnRefresh);
            this.gbStateInfo.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gbStateInfo.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.gbStateInfo.Location = new System.Drawing.Point(3, 3);
            this.gbStateInfo.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.gbStateInfo.MinimumSize = new System.Drawing.Size(1, 1);
            this.gbStateInfo.Name = "gbStateInfo";
            this.gbStateInfo.Padding = new System.Windows.Forms.Padding(4, 32, 4, 4);
            this.gbStateInfo.Size = new System.Drawing.Size(1608, 848);
            this.gbStateInfo.TabIndex = 1;
            this.gbStateInfo.Text = "公共区信息";
            this.gbStateInfo.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // uiPanel1
            // 
            this.uiPanel1.Controls.Add(this.uiTableLayoutPanel1);
            this.uiPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uiPanel1.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiPanel1.Location = new System.Drawing.Point(4, 32);
            this.uiPanel1.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.uiPanel1.MinimumSize = new System.Drawing.Size(1, 1);
            this.uiPanel1.Name = "uiPanel1";
            this.uiPanel1.RectColor = System.Drawing.Color.Transparent;
            this.uiPanel1.Size = new System.Drawing.Size(1600, 812);
            this.uiPanel1.TabIndex = 27;
            this.uiPanel1.Text = null;
            this.uiPanel1.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // btnRefresh
            // 
            this.btnRefresh.BackColor = System.Drawing.Color.Transparent;
            this.btnRefresh.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnRefresh.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnRefresh.IsCircle = true;
            this.btnRefresh.Location = new System.Drawing.Point(191, -1);
            this.btnRefresh.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnRefresh.Name = "btnRefresh";
            this.btnRefresh.Size = new System.Drawing.Size(35, 35);
            this.btnRefresh.Symbol = 61473;
            this.btnRefresh.TabIndex = 0;
            this.btnRefresh.TipsFont = new System.Drawing.Font("微软雅黑", 11F);
            this.btnRefresh.TipsText = "刷新";
            this.btnRefresh.Click += new System.EventHandler(this.btnRefresh_Click);
            // 
            // uiTableLayoutPanel1
            // 
            this.uiTableLayoutPanel1.BackColor = System.Drawing.Color.Transparent;
            this.uiTableLayoutPanel1.ColumnCount = 3;
            this.uiTableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 32F));
            this.uiTableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 34F));
            this.uiTableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 34F));
            this.uiTableLayoutPanel1.Controls.Add(this.uiGroupBox1, 0, 0);
            this.uiTableLayoutPanel1.Controls.Add(this.gbLevelState, 1, 0);
            this.uiTableLayoutPanel1.Controls.Add(this.gbElementControl, 2, 0);
            this.uiTableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uiTableLayoutPanel1.Location = new System.Drawing.Point(0, 0);
            this.uiTableLayoutPanel1.Name = "uiTableLayoutPanel1";
            this.uiTableLayoutPanel1.RowCount = 1;
            this.uiTableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.uiTableLayoutPanel1.Size = new System.Drawing.Size(1600, 812);
            this.uiTableLayoutPanel1.TabIndex = 29;
            this.uiTableLayoutPanel1.TagString = null;
            // 
            // gbLevelState
            // 
            this.gbLevelState.Controls.Add(this.uC_ReagentInfo3);
            this.gbLevelState.Controls.Add(this.uC_ReagentInfo2);
            this.gbLevelState.Controls.Add(this.uC_ReagentInfo1);
            this.gbLevelState.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gbLevelState.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.gbLevelState.Location = new System.Drawing.Point(518, 5);
            this.gbLevelState.Margin = new System.Windows.Forms.Padding(6, 5, 6, 5);
            this.gbLevelState.MinimumSize = new System.Drawing.Size(1, 1);
            this.gbLevelState.Name = "gbLevelState";
            this.gbLevelState.Padding = new System.Windows.Forms.Padding(1, 32, 1, 1);
            this.gbLevelState.Size = new System.Drawing.Size(532, 802);
            this.gbLevelState.TabIndex = 33;
            this.gbLevelState.Text = "试剂信息";
            this.gbLevelState.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // uC_ReagentInfo3
            // 
            this.uC_ReagentInfo3.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uC_ReagentInfo3.Location = new System.Drawing.Point(53, 566);
            this.uC_ReagentInfo3.MinimumSize = new System.Drawing.Size(1, 1);
            this.uC_ReagentInfo3.Name = "uC_ReagentInfo3";
            this.uC_ReagentInfo3.Size = new System.Drawing.Size(430, 222);
            this.uC_ReagentInfo3.TabIndex = 2;
            this.uC_ReagentInfo3.Text = "uC_OneWMS3900ReagentInfo3";
            this.uC_ReagentInfo3.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // uC_ReagentInfo2
            // 
            this.uC_ReagentInfo2.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uC_ReagentInfo2.Location = new System.Drawing.Point(53, 302);
            this.uC_ReagentInfo2.MinimumSize = new System.Drawing.Size(1, 1);
            this.uC_ReagentInfo2.Name = "uC_ReagentInfo2";
            this.uC_ReagentInfo2.Size = new System.Drawing.Size(430, 222);
            this.uC_ReagentInfo2.TabIndex = 1;
            this.uC_ReagentInfo2.Text = "uC_OneWMS3900ReagentInfo2";
            this.uC_ReagentInfo2.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // uC_ReagentInfo1
            // 
            this.uC_ReagentInfo1.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uC_ReagentInfo1.Location = new System.Drawing.Point(53, 38);
            this.uC_ReagentInfo1.MinimumSize = new System.Drawing.Size(1, 1);
            this.uC_ReagentInfo1.Name = "uC_ReagentInfo1";
            this.uC_ReagentInfo1.Size = new System.Drawing.Size(430, 222);
            this.uC_ReagentInfo1.TabIndex = 0;
            this.uC_ReagentInfo1.Text = null;
            this.uC_ReagentInfo1.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // gbElementControl
            // 
            this.gbElementControl.Controls.Add(this.pnlElementControl);
            this.gbElementControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gbElementControl.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.gbElementControl.Location = new System.Drawing.Point(1062, 5);
            this.gbElementControl.Margin = new System.Windows.Forms.Padding(6, 5, 6, 5);
            this.gbElementControl.MinimumSize = new System.Drawing.Size(1, 1);
            this.gbElementControl.Name = "gbElementControl";
            this.gbElementControl.Padding = new System.Windows.Forms.Padding(2, 32, 2, 1);
            this.gbElementControl.Size = new System.Drawing.Size(532, 802);
            this.gbElementControl.TabIndex = 34;
            this.gbElementControl.Text = "器件信息";
            this.gbElementControl.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // pnlElementControl
            // 
            this.pnlElementControl.AutoScroll = true;
            this.pnlElementControl.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(243)))), ((int)(((byte)(249)))), ((int)(((byte)(255)))));
            this.pnlElementControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pnlElementControl.FlowDirection = System.Windows.Forms.FlowDirection.TopDown;
            this.pnlElementControl.Location = new System.Drawing.Point(2, 32);
            this.pnlElementControl.Name = "pnlElementControl";
            this.pnlElementControl.Padding = new System.Windows.Forms.Padding(4, 1, 1, 1);
            this.pnlElementControl.Size = new System.Drawing.Size(528, 769);
            this.pnlElementControl.TabIndex = 0;
            this.pnlElementControl.WrapContents = false;
            // 
            // uiGroupBox1
            // 
            this.uiGroupBox1.Controls.Add(this.lblElectrodeLongstTime);
            this.uiGroupBox1.Controls.Add(this.lblElectrodeChangeTime);
            this.uiGroupBox1.Controls.Add(this.uiLabel2);
            this.uiGroupBox1.Controls.Add(this.uiLabel1);
            this.uiGroupBox1.Controls.Add(this.pnlLevelState);
            this.uiGroupBox1.Controls.Add(this.uiLine1);
            this.uiGroupBox1.Controls.Add(this.uiLine3);
            this.uiGroupBox1.Controls.Add(this.uiLabel6);
            this.uiGroupBox1.Controls.Add(this.lblTotalStepCount);
            this.uiGroupBox1.Controls.Add(this.proBarFlow);
            this.uiGroupBox1.Controls.Add(this.uiLabel3);
            this.uiGroupBox1.Controls.Add(this.lblCurrentStep);
            this.uiGroupBox1.Controls.Add(this.uiLine2);
            this.uiGroupBox1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uiGroupBox1.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiGroupBox1.Location = new System.Drawing.Point(6, 5);
            this.uiGroupBox1.Margin = new System.Windows.Forms.Padding(6, 5, 6, 5);
            this.uiGroupBox1.MinimumSize = new System.Drawing.Size(1, 1);
            this.uiGroupBox1.Name = "uiGroupBox1";
            this.uiGroupBox1.Padding = new System.Windows.Forms.Padding(0, 32, 33, 0);
            this.uiGroupBox1.Size = new System.Drawing.Size(500, 802);
            this.uiGroupBox1.TabIndex = 35;
            this.uiGroupBox1.Text = "基本信息";
            this.uiGroupBox1.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // lblElectrodeLongstTime
            // 
            this.lblElectrodeLongstTime.AutoSize = true;
            this.lblElectrodeLongstTime.BackColor = System.Drawing.Color.Transparent;
            this.lblElectrodeLongstTime.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblElectrodeLongstTime.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.lblElectrodeLongstTime.Location = new System.Drawing.Point(273, 449);
            this.lblElectrodeLongstTime.Name = "lblElectrodeLongstTime";
            this.lblElectrodeLongstTime.Size = new System.Drawing.Size(115, 21);
            this.lblElectrodeLongstTime.TabIndex = 17;
            this.lblElectrodeLongstTime.Text = "— — — — —";
            // 
            // lblElectrodeChangeTime
            // 
            this.lblElectrodeChangeTime.AutoSize = true;
            this.lblElectrodeChangeTime.BackColor = System.Drawing.Color.Transparent;
            this.lblElectrodeChangeTime.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblElectrodeChangeTime.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.lblElectrodeChangeTime.Location = new System.Drawing.Point(273, 380);
            this.lblElectrodeChangeTime.Name = "lblElectrodeChangeTime";
            this.lblElectrodeChangeTime.Size = new System.Drawing.Size(115, 21);
            this.lblElectrodeChangeTime.TabIndex = 16;
            this.lblElectrodeChangeTime.Text = "— — — — —";
            // 
            // uiLabel2
            // 
            this.uiLabel2.AutoSize = true;
            this.uiLabel2.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel2.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel2.Location = new System.Drawing.Point(96, 449);
            this.uiLabel2.Name = "uiLabel2";
            this.uiLabel2.Size = new System.Drawing.Size(138, 21);
            this.uiLabel2.TabIndex = 15;
            this.uiLabel2.Text = "电极最久可用时间";
            // 
            // uiLabel1
            // 
            this.uiLabel1.AutoSize = true;
            this.uiLabel1.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel1.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel1.Location = new System.Drawing.Point(128, 380);
            this.uiLabel1.Name = "uiLabel1";
            this.uiLabel1.Size = new System.Drawing.Size(106, 21);
            this.uiLabel1.TabIndex = 14;
            this.uiLabel1.Text = "电极更换时间";
            // 
            // pnlLevelState
            // 
            this.pnlLevelState.AutoScroll = true;
            this.pnlLevelState.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(243)))), ((int)(((byte)(249)))), ((int)(((byte)(255)))));
            this.pnlLevelState.FlowDirection = System.Windows.Forms.FlowDirection.TopDown;
            this.pnlLevelState.Location = new System.Drawing.Point(9, 581);
            this.pnlLevelState.Name = "pnlLevelState";
            this.pnlLevelState.Padding = new System.Windows.Forms.Padding(4, 1, 1, 1);
            this.pnlLevelState.Size = new System.Drawing.Size(483, 215);
            this.pnlLevelState.TabIndex = 13;
            this.pnlLevelState.WrapContents = false;
            // 
            // uiLine1
            // 
            this.uiLine1.BackColor = System.Drawing.Color.Transparent;
            this.uiLine1.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLine1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLine1.Location = new System.Drawing.Point(1, 534);
            this.uiLine1.MinimumSize = new System.Drawing.Size(1, 1);
            this.uiLine1.Name = "uiLine1";
            this.uiLine1.Size = new System.Drawing.Size(499, 29);
            this.uiLine1.TabIndex = 12;
            this.uiLine1.Text = "液位状态";
            // 
            // uiLine3
            // 
            this.uiLine3.BackColor = System.Drawing.Color.Transparent;
            this.uiLine3.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLine3.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLine3.Location = new System.Drawing.Point(1, 301);
            this.uiLine3.MinimumSize = new System.Drawing.Size(1, 1);
            this.uiLine3.Name = "uiLine3";
            this.uiLine3.Size = new System.Drawing.Size(499, 29);
            this.uiLine3.TabIndex = 11;
            this.uiLine3.Text = "电极已使用天数";
            // 
            // uiLabel6
            // 
            this.uiLabel6.AutoSize = true;
            this.uiLabel6.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel6.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel6.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel6.Location = new System.Drawing.Point(128, 108);
            this.uiLabel6.Name = "uiLabel6";
            this.uiLabel6.Size = new System.Drawing.Size(106, 21);
            this.uiLabel6.TabIndex = 6;
            this.uiLabel6.Text = "流程步骤总数";
            // 
            // lblTotalStepCount
            // 
            this.lblTotalStepCount.AutoSize = true;
            this.lblTotalStepCount.BackColor = System.Drawing.Color.Transparent;
            this.lblTotalStepCount.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblTotalStepCount.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.lblTotalStepCount.Location = new System.Drawing.Point(273, 108);
            this.lblTotalStepCount.Name = "lblTotalStepCount";
            this.lblTotalStepCount.Size = new System.Drawing.Size(115, 21);
            this.lblTotalStepCount.TabIndex = 7;
            this.lblTotalStepCount.Text = "— — — — —";
            // 
            // proBarFlow
            // 
            this.proBarFlow.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(243)))), ((int)(((byte)(255)))));
            this.proBarFlow.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.proBarFlow.Location = new System.Drawing.Point(37, 231);
            this.proBarFlow.MinimumSize = new System.Drawing.Size(3, 3);
            this.proBarFlow.Name = "proBarFlow";
            this.proBarFlow.Size = new System.Drawing.Size(424, 29);
            this.proBarFlow.TabIndex = 10;
            this.proBarFlow.Text = "uiProcessBar1";
            // 
            // uiLabel3
            // 
            this.uiLabel3.AutoSize = true;
            this.uiLabel3.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel3.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel3.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel3.Location = new System.Drawing.Point(144, 166);
            this.uiLabel3.Name = "uiLabel3";
            this.uiLabel3.Size = new System.Drawing.Size(90, 21);
            this.uiLabel3.TabIndex = 8;
            this.uiLabel3.Text = "当前步骤数";
            // 
            // lblCurrentStep
            // 
            this.lblCurrentStep.AutoSize = true;
            this.lblCurrentStep.BackColor = System.Drawing.Color.Transparent;
            this.lblCurrentStep.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblCurrentStep.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.lblCurrentStep.Location = new System.Drawing.Point(273, 166);
            this.lblCurrentStep.Name = "lblCurrentStep";
            this.lblCurrentStep.Size = new System.Drawing.Size(115, 21);
            this.lblCurrentStep.TabIndex = 9;
            this.lblCurrentStep.Text = "— — — — —";
            // 
            // uiLine2
            // 
            this.uiLine2.BackColor = System.Drawing.Color.Transparent;
            this.uiLine2.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLine2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLine2.Location = new System.Drawing.Point(1, 47);
            this.uiLine2.MinimumSize = new System.Drawing.Size(1, 1);
            this.uiLine2.Name = "uiLine2";
            this.uiLine2.Size = new System.Drawing.Size(499, 29);
            this.uiLine2.TabIndex = 3;
            this.uiLine2.Text = "流程进度";
            // 
            // UC_OneWCS3900NodeParam
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.Controls.Add(this.gbStateInfo);
            this.Name = "UC_OneWCS3900NodeParam";
            this.Padding = new System.Windows.Forms.Padding(3);
            this.Size = new System.Drawing.Size(1614, 854);
            this.gbStateInfo.ResumeLayout(false);
            this.uiPanel1.ResumeLayout(false);
            this.uiTableLayoutPanel1.ResumeLayout(false);
            this.gbLevelState.ResumeLayout(false);
            this.gbElementControl.ResumeLayout(false);
            this.uiGroupBox1.ResumeLayout(false);
            this.uiGroupBox1.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private Sunny.UI.UIGroupBox gbStateInfo;
        private Sunny.UI.UISymbolButton btnRefresh;
        private Sunny.UI.UIPanel uiPanel1;
        private Sunny.UI.UITableLayoutPanel uiTableLayoutPanel1;
        private Sunny.UI.UIGroupBox gbElementControl;
        private System.Windows.Forms.FlowLayoutPanel pnlElementControl;
        private Sunny.UI.UIGroupBox gbLevelState;
        private UC_OneWMS3900ReagentInfo uC_ReagentInfo3;
        private UC_OneWMS3900ReagentInfo uC_ReagentInfo2;
        private UC_OneWMS3900ReagentInfo uC_ReagentInfo1;
        private Sunny.UI.UIGroupBox uiGroupBox1;
        private Sunny.UI.UILabel lblElectrodeLongstTime;
        private Sunny.UI.UILabel lblElectrodeChangeTime;
        private Sunny.UI.UILabel uiLabel2;
        private Sunny.UI.UILabel uiLabel1;
        private System.Windows.Forms.FlowLayoutPanel pnlLevelState;
        private Sunny.UI.UILine uiLine1;
        private Sunny.UI.UILine uiLine3;
        private Sunny.UI.UILabel uiLabel6;
        private Sunny.UI.UILabel lblTotalStepCount;
        private Sunny.UI.UIProcessBar proBarFlow;
        private Sunny.UI.UILabel uiLabel3;
        private Sunny.UI.UILabel lblCurrentStep;
        private Sunny.UI.UILine uiLine2;
    }
}
