﻿namespace Fpi.WMS3000.Equipment.UI
{
    partial class UC_SIA3900Param
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.tabMain = new Sunny.UI.UITabControl();
            this.tbpState = new System.Windows.Forms.TabPage();
            this.uc_SIA3900StateAlarm = new Fpi.WMS3000.Equipment.UI.UC_DeviceParamData();
            this.uc_SIA3900DeviceState = new Fpi.WMS3000.Equipment.UI.UC_SIA3900DeviceState();
            this.tabParam = new System.Windows.Forms.TabPage();
            this.uc_SIA3900DeviceKeyParams = new Fpi.WMS3000.Equipment.UI.UC_DeviceParamData();
            this.uc_SIA3900MeasureParam = new Fpi.WMS3000.Equipment.UI.UC_DeviceParamData();
            this.tbpMaintain = new System.Windows.Forms.TabPage();
            this.uc_SIA3900DeviceParamsSet = new Fpi.WMS3000.Equipment.UI.UC_SIA3900DeviceParamsSet();
            this.tabOper = new System.Windows.Forms.TabPage();
            this.uc_SIA3900DeviceOperControl = new Fpi.WMS3000.Equipment.UI.UC_SIA3900DeviceOperControl();
            this.tabCoefficient = new System.Windows.Forms.TabPage();
            this.uc_SIA3900CalibrateCoefficientDetail = new Fpi.WMS3000.Equipment.UI.UC_SIA3900CalibrateCoefficientDetail();
            this.tabReagent = new System.Windows.Forms.TabPage();
            this.uc_SIA3900ReagentInfo = new Fpi.WMS3000.Equipment.UI.UC_SIA3900ReagentInfo();
            this.tabDeviceData = new System.Windows.Forms.TabPage();
            this.uc_SIA3900CurrentLog = new Fpi.WMS3000.Equipment.UI.UC_DeviceCurrentLogQuery();
            this.uc_SIA3900HistoryDate = new Fpi.WMS3000.Equipment.UI.UC_DeviceParamData();
            this.tabAlarm = new System.Windows.Forms.TabPage();
            this.uc_DeviceAllAlarm = new Fpi.WMS3000.Equipment.Common.UI.UC_DeviceAllAlarm();
            this.tabLogQuery = new System.Windows.Forms.TabPage();
            this.uc_SIA3900HistoryLog = new Fpi.WMS3000.Equipment.UI.UC_DeviceHistoryLogQuery();
            this.tabLife = new System.Windows.Forms.TabPage();
            this.uc_DeviceLifeMonitor = new Fpi.WMS3000.Equipment.UI.UC_SIA3900DeviceLifeMonitor();
            this.tabDianosis = new System.Windows.Forms.TabPage();
            this.uc_DiagnosisRecords = new Fpi.WMS3000.Equipment.UI.UC_SIA3900DiagnosisRecords();
            this.tabMain.SuspendLayout();
            this.tbpState.SuspendLayout();
            this.tabParam.SuspendLayout();
            this.tbpMaintain.SuspendLayout();
            this.tabOper.SuspendLayout();
            this.tabCoefficient.SuspendLayout();
            this.tabReagent.SuspendLayout();
            this.tabDeviceData.SuspendLayout();
            this.tabAlarm.SuspendLayout();
            this.tabLogQuery.SuspendLayout();
            this.tabLife.SuspendLayout();
            this.tabDianosis.SuspendLayout();
            this.SuspendLayout();
            // 
            // tabMain
            // 
            this.tabMain.Controls.Add(this.tbpState);
            this.tabMain.Controls.Add(this.tabParam);
            this.tabMain.Controls.Add(this.tbpMaintain);
            this.tabMain.Controls.Add(this.tabOper);
            this.tabMain.Controls.Add(this.tabCoefficient);
            this.tabMain.Controls.Add(this.tabReagent);
            this.tabMain.Controls.Add(this.tabDeviceData);
            this.tabMain.Controls.Add(this.tabAlarm);
            this.tabMain.Controls.Add(this.tabLogQuery);
            this.tabMain.Controls.Add(this.tabLife);
            this.tabMain.Controls.Add(this.tabDianosis);
            this.tabMain.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabMain.DrawMode = System.Windows.Forms.TabDrawMode.OwnerDrawFixed;
            this.tabMain.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.tabMain.ItemSize = new System.Drawing.Size(150, 40);
            this.tabMain.Location = new System.Drawing.Point(0, 0);
            this.tabMain.MainPage = "";
            this.tabMain.MenuStyle = Sunny.UI.UIMenuStyle.White;
            this.tabMain.Name = "tabMain";
            this.tabMain.RightToLeft = System.Windows.Forms.RightToLeft.No;
            this.tabMain.SelectedIndex = 0;
            this.tabMain.Size = new System.Drawing.Size(1616, 936);
            this.tabMain.SizeMode = System.Windows.Forms.TabSizeMode.Fixed;
            this.tabMain.TabBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(240)))), ((int)(((byte)(240)))));
            this.tabMain.TabIndex = 1;
            this.tabMain.TabPageTextAlignment = System.Windows.Forms.HorizontalAlignment.Center;
            this.tabMain.TabSelectedColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(250)))), ((int)(((byte)(250)))));
            this.tabMain.TabSelectedHighColorSize = 0;
            this.tabMain.TabUnSelectedColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(240)))), ((int)(((byte)(240)))));
            this.tabMain.TabUnSelectedForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.tabMain.TipsFont = new System.Drawing.Font("微软雅黑", 12F);
            // 
            // tbpState
            // 
            this.tbpState.AutoScroll = true;
            this.tbpState.Controls.Add(this.uc_SIA3900StateAlarm);
            this.tbpState.Controls.Add(this.uc_SIA3900DeviceState);
            this.tbpState.Location = new System.Drawing.Point(0, 40);
            this.tbpState.Name = "tbpState";
            this.tbpState.Size = new System.Drawing.Size(1616, 896);
            this.tbpState.TabIndex = 0;
            this.tbpState.Text = "系统状态";
            this.tbpState.UseVisualStyleBackColor = true;
            // 
            // uc_SIA3900StateAlarm
            // 
            this.uc_SIA3900StateAlarm.CanRefresh = true;
            this.uc_SIA3900StateAlarm.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uc_SIA3900StateAlarm.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uc_SIA3900StateAlarm.Location = new System.Drawing.Point(800, 0);
            this.uc_SIA3900StateAlarm.MinimumSize = new System.Drawing.Size(1, 1);
            this.uc_SIA3900StateAlarm.Name = "uc_SIA3900StateAlarm";
            this.uc_SIA3900StateAlarm.Padding = new System.Windows.Forms.Padding(1);
            this.uc_SIA3900StateAlarm.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.uc_SIA3900StateAlarm.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.uc_SIA3900StateAlarm.Size = new System.Drawing.Size(816, 896);
            this.uc_SIA3900StateAlarm.TabIndex = 3;
            this.uc_SIA3900StateAlarm.Text = "状态告警";
            this.uc_SIA3900StateAlarm.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // uc_SIA3900DeviceState
            // 
            this.uc_SIA3900DeviceState.Dock = System.Windows.Forms.DockStyle.Left;
            this.uc_SIA3900DeviceState.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uc_SIA3900DeviceState.Location = new System.Drawing.Point(0, 0);
            this.uc_SIA3900DeviceState.MinimumSize = new System.Drawing.Size(1, 1);
            this.uc_SIA3900DeviceState.Name = "uc_SIA3900DeviceState";
            this.uc_SIA3900DeviceState.Padding = new System.Windows.Forms.Padding(1);
            this.uc_SIA3900DeviceState.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.uc_SIA3900DeviceState.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.uc_SIA3900DeviceState.Size = new System.Drawing.Size(800, 896);
            this.uc_SIA3900DeviceState.TabIndex = 0;
            this.uc_SIA3900DeviceState.Text = null;
            this.uc_SIA3900DeviceState.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // tabParam
            // 
            this.tabParam.Controls.Add(this.uc_SIA3900DeviceKeyParams);
            this.tabParam.Controls.Add(this.uc_SIA3900MeasureParam);
            this.tabParam.Location = new System.Drawing.Point(0, 40);
            this.tabParam.Name = "tabParam";
            this.tabParam.Size = new System.Drawing.Size(200, 60);
            this.tabParam.TabIndex = 3;
            this.tabParam.Text = "关键参数";
            this.tabParam.UseVisualStyleBackColor = true;
            // 
            // uc_SIA3900DeviceKeyParams
            // 
            this.uc_SIA3900DeviceKeyParams.CanRefresh = true;
            this.uc_SIA3900DeviceKeyParams.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uc_SIA3900DeviceKeyParams.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uc_SIA3900DeviceKeyParams.Location = new System.Drawing.Point(808, 0);
            this.uc_SIA3900DeviceKeyParams.MinimumSize = new System.Drawing.Size(1, 1);
            this.uc_SIA3900DeviceKeyParams.Name = "uc_SIA3900DeviceKeyParams";
            this.uc_SIA3900DeviceKeyParams.Padding = new System.Windows.Forms.Padding(1);
            this.uc_SIA3900DeviceKeyParams.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.uc_SIA3900DeviceKeyParams.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.uc_SIA3900DeviceKeyParams.Size = new System.Drawing.Size(1, 60);
            this.uc_SIA3900DeviceKeyParams.TabIndex = 4;
            this.uc_SIA3900DeviceKeyParams.Text = "关键参数";
            this.uc_SIA3900DeviceKeyParams.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // uc_SIA3900MeasureParam
            // 
            this.uc_SIA3900MeasureParam.CanRefresh = true;
            this.uc_SIA3900MeasureParam.Dock = System.Windows.Forms.DockStyle.Left;
            this.uc_SIA3900MeasureParam.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uc_SIA3900MeasureParam.Location = new System.Drawing.Point(0, 0);
            this.uc_SIA3900MeasureParam.MinimumSize = new System.Drawing.Size(1, 1);
            this.uc_SIA3900MeasureParam.Name = "uc_SIA3900MeasureParam";
            this.uc_SIA3900MeasureParam.Padding = new System.Windows.Forms.Padding(1);
            this.uc_SIA3900MeasureParam.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.uc_SIA3900MeasureParam.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.uc_SIA3900MeasureParam.Size = new System.Drawing.Size(808, 60);
            this.uc_SIA3900MeasureParam.TabIndex = 4;
            this.uc_SIA3900MeasureParam.Text = "测量相关参数";
            this.uc_SIA3900MeasureParam.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // tbpMaintain
            // 
            this.tbpMaintain.AutoScroll = true;
            this.tbpMaintain.Controls.Add(this.uc_SIA3900DeviceParamsSet);
            this.tbpMaintain.Location = new System.Drawing.Point(0, 40);
            this.tbpMaintain.Name = "tbpMaintain";
            this.tbpMaintain.Size = new System.Drawing.Size(200, 60);
            this.tbpMaintain.TabIndex = 2;
            this.tbpMaintain.Text = "设备参数设置";
            this.tbpMaintain.UseVisualStyleBackColor = true;
            // 
            // uc_SIA3900DeviceParamsSet
            // 
            this.uc_SIA3900DeviceParamsSet.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uc_SIA3900DeviceParamsSet.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uc_SIA3900DeviceParamsSet.Location = new System.Drawing.Point(0, 0);
            this.uc_SIA3900DeviceParamsSet.MinimumSize = new System.Drawing.Size(1, 1);
            this.uc_SIA3900DeviceParamsSet.Name = "uc_SIA3900DeviceParamsSet";
            this.uc_SIA3900DeviceParamsSet.Padding = new System.Windows.Forms.Padding(1);
            this.uc_SIA3900DeviceParamsSet.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.uc_SIA3900DeviceParamsSet.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.uc_SIA3900DeviceParamsSet.Size = new System.Drawing.Size(200, 60);
            this.uc_SIA3900DeviceParamsSet.TabIndex = 0;
            this.uc_SIA3900DeviceParamsSet.Text = null;
            this.uc_SIA3900DeviceParamsSet.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // tabOper
            // 
            this.tabOper.Controls.Add(this.uc_SIA3900DeviceOperControl);
            this.tabOper.Location = new System.Drawing.Point(0, 40);
            this.tabOper.Name = "tabOper";
            this.tabOper.Size = new System.Drawing.Size(200, 60);
            this.tabOper.TabIndex = 10;
            this.tabOper.Text = "流程控制";
            this.tabOper.UseVisualStyleBackColor = true;
            // 
            // uc_SIA3900DeviceOperControl
            // 
            this.uc_SIA3900DeviceOperControl.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(243)))), ((int)(((byte)(249)))), ((int)(((byte)(255)))));
            this.uc_SIA3900DeviceOperControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uc_SIA3900DeviceOperControl.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uc_SIA3900DeviceOperControl.Location = new System.Drawing.Point(0, 0);
            this.uc_SIA3900DeviceOperControl.MinimumSize = new System.Drawing.Size(1, 1);
            this.uc_SIA3900DeviceOperControl.Name = "uc_SIA3900DeviceOperControl";
            this.uc_SIA3900DeviceOperControl.Size = new System.Drawing.Size(200, 60);
            this.uc_SIA3900DeviceOperControl.TabIndex = 0;
            this.uc_SIA3900DeviceOperControl.Text = null;
            this.uc_SIA3900DeviceOperControl.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // tabCoefficient
            // 
            this.tabCoefficient.Controls.Add(this.uc_SIA3900CalibrateCoefficientDetail);
            this.tabCoefficient.Location = new System.Drawing.Point(0, 40);
            this.tabCoefficient.Name = "tabCoefficient";
            this.tabCoefficient.Size = new System.Drawing.Size(200, 60);
            this.tabCoefficient.TabIndex = 4;
            this.tabCoefficient.Text = "详细标定系数";
            this.tabCoefficient.UseVisualStyleBackColor = true;
            // 
            // uc_SIA3900CalibrateCoefficientDetail
            // 
            this.uc_SIA3900CalibrateCoefficientDetail.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uc_SIA3900CalibrateCoefficientDetail.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uc_SIA3900CalibrateCoefficientDetail.IsRefresh = true;
            this.uc_SIA3900CalibrateCoefficientDetail.Location = new System.Drawing.Point(0, 0);
            this.uc_SIA3900CalibrateCoefficientDetail.Margin = new System.Windows.Forms.Padding(4);
            this.uc_SIA3900CalibrateCoefficientDetail.MinimumSize = new System.Drawing.Size(1, 1);
            this.uc_SIA3900CalibrateCoefficientDetail.Name = "uc_SIA3900CalibrateCoefficientDetail";
            this.uc_SIA3900CalibrateCoefficientDetail.Padding = new System.Windows.Forms.Padding(1);
            this.uc_SIA3900CalibrateCoefficientDetail.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.uc_SIA3900CalibrateCoefficientDetail.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.uc_SIA3900CalibrateCoefficientDetail.Size = new System.Drawing.Size(200, 60);
            this.uc_SIA3900CalibrateCoefficientDetail.TabIndex = 0;
            this.uc_SIA3900CalibrateCoefficientDetail.Text = null;
            this.uc_SIA3900CalibrateCoefficientDetail.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // tabReagent
            // 
            this.tabReagent.Controls.Add(this.uc_SIA3900ReagentInfo);
            this.tabReagent.Location = new System.Drawing.Point(0, 40);
            this.tabReagent.Name = "tabReagent";
            this.tabReagent.Size = new System.Drawing.Size(1616, 896);
            this.tabReagent.TabIndex = 5;
            this.tabReagent.Text = "试剂维护";
            this.tabReagent.UseVisualStyleBackColor = true;
            // 
            // uc_SIA3900ReagentInfo
            // 
            this.uc_SIA3900ReagentInfo.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uc_SIA3900ReagentInfo.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uc_SIA3900ReagentInfo.Location = new System.Drawing.Point(0, 0);
            this.uc_SIA3900ReagentInfo.Margin = new System.Windows.Forms.Padding(4);
            this.uc_SIA3900ReagentInfo.MinimumSize = new System.Drawing.Size(1, 1);
            this.uc_SIA3900ReagentInfo.Name = "uc_SIA3900ReagentInfo";
            this.uc_SIA3900ReagentInfo.Padding = new System.Windows.Forms.Padding(1);
            this.uc_SIA3900ReagentInfo.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.uc_SIA3900ReagentInfo.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.uc_SIA3900ReagentInfo.Size = new System.Drawing.Size(1616, 896);
            this.uc_SIA3900ReagentInfo.TabIndex = 0;
            this.uc_SIA3900ReagentInfo.Text = null;
            this.uc_SIA3900ReagentInfo.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // tabDeviceData
            // 
            this.tabDeviceData.Controls.Add(this.uc_SIA3900CurrentLog);
            this.tabDeviceData.Controls.Add(this.uc_SIA3900HistoryDate);
            this.tabDeviceData.Location = new System.Drawing.Point(0, 40);
            this.tabDeviceData.Name = "tabDeviceData";
            this.tabDeviceData.Size = new System.Drawing.Size(1616, 896);
            this.tabDeviceData.TabIndex = 6;
            this.tabDeviceData.Text = "设备数据";
            this.tabDeviceData.UseVisualStyleBackColor = true;
            // 
            // uc_SIA3900CurrentLog
            // 
            this.uc_SIA3900CurrentLog.CanRefresh = true;
            this.uc_SIA3900CurrentLog.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uc_SIA3900CurrentLog.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uc_SIA3900CurrentLog.Location = new System.Drawing.Point(380, 0);
            this.uc_SIA3900CurrentLog.MinimumSize = new System.Drawing.Size(1, 1);
            this.uc_SIA3900CurrentLog.Name = "uc_SIA3900CurrentLog";
            this.uc_SIA3900CurrentLog.Padding = new System.Windows.Forms.Padding(1);
            this.uc_SIA3900CurrentLog.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.uc_SIA3900CurrentLog.RectColor = System.Drawing.Color.Transparent;
            this.uc_SIA3900CurrentLog.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.uc_SIA3900CurrentLog.Size = new System.Drawing.Size(1236, 896);
            this.uc_SIA3900CurrentLog.TabIndex = 3;
            this.uc_SIA3900CurrentLog.Text = "uC_SIA3900Log1";
            this.uc_SIA3900CurrentLog.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // uc_SIA3900HistoryDate
            // 
            this.uc_SIA3900HistoryDate.CanRefresh = true;
            this.uc_SIA3900HistoryDate.Dock = System.Windows.Forms.DockStyle.Left;
            this.uc_SIA3900HistoryDate.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uc_SIA3900HistoryDate.Location = new System.Drawing.Point(0, 0);
            this.uc_SIA3900HistoryDate.MinimumSize = new System.Drawing.Size(1, 1);
            this.uc_SIA3900HistoryDate.Name = "uc_SIA3900HistoryDate";
            this.uc_SIA3900HistoryDate.Padding = new System.Windows.Forms.Padding(1);
            this.uc_SIA3900HistoryDate.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.uc_SIA3900HistoryDate.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.uc_SIA3900HistoryDate.Size = new System.Drawing.Size(380, 896);
            this.uc_SIA3900HistoryDate.TabIndex = 6;
            this.uc_SIA3900HistoryDate.Text = "历史数据";
            this.uc_SIA3900HistoryDate.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // tabAlarm
            // 
            this.tabAlarm.Controls.Add(this.uc_DeviceAllAlarm);
            this.tabAlarm.Location = new System.Drawing.Point(0, 40);
            this.tabAlarm.Name = "tabAlarm";
            this.tabAlarm.Size = new System.Drawing.Size(200, 60);
            this.tabAlarm.TabIndex = 7;
            this.tabAlarm.Text = "设备报警";
            this.tabAlarm.UseVisualStyleBackColor = true;
            // 
            // uc_DeviceAllAlarm
            // 
            this.uc_DeviceAllAlarm.CanRefresh = true;
            this.uc_DeviceAllAlarm.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uc_DeviceAllAlarm.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uc_DeviceAllAlarm.Location = new System.Drawing.Point(0, 0);
            this.uc_DeviceAllAlarm.MinimumSize = new System.Drawing.Size(1, 1);
            this.uc_DeviceAllAlarm.Name = "uc_DeviceAllAlarm";
            this.uc_DeviceAllAlarm.Padding = new System.Windows.Forms.Padding(1);
            this.uc_DeviceAllAlarm.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.uc_DeviceAllAlarm.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.uc_DeviceAllAlarm.Size = new System.Drawing.Size(200, 60);
            this.uc_DeviceAllAlarm.TabIndex = 2;
            this.uc_DeviceAllAlarm.Text = "uc_DeviceAllAlarm";
            this.uc_DeviceAllAlarm.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // tabLogQuery
            // 
            this.tabLogQuery.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(243)))), ((int)(((byte)(249)))), ((int)(((byte)(255)))));
            this.tabLogQuery.Controls.Add(this.uc_SIA3900HistoryLog);
            this.tabLogQuery.Location = new System.Drawing.Point(0, 40);
            this.tabLogQuery.Name = "tabLogQuery";
            this.tabLogQuery.Size = new System.Drawing.Size(1616, 896);
            this.tabLogQuery.TabIndex = 8;
            this.tabLogQuery.Text = "历史日志";
            // 
            // uc_SIA3900HistoryLog
            // 
            this.uc_SIA3900HistoryLog.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uc_SIA3900HistoryLog.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uc_SIA3900HistoryLog.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(15)))), ((int)(((byte)(68)))), ((int)(((byte)(134)))));
            this.uc_SIA3900HistoryLog.Location = new System.Drawing.Point(0, 0);
            this.uc_SIA3900HistoryLog.MinimumSize = new System.Drawing.Size(1, 1);
            this.uc_SIA3900HistoryLog.Name = "uc_SIA3900HistoryLog";
            this.uc_SIA3900HistoryLog.Padding = new System.Windows.Forms.Padding(1);
            this.uc_SIA3900HistoryLog.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.uc_SIA3900HistoryLog.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.uc_SIA3900HistoryLog.Size = new System.Drawing.Size(1616, 896);
            this.uc_SIA3900HistoryLog.TabIndex = 1;
            this.uc_SIA3900HistoryLog.Text = "设备历史日志查询";
            this.uc_SIA3900HistoryLog.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // tabLife
            // 
            this.tabLife.Controls.Add(this.uc_DeviceLifeMonitor);
            this.tabLife.Location = new System.Drawing.Point(0, 40);
            this.tabLife.Name = "tabLife";
            this.tabLife.Size = new System.Drawing.Size(200, 60);
            this.tabLife.TabIndex = 9;
            this.tabLife.Text = "寿命监测";
            this.tabLife.UseVisualStyleBackColor = true;
            // 
            // uc_DeviceLifeMonitor
            // 
            this.uc_DeviceLifeMonitor.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uc_DeviceLifeMonitor.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uc_DeviceLifeMonitor.Location = new System.Drawing.Point(0, 0);
            this.uc_DeviceLifeMonitor.MinimumSize = new System.Drawing.Size(1, 1);
            this.uc_DeviceLifeMonitor.Name = "uc_DeviceLifeMonitor";
            this.uc_DeviceLifeMonitor.Padding = new System.Windows.Forms.Padding(2, 0, 2, 2);
            this.uc_DeviceLifeMonitor.Size = new System.Drawing.Size(200, 60);
            this.uc_DeviceLifeMonitor.TabIndex = 0;
            this.uc_DeviceLifeMonitor.Text = null;
            this.uc_DeviceLifeMonitor.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // tabDianosis
            // 
            this.tabDianosis.Controls.Add(this.uc_DiagnosisRecords);
            this.tabDianosis.Location = new System.Drawing.Point(0, 40);
            this.tabDianosis.Name = "tabDianosis";
            this.tabDianosis.Size = new System.Drawing.Size(200, 60);
            this.tabDianosis.TabIndex = 11;
            this.tabDianosis.Text = "诊断记录";
            this.tabDianosis.UseVisualStyleBackColor = true;
            // 
            // uc_DiagnosisRecords
            // 
            this.uc_DiagnosisRecords.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uc_DiagnosisRecords.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uc_DiagnosisRecords.Location = new System.Drawing.Point(0, 0);
            this.uc_DiagnosisRecords.MinimumSize = new System.Drawing.Size(1, 1);
            this.uc_DiagnosisRecords.Name = "uc_DiagnosisRecords";
            this.uc_DiagnosisRecords.Size = new System.Drawing.Size(200, 60);
            this.uc_DiagnosisRecords.TabIndex = 0;
            this.uc_DiagnosisRecords.Text = null;
            this.uc_DiagnosisRecords.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // UC_SIA3900Param
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.Controls.Add(this.tabMain);
            this.Name = "UC_SIA3900Param";
            this.Size = new System.Drawing.Size(1616, 936);
            this.Load += new System.EventHandler(this.UC_SIA3900Param_Load);
            this.tabMain.ResumeLayout(false);
            this.tbpState.ResumeLayout(false);
            this.tabParam.ResumeLayout(false);
            this.tbpMaintain.ResumeLayout(false);
            this.tabOper.ResumeLayout(false);
            this.tabCoefficient.ResumeLayout(false);
            this.tabReagent.ResumeLayout(false);
            this.tabDeviceData.ResumeLayout(false);
            this.tabAlarm.ResumeLayout(false);
            this.tabLogQuery.ResumeLayout(false);
            this.tabLife.ResumeLayout(false);
            this.tabDianosis.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private Sunny.UI.UITabControl tabMain;
        private System.Windows.Forms.TabPage tbpState;
        private System.Windows.Forms.TabPage tbpMaintain;
        private UC_SIA3900DeviceState uc_SIA3900DeviceState;
        private System.Windows.Forms.TabPage tabParam;
        private System.Windows.Forms.TabPage tabCoefficient;
        private UC_SIA3900CalibrateCoefficientDetail uc_SIA3900CalibrateCoefficientDetail;
        private System.Windows.Forms.TabPage tabReagent;
        private Equipment.UI.UC_SIA3900ReagentInfo uc_SIA3900ReagentInfo;
        private System.Windows.Forms.TabPage tabDeviceData;
        private System.Windows.Forms.TabPage tabAlarm;
        private Common.UI.UC_DeviceAllAlarm uc_DeviceAllAlarm;
        private UC_DeviceParamData uc_SIA3900StateAlarm;
        private UC_DeviceParamData uc_SIA3900DeviceKeyParams;
        private UC_DeviceParamData uc_SIA3900MeasureParam;
        private UC_DeviceParamData uc_SIA3900HistoryDate;
        private UC_DeviceCurrentLogQuery uc_SIA3900CurrentLog;
        private System.Windows.Forms.TabPage tabLogQuery;
        private UC_DeviceHistoryLogQuery uc_SIA3900HistoryLog;
        private System.Windows.Forms.TabPage tabLife;
        private System.Windows.Forms.TabPage tabOper;
        private UC_SIA3900DeviceParamsSet uc_SIA3900DeviceParamsSet;
        private UC_SIA3900DeviceOperControl uc_SIA3900DeviceOperControl;
        private System.Windows.Forms.TabPage tabDianosis;
        private UC_SIA3900DeviceLifeMonitor uc_DeviceLifeMonitor;
        private UC_SIA3900DiagnosisRecords uc_DiagnosisRecords;
    }
}
