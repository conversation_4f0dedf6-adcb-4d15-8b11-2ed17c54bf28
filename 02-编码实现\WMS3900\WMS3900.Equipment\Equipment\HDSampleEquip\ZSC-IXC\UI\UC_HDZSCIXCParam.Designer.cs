﻿namespace Fpi.WMS3000.Equipment.UI
{
    partial class UC_HDZSCIXCParam
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if(disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.tabControl = new System.Windows.Forms.TabPage();
            this.uc_DeviceOperControl = new Fpi.WMS3000.Equipment.UI.UC_HDZSCIXCDeviceOperControl();
            this.tbpState = new System.Windows.Forms.TabPage();
            this.uc_DeviceAllAlarm = new Fpi.WMS3000.Equipment.Common.UI.UC_DeviceAllAlarm();
            this.uc_DeviceParamData = new Fpi.WMS3000.Equipment.UI.UC_DeviceParamData();
            this.tabMain = new Sunny.UI.UITabControl();
            this.tabPage1 = new System.Windows.Forms.TabPage();
            this.uC_SampleDoorLogQuery1 = new Fpi.WMS3000.Equipment.UI.UC_SampleDoorLogQuery();
            this.tabControl.SuspendLayout();
            this.tbpState.SuspendLayout();
            this.tabMain.SuspendLayout();
            this.tabPage1.SuspendLayout();
            this.SuspendLayout();
            // 
            // tabControl
            // 
            this.tabControl.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(243)))), ((int)(((byte)(249)))), ((int)(((byte)(255)))));
            this.tabControl.Controls.Add(this.uc_DeviceOperControl);
            this.tabControl.Location = new System.Drawing.Point(0, 40);
            this.tabControl.Name = "tabControl";
            this.tabControl.Size = new System.Drawing.Size(200, 60);
            this.tabControl.TabIndex = 3;
            this.tabControl.Text = "设备控制";
            // 
            // uc_DeviceOperControl
            // 
            this.uc_DeviceOperControl.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(243)))), ((int)(((byte)(249)))), ((int)(((byte)(255)))));
            this.uc_DeviceOperControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uc_DeviceOperControl.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uc_DeviceOperControl.Location = new System.Drawing.Point(0, 0);
            this.uc_DeviceOperControl.MinimumSize = new System.Drawing.Size(1, 1);
            this.uc_DeviceOperControl.Name = "uc_DeviceOperControl";
            this.uc_DeviceOperControl.Padding = new System.Windows.Forms.Padding(1);
            this.uc_DeviceOperControl.Size = new System.Drawing.Size(200, 60);
            this.uc_DeviceOperControl.TabIndex = 0;
            this.uc_DeviceOperControl.Text = "uC_HDZSCIXCDeviceOperControl1";
            this.uc_DeviceOperControl.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // tbpState
            // 
            this.tbpState.AutoScroll = true;
            this.tbpState.Controls.Add(this.uc_DeviceAllAlarm);
            this.tbpState.Controls.Add(this.uc_DeviceParamData);
            this.tbpState.Location = new System.Drawing.Point(0, 40);
            this.tbpState.Name = "tbpState";
            this.tbpState.Size = new System.Drawing.Size(1616, 896);
            this.tbpState.TabIndex = 0;
            this.tbpState.Text = "系统状态";
            this.tbpState.UseVisualStyleBackColor = true;
            // 
            // uc_DeviceAllAlarm
            // 
            this.uc_DeviceAllAlarm.CanRefresh = true;
            this.uc_DeviceAllAlarm.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uc_DeviceAllAlarm.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uc_DeviceAllAlarm.Location = new System.Drawing.Point(1216, 0);
            this.uc_DeviceAllAlarm.MinimumSize = new System.Drawing.Size(1, 1);
            this.uc_DeviceAllAlarm.Name = "uc_DeviceAllAlarm";
            this.uc_DeviceAllAlarm.Padding = new System.Windows.Forms.Padding(1);
            this.uc_DeviceAllAlarm.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.uc_DeviceAllAlarm.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.uc_DeviceAllAlarm.Size = new System.Drawing.Size(400, 896);
            this.uc_DeviceAllAlarm.TabIndex = 1;
            this.uc_DeviceAllAlarm.Text = "uc_DeviceAllAlarm";
            this.uc_DeviceAllAlarm.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // uc_DeviceParamData
            // 
            this.uc_DeviceParamData.CanRefresh = true;
            this.uc_DeviceParamData.Dock = System.Windows.Forms.DockStyle.Left;
            this.uc_DeviceParamData.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uc_DeviceParamData.Location = new System.Drawing.Point(0, 0);
            this.uc_DeviceParamData.MinimumSize = new System.Drawing.Size(1, 1);
            this.uc_DeviceParamData.Name = "uc_DeviceParamData";
            this.uc_DeviceParamData.Padding = new System.Windows.Forms.Padding(1);
            this.uc_DeviceParamData.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.uc_DeviceParamData.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.uc_DeviceParamData.Size = new System.Drawing.Size(1216, 896);
            this.uc_DeviceParamData.TabIndex = 3;
            this.uc_DeviceParamData.Text = "设备参数";
            this.uc_DeviceParamData.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // tabMain
            // 
            this.tabMain.Controls.Add(this.tbpState);
            this.tabMain.Controls.Add(this.tabControl);
            this.tabMain.Controls.Add(this.tabPage1);
            this.tabMain.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabMain.DrawMode = System.Windows.Forms.TabDrawMode.OwnerDrawFixed;
            this.tabMain.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.tabMain.ItemSize = new System.Drawing.Size(150, 40);
            this.tabMain.Location = new System.Drawing.Point(0, 0);
            this.tabMain.MainPage = "";
            this.tabMain.MenuStyle = Sunny.UI.UIMenuStyle.White;
            this.tabMain.Name = "tabMain";
            this.tabMain.RightToLeft = System.Windows.Forms.RightToLeft.No;
            this.tabMain.SelectedIndex = 0;
            this.tabMain.Size = new System.Drawing.Size(1616, 936);
            this.tabMain.SizeMode = System.Windows.Forms.TabSizeMode.Fixed;
            this.tabMain.TabBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(240)))), ((int)(((byte)(240)))));
            this.tabMain.TabIndex = 0;
            this.tabMain.TabPageTextAlignment = System.Windows.Forms.HorizontalAlignment.Center;
            this.tabMain.TabSelectedColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(250)))), ((int)(((byte)(250)))));
            this.tabMain.TabSelectedHighColorSize = 0;
            this.tabMain.TabUnSelectedColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(240)))), ((int)(((byte)(240)))));
            this.tabMain.TabUnSelectedForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.tabMain.TipsFont = new System.Drawing.Font("微软雅黑", 12F);
            // 
            // tabPage1
            // 
            this.tabPage1.Controls.Add(this.uC_SampleDoorLogQuery1);
            this.tabPage1.Location = new System.Drawing.Point(0, 40);
            this.tabPage1.Name = "tabPage1";
            this.tabPage1.Size = new System.Drawing.Size(1616, 896);
            this.tabPage1.TabIndex = 4;
            this.tabPage1.Text = "门禁记录";
            this.tabPage1.UseVisualStyleBackColor = true;
            // 
            // uC_SampleDoorLogQuery1
            // 
            this.uC_SampleDoorLogQuery1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uC_SampleDoorLogQuery1.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uC_SampleDoorLogQuery1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(80)))), ((int)(((byte)(160)))), ((int)(((byte)(255)))));
            this.uC_SampleDoorLogQuery1.Location = new System.Drawing.Point(0, 0);
            this.uC_SampleDoorLogQuery1.MinimumSize = new System.Drawing.Size(1, 1);
            this.uC_SampleDoorLogQuery1.Name = "uC_SampleDoorLogQuery1";
            this.uC_SampleDoorLogQuery1.Padding = new System.Windows.Forms.Padding(1);
            this.uC_SampleDoorLogQuery1.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.uC_SampleDoorLogQuery1.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.uC_SampleDoorLogQuery1.Size = new System.Drawing.Size(1616, 896);
            this.uC_SampleDoorLogQuery1.TabIndex = 0;
            this.uC_SampleDoorLogQuery1.Text = "uC_SampleDoorLogQuery1";
            this.uC_SampleDoorLogQuery1.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // UC_HDZSCIXCParam
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(243)))), ((int)(((byte)(249)))), ((int)(((byte)(255)))));
            this.Controls.Add(this.tabMain);
            this.Name = "UC_HDZSCIXCParam";
            this.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.Size = new System.Drawing.Size(1616, 936);
            this.Load += new System.EventHandler(this.UC_QCD3900Params_Load);
            this.tabControl.ResumeLayout(false);
            this.tbpState.ResumeLayout(false);
            this.tabMain.ResumeLayout(false);
            this.tabPage1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.TabPage tabControl;
        private System.Windows.Forms.TabPage tbpState;
        private Common.UI.UC_DeviceAllAlarm uc_DeviceAllAlarm;
        private Sunny.UI.UITabControl tabMain;
        private UC_DeviceParamData uc_DeviceParamData;
        private UC_HDZSCIXCDeviceOperControl uc_DeviceOperControl;
        private System.Windows.Forms.TabPage tabPage1;
        private UC_SampleDoorLogQuery uC_SampleDoorLogQuery1;
    }
}
