﻿using System;
using System.Threading.Tasks;
using Fpi.WMS3000.Equipment.Config;
using Fpi.WMS3000.Voice.Helper;

namespace Fpi.WMS3000.Voice.VoicesTemplate
{
    /// <summary>
    /// 打开门禁
    /// </summary>
    public class EntranceControl : CustomVoiceCmd
    {
        #region 公共方法（重写）

        public override string ToString()
        {
            return "打开门禁";
        }

        public override string CustomDo(int paran)
        {
            try
            {
                var netEntrance = ExterEquipConfigManager.GetInstance().EntranceSelect.MainEntran;
                if(netEntrance == null)
                {
                    throw new Exception("系统内未配置门禁设备！");
                }

                Task.Run(() =>
                {
                    try
                    {
                        netEntrance.OpenDoorAsync().Wait();

                        VoiceLogHelper.Info($"[{CmdConfig.name}]命令执行成功。");
                    }
                    catch(Exception e)
                    {
                        VoiceLogHelper.Info($"[{CmdConfig.name}]命令执行出错：{e.Message}");
                    }
                });

                return "好的，正在打开";
            }
            catch(Exception ex)
            {
                VoiceLogHelper.Info($"[{CmdConfig.name}]命令执行出错: {ex.Message}");
                return $"抱歉，执行出错，{ex.Message}";
            }
        }

        #endregion
    }
}