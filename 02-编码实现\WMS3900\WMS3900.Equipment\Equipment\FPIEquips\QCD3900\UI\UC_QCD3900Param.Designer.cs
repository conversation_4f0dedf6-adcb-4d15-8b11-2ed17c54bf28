﻿namespace Fpi.WMS3000.Equipment.UI
{
    partial class UC_QCD3900Param
    {
        /// <summary> 
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if(disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region 组件设计器生成的代码

        /// <summary> 
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.tabMain = new Sunny.UI.UITabControl();
            this.tbpState = new System.Windows.Forms.TabPage();
            this.uc_DeviceAllAlarm = new Fpi.WMS3000.Equipment.Common.UI.UC_DeviceAllAlarm();
            this.uc_QCD3900DeviceState = new Fpi.WMS3000.Equipment.UI.UC_QCD3900DeviceState();
            this.tabLiquidLife = new System.Windows.Forms.TabPage();
            this.uc_QCD3900LiquidInfo = new Fpi.WMS3000.Equipment.UI.btnRefreshOther();
            this.tbpElementLite = new System.Windows.Forms.TabPage();
            this.uc_QCD3900CompInfo = new Fpi.WMS3000.Equipment.UI.UC_QCD3900CompInfo();
            this.tbpMaintain = new System.Windows.Forms.TabPage();
            this.uc_QCD3900Maintain = new Fpi.WMS3000.Equipment.UI.UC_QCD3900Maintain();
            this.tabFlowControl = new System.Windows.Forms.TabPage();
            this.uc_QCD3900FlowControl = new Fpi.WMS3000.Equipment.UI.UC_QCD3900FlowControl();
            this.uc_QCD3900MeasureParam = new Fpi.WMS3000.Equipment.UI.UC_QCD3900MeasureParam();
            this.tabCurrentLog = new System.Windows.Forms.TabPage();
            this.uc_DeviceLog = new Fpi.WMS3000.Equipment.UI.UC_DeviceCurrentLogQuery();
            this.tabLog = new System.Windows.Forms.TabPage();
            this.uc_DeviceHistoryLog = new Fpi.WMS3000.Equipment.UI.UC_DeviceHistoryLogQuery();
            this.tabMain.SuspendLayout();
            this.tbpState.SuspendLayout();
            this.tabLiquidLife.SuspendLayout();
            this.tbpElementLite.SuspendLayout();
            this.tbpMaintain.SuspendLayout();
            this.tabFlowControl.SuspendLayout();
            this.tabCurrentLog.SuspendLayout();
            this.tabLog.SuspendLayout();
            this.SuspendLayout();
            // 
            // tabMain
            // 
            this.tabMain.Controls.Add(this.tbpState);
            this.tabMain.Controls.Add(this.tabLiquidLife);
            this.tabMain.Controls.Add(this.tbpElementLite);
            this.tabMain.Controls.Add(this.tbpMaintain);
            this.tabMain.Controls.Add(this.tabFlowControl);
            this.tabMain.Controls.Add(this.tabCurrentLog);
            this.tabMain.Controls.Add(this.tabLog);
            this.tabMain.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tabMain.DrawMode = System.Windows.Forms.TabDrawMode.OwnerDrawFixed;
            this.tabMain.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.tabMain.ItemSize = new System.Drawing.Size(150, 40);
            this.tabMain.Location = new System.Drawing.Point(0, 0);
            this.tabMain.MainPage = "";
            this.tabMain.MenuStyle = Sunny.UI.UIMenuStyle.White;
            this.tabMain.Name = "tabMain";
            this.tabMain.RightToLeft = System.Windows.Forms.RightToLeft.No;
            this.tabMain.SelectedIndex = 0;
            this.tabMain.Size = new System.Drawing.Size(1616, 936);
            this.tabMain.SizeMode = System.Windows.Forms.TabSizeMode.Fixed;
            this.tabMain.TabBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(240)))), ((int)(((byte)(240)))));
            this.tabMain.TabIndex = 0;
            this.tabMain.TabPageTextAlignment = System.Windows.Forms.HorizontalAlignment.Center;
            this.tabMain.TabSelectedColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(250)))), ((int)(((byte)(250)))));
            this.tabMain.TabSelectedHighColorSize = 0;
            this.tabMain.TabUnSelectedColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(240)))), ((int)(((byte)(240)))));
            this.tabMain.TabUnSelectedForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.tabMain.TipsFont = new System.Drawing.Font("微软雅黑", 12F);
            // 
            // tbpState
            // 
            this.tbpState.AutoScroll = true;
            this.tbpState.Controls.Add(this.uc_DeviceAllAlarm);
            this.tbpState.Controls.Add(this.uc_QCD3900DeviceState);
            this.tbpState.Location = new System.Drawing.Point(0, 40);
            this.tbpState.Name = "tbpState";
            this.tbpState.Size = new System.Drawing.Size(1616, 896);
            this.tbpState.TabIndex = 0;
            this.tbpState.Text = "系统状态";
            this.tbpState.UseVisualStyleBackColor = true;
            // 
            // uc_DeviceAllAlarm
            // 
            this.uc_DeviceAllAlarm.CanRefresh = true;
            this.uc_DeviceAllAlarm.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uc_DeviceAllAlarm.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uc_DeviceAllAlarm.Location = new System.Drawing.Point(335, 0);
            this.uc_DeviceAllAlarm.MinimumSize = new System.Drawing.Size(1, 1);
            this.uc_DeviceAllAlarm.Name = "uc_DeviceAllAlarm";
            this.uc_DeviceAllAlarm.Padding = new System.Windows.Forms.Padding(1);
            this.uc_DeviceAllAlarm.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.uc_DeviceAllAlarm.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.uc_DeviceAllAlarm.Size = new System.Drawing.Size(1281, 896);
            this.uc_DeviceAllAlarm.TabIndex = 1;
            this.uc_DeviceAllAlarm.Text = "uc_DeviceAllAlarm";
            this.uc_DeviceAllAlarm.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // uc_QCD3900DeviceState
            // 
            this.uc_QCD3900DeviceState.Dock = System.Windows.Forms.DockStyle.Left;
            this.uc_QCD3900DeviceState.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uc_QCD3900DeviceState.Location = new System.Drawing.Point(0, 0);
            this.uc_QCD3900DeviceState.MinimumSize = new System.Drawing.Size(1, 1);
            this.uc_QCD3900DeviceState.Name = "uc_QCD3900DeviceState";
            this.uc_QCD3900DeviceState.Padding = new System.Windows.Forms.Padding(1);
            this.uc_QCD3900DeviceState.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.uc_QCD3900DeviceState.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.uc_QCD3900DeviceState.Size = new System.Drawing.Size(335, 896);
            this.uc_QCD3900DeviceState.TabIndex = 0;
            this.uc_QCD3900DeviceState.Text = "uC_QCD3900DeviceStateParams1";
            this.uc_QCD3900DeviceState.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // tabLiquidLife
            // 
            this.tabLiquidLife.Controls.Add(this.uc_QCD3900LiquidInfo);
            this.tabLiquidLife.Location = new System.Drawing.Point(0, 40);
            this.tabLiquidLife.Name = "tabLiquidLife";
            this.tabLiquidLife.Size = new System.Drawing.Size(1616, 896);
            this.tabLiquidLife.TabIndex = 4;
            this.tabLiquidLife.Text = "试剂维护";
            this.tabLiquidLife.UseVisualStyleBackColor = true;
            // 
            // uc_QCD3900LiquidInfo
            // 
            this.uc_QCD3900LiquidInfo.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uc_QCD3900LiquidInfo.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uc_QCD3900LiquidInfo.Location = new System.Drawing.Point(0, 0);
            this.uc_QCD3900LiquidInfo.MinimumSize = new System.Drawing.Size(1, 1);
            this.uc_QCD3900LiquidInfo.Name = "uc_QCD3900LiquidInfo";
            this.uc_QCD3900LiquidInfo.Padding = new System.Windows.Forms.Padding(1);
            this.uc_QCD3900LiquidInfo.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.uc_QCD3900LiquidInfo.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.uc_QCD3900LiquidInfo.Size = new System.Drawing.Size(1616, 896);
            this.uc_QCD3900LiquidInfo.TabIndex = 0;
            this.uc_QCD3900LiquidInfo.Text = null;
            this.uc_QCD3900LiquidInfo.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // tbpElementLite
            // 
            this.tbpElementLite.AutoScroll = true;
            this.tbpElementLite.Controls.Add(this.uc_QCD3900CompInfo);
            this.tbpElementLite.Location = new System.Drawing.Point(0, 40);
            this.tbpElementLite.Name = "tbpElementLite";
            this.tbpElementLite.Size = new System.Drawing.Size(200, 60);
            this.tbpElementLite.TabIndex = 1;
            this.tbpElementLite.Text = "器件维护";
            this.tbpElementLite.UseVisualStyleBackColor = true;
            // 
            // uc_QCD3900CompInfo
            // 
            this.uc_QCD3900CompInfo.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uc_QCD3900CompInfo.Font = new System.Drawing.Font("微软雅黑", 12F);
            this.uc_QCD3900CompInfo.Location = new System.Drawing.Point(0, 0);
            this.uc_QCD3900CompInfo.MinimumSize = new System.Drawing.Size(1, 1);
            this.uc_QCD3900CompInfo.Name = "uc_QCD3900CompInfo";
            this.uc_QCD3900CompInfo.Padding = new System.Windows.Forms.Padding(1);
            this.uc_QCD3900CompInfo.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.uc_QCD3900CompInfo.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.uc_QCD3900CompInfo.Size = new System.Drawing.Size(200, 60);
            this.uc_QCD3900CompInfo.TabIndex = 0;
            this.uc_QCD3900CompInfo.Text = "uc_QCD3900CompInfo";
            this.uc_QCD3900CompInfo.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // tbpMaintain
            // 
            this.tbpMaintain.AutoScroll = true;
            this.tbpMaintain.Controls.Add(this.uc_QCD3900Maintain);
            this.tbpMaintain.Location = new System.Drawing.Point(0, 40);
            this.tbpMaintain.Name = "tbpMaintain";
            this.tbpMaintain.Size = new System.Drawing.Size(200, 60);
            this.tbpMaintain.TabIndex = 2;
            this.tbpMaintain.Text = "设备维护";
            this.tbpMaintain.UseVisualStyleBackColor = true;
            // 
            // uc_QCD3900Maintain
            // 
            this.uc_QCD3900Maintain.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uc_QCD3900Maintain.Location = new System.Drawing.Point(0, 0);
            this.uc_QCD3900Maintain.MinimumSize = new System.Drawing.Size(1, 1);
            this.uc_QCD3900Maintain.Name = "uc_QCD3900Maintain";
            this.uc_QCD3900Maintain.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.uc_QCD3900Maintain.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.uc_QCD3900Maintain.Size = new System.Drawing.Size(1612, 896);
            this.uc_QCD3900Maintain.TabIndex = 1;
            this.uc_QCD3900Maintain.Text = null;
            this.uc_QCD3900Maintain.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // tabFlowControl
            // 
            this.tabFlowControl.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(243)))), ((int)(((byte)(249)))), ((int)(((byte)(255)))));
            this.tabFlowControl.Controls.Add(this.uc_QCD3900FlowControl);
            this.tabFlowControl.Controls.Add(this.uc_QCD3900MeasureParam);
            this.tabFlowControl.Location = new System.Drawing.Point(0, 40);
            this.tabFlowControl.Name = "tabFlowControl";
            this.tabFlowControl.Size = new System.Drawing.Size(200, 60);
            this.tabFlowControl.TabIndex = 3;
            this.tabFlowControl.Text = "流程控制";
            // 
            // uc_QCD3900FlowControl
            // 
            this.uc_QCD3900FlowControl.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uc_QCD3900FlowControl.Location = new System.Drawing.Point(384, 0);
            this.uc_QCD3900FlowControl.MinimumSize = new System.Drawing.Size(1, 1);
            this.uc_QCD3900FlowControl.Name = "uc_QCD3900FlowControl";
            this.uc_QCD3900FlowControl.Padding = new System.Windows.Forms.Padding(1);
            this.uc_QCD3900FlowControl.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.uc_QCD3900FlowControl.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.uc_QCD3900FlowControl.Size = new System.Drawing.Size(605, 896);
            this.uc_QCD3900FlowControl.TabIndex = 0;
            this.uc_QCD3900FlowControl.Text = "uC_QCD3900FlowControl1";
            this.uc_QCD3900FlowControl.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // uc_QCD3900MeasureParam
            // 
            this.uc_QCD3900MeasureParam.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uc_QCD3900MeasureParam.Location = new System.Drawing.Point(0, 0);
            this.uc_QCD3900MeasureParam.MinimumSize = new System.Drawing.Size(1, 1);
            this.uc_QCD3900MeasureParam.Name = "uc_QCD3900MeasureParam";
            this.uc_QCD3900MeasureParam.Padding = new System.Windows.Forms.Padding(1);
            this.uc_QCD3900MeasureParam.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.uc_QCD3900MeasureParam.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.uc_QCD3900MeasureParam.Size = new System.Drawing.Size(384, 896);
            this.uc_QCD3900MeasureParam.TabIndex = 1;
            this.uc_QCD3900MeasureParam.Text = "uC_QCD3900MeasureParam1";
            this.uc_QCD3900MeasureParam.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // tabCurrentLog
            // 
            this.tabCurrentLog.Controls.Add(this.uc_DeviceLog);
            this.tabCurrentLog.Location = new System.Drawing.Point(0, 40);
            this.tabCurrentLog.Name = "tabCurrentLog";
            this.tabCurrentLog.Size = new System.Drawing.Size(200, 60);
            this.tabCurrentLog.TabIndex = 6;
            this.tabCurrentLog.Text = "运行日志";
            this.tabCurrentLog.UseVisualStyleBackColor = true;
            // 
            // uc_DeviceLog
            // 
            this.uc_DeviceLog.CanRefresh = true;
            this.uc_DeviceLog.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uc_DeviceLog.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uc_DeviceLog.Location = new System.Drawing.Point(0, 0);
            this.uc_DeviceLog.MinimumSize = new System.Drawing.Size(1, 1);
            this.uc_DeviceLog.Name = "uc_DeviceLog";
            this.uc_DeviceLog.Padding = new System.Windows.Forms.Padding(1);
            this.uc_DeviceLog.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.uc_DeviceLog.RectColor = System.Drawing.Color.Transparent;
            this.uc_DeviceLog.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.uc_DeviceLog.Size = new System.Drawing.Size(200, 60);
            this.uc_DeviceLog.TabIndex = 2;
            this.uc_DeviceLog.Text = null;
            this.uc_DeviceLog.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // tabLog
            // 
            this.tabLog.Controls.Add(this.uc_DeviceHistoryLog);
            this.tabLog.Location = new System.Drawing.Point(0, 40);
            this.tabLog.Name = "tabLog";
            this.tabLog.Size = new System.Drawing.Size(200, 60);
            this.tabLog.TabIndex = 5;
            this.tabLog.Text = "历史日志";
            this.tabLog.UseVisualStyleBackColor = true;
            // 
            // uc_DeviceHistoryLog
            // 
            this.uc_DeviceHistoryLog.Dock = System.Windows.Forms.DockStyle.Fill;
            this.uc_DeviceHistoryLog.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uc_DeviceHistoryLog.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(80)))), ((int)(((byte)(160)))), ((int)(((byte)(255)))));
            this.uc_DeviceHistoryLog.Location = new System.Drawing.Point(0, 0);
            this.uc_DeviceHistoryLog.MinimumSize = new System.Drawing.Size(1, 1);
            this.uc_DeviceHistoryLog.Name = "uc_DeviceHistoryLog";
            this.uc_DeviceHistoryLog.Padding = new System.Windows.Forms.Padding(1);
            this.uc_DeviceHistoryLog.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.uc_DeviceHistoryLog.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.uc_DeviceHistoryLog.Size = new System.Drawing.Size(200, 60);
            this.uc_DeviceHistoryLog.TabIndex = 0;
            this.uc_DeviceHistoryLog.Text = "设备历史日志查询";
            this.uc_DeviceHistoryLog.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // UC_QCD3900Param
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(243)))), ((int)(((byte)(249)))), ((int)(((byte)(255)))));
            this.Controls.Add(this.tabMain);
            this.Name = "UC_QCD3900Param";
            this.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.Size = new System.Drawing.Size(1616, 936);
            this.Load += new System.EventHandler(this.UC_QCD3900Params_Load);
            this.tabMain.ResumeLayout(false);
            this.tbpState.ResumeLayout(false);
            this.tabLiquidLife.ResumeLayout(false);
            this.tbpElementLite.ResumeLayout(false);
            this.tbpMaintain.ResumeLayout(false);
            this.tabFlowControl.ResumeLayout(false);
            this.tabCurrentLog.ResumeLayout(false);
            this.tabLog.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private Sunny.UI.UITabControl tabMain;
        private System.Windows.Forms.TabPage tbpState;
        private System.Windows.Forms.TabPage tbpElementLite;
        private System.Windows.Forms.TabPage tbpMaintain;
        private Common.UI.UC_DeviceAllAlarm uc_DeviceAllAlarm;
        private UC_QCD3900DeviceState uc_QCD3900DeviceState;
        private UC_QCD3900Maintain uc_QCD3900Maintain;
        private System.Windows.Forms.TabPage tabFlowControl;
        private UC_QCD3900FlowControl uc_QCD3900FlowControl;
        private UC_QCD3900MeasureParam uc_QCD3900MeasureParam;
        private System.Windows.Forms.TabPage tabLiquidLife;
        private btnRefreshOther uc_QCD3900LiquidInfo;
        private UC_QCD3900CompInfo uc_QCD3900CompInfo;
        private System.Windows.Forms.TabPage tabLog;
        private UC_DeviceHistoryLogQuery uc_DeviceHistoryLog;
        private System.Windows.Forms.TabPage tabCurrentLog;
        private UC_DeviceCurrentLogQuery uc_DeviceLog;
    }
}
