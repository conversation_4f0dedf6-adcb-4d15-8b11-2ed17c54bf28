﻿using System.Collections.Generic;
using System.ComponentModel;
using Fpi.WMS3000.Equipment;

namespace Fpi.WMS3000.SystemConfig.ImagePatrol.Config
{
    /// <summary>
    /// 高指氨氮质控仪巡检结果
    /// </summary>
    public class CodMnNH4QCDPatrolResult : ImageUnitPatrolResultBase
    {
        #region 字段属性

        #region 高指质控仪

        /// <summary>
        /// 高指质控水样管状态
        /// </summary>
        [Description("高指质控水样管状态")]
        public eSmutState CodMnWaterTubeSmutState { get; set; }

        /// <summary>
        /// 高指质控标样管状态
        /// </summary>
        [Description("高指质控标样管状态")]
        public eSmutState CodMnSampleTubeSmutState { get; set; }

        /// <summary>
        /// 高指质控水样杯脏污
        /// </summary>
        [Description("高指质控水样杯脏污")]
        public eSmutState CodMnWaterCupSmutState { get; set; }

        /// <summary>
        /// 高指质控标样杯脏污
        /// </summary>
        [Description("高指质控标样杯脏污")]
        public eSmutState CodMnSampleCupSmutState { get; set; }

        #endregion

        #region 氨氮质控仪

        /// <summary>
        /// 氨氮质控水样管状态
        /// </summary>
        [Description("氨氮质控水样管状态")]
        public eSmutState NH4WaterTubeSmutState { get; set; }

        /// <summary>
        /// 氨氮质控标样管状态
        /// </summary>
        [Description("氨氮质控标样管状态")]
        public eSmutState NH4SampleTubeSmutState { get; set; }

        /// <summary>
        /// 氨氮质控水样杯脏污
        /// </summary>
        [Description("氨氮质控水样杯脏污")]
        public eSmutState NH4WaterCupSmutState { get; set; }

        /// <summary>
        /// 氨氮质控标样杯脏污
        /// </summary>
        [Description("氨氮质控标样杯脏污")]
        public eSmutState NH4SampleCupSmutState { get; set; }

        #endregion

        #region 水桶

        /// <summary>
        /// 高指氨氮废水桶状态
        /// </summary>
        [Description("高指氨氮废水桶状态")]
        public eEarlyWarnState CodMnNH4WasteWaterBucketState { get; set; }

        /// <summary>
        /// 高指氨氮废液桶状态
        /// </summary>
        [Description("高指氨氮废液桶状态")]
        public eEarlyWarnState CodMnNH4WasteTankBucketState { get; set; }

        /// <summary>
        /// 高指氨氮纯水桶状态
        /// </summary>
        [Description("高指氨氮纯水桶状态")]
        public eEarlyWarnState CodMnNH4WaterBucketState { get; set; }

        #endregion

        #endregion

        #region 构造

        public CodMnNH4QCDPatrolResult()
        {
            UnitId = "CodMnNH4QCD";
            UnitName = "高指氨氮质控仪";
        }

        #endregion

        #region 方法重写

        /// <summary>
        /// 打印巡检结果
        /// </summary>
        /// <returns></returns>
        public override List<string> GetResultStr()
        {
            return new List<string>
            {
                $"高指质控水样管状态：{CodMnWaterTubeSmutState}",
                $"高指质控标样管状态：{CodMnSampleTubeSmutState}",
                $"高指质控水样杯脏污：{CodMnWaterCupSmutState}",
                $"高指质控标样杯脏污：{CodMnSampleCupSmutState}",
                $"氨氮质控水样管状态：{NH4WaterTubeSmutState}",
                $"氨氮质控标样管状态：{NH4SampleTubeSmutState}",
                $"氨氮质控水样杯脏污：{NH4WaterCupSmutState}",
                $"氨氮质控标样杯脏污：{NH4SampleCupSmutState}",
                $"高指氨氮废水桶状态：{CodMnNH4WasteWaterBucketState}",
                $"高指氨氮废液桶状态：{CodMnNH4WasteTankBucketState}",
                $"高指氨氮纯水桶状态：{CodMnNH4WaterBucketState}"
            };
        }

        #endregion
    }
}