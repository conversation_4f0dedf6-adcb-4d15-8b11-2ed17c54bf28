﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Fpi.Json;
using Fpi.Util.Extensions;
using Fpi.Util.Reflection;
using Fpi.WMS3000.Algorithm;
using Fpi.WMS3000.Equipment;
using Fpi.WMS3000.Equipment.Config;
using Fpi.WMS3000.SystemConfig.SmartPatrol;
using Newtonsoft.Json;
using OpenCvSharp;

namespace Fpi.WMS3000.SystemConfig.ImagePatrol.Config
{
    /// <summary>
    /// 图像巡检管理类
    /// 巡检触发，过程控制，结果保存
    /// </summary>
    public class ImagePatrolManager : BaseJsonNode
    {
        #region 字段属性

        #region 配置存储

        /// <summary>
        /// 当前启用的巡检项类型列表
        /// </summary>
        [Description("当前启用的巡检项类型列表")]
        public List<string> UsedPatrolUnitTypeList = new List<string>();

        #endregion

        /// <summary>
        /// 巡检单元列表
        /// </summary>
        [Description("巡检单元列表")]
        [JsonIgnore]
        public List<ImageUnitSmartPatrolBase> PatrolUnitList = new List<ImageUnitSmartPatrolBase>();

        /// <summary>
        /// 当前正在运行的单元的序号
        /// </summary>
        [Description("当前正在运行的单元的序号")]
        [JsonIgnore]
        public int CurrnetUnitIndex;

        /// <summary>
        /// 最新巡检结果
        /// </summary>
        [Description("最新巡检结果")]
        public OldImagePatrolResult LatestImagePatrolResult;

        /// <summary>
        /// 巡检状态
        /// </summary>
        [Description("巡检状态")]
        [JsonIgnore]
        public ePatrolState PatrolState;

        /// <summary>
        /// 任务取消Token
        /// </summary>
        [JsonIgnore]
        public CancellationTokenSource CancelToken;

        #endregion

        #region 单例

        private ImagePatrolManager()
        {
            // 巡检执行时打印日志
            PatrolLogEvent += (string info) => { ImagePatrolLogHelper.WritePatrolLog(info); };

            loadJson();

            // 构建巡检项列表
            RebuildPatrolUnitList();
        }

        [JsonIgnore]
        private static readonly object lockObj = new object();

        [JsonIgnore]
        private static ImagePatrolManager _instance = null;

        public static ImagePatrolManager GetInstance()
        {
            lock(lockObj)
            {
                if(_instance == null)
                {
                    _instance = new ImagePatrolManager();
                }
            }

            return _instance;
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 巡检状态锁
        /// </summary>
        [JsonIgnore]
        private static readonly object SyncObj = new object();

        /// <summary>
        /// 开始巡检流程（异步）
        /// </summary>
        public bool StartDoPatrol(eImagePatrolTriggerType patrolTriggerType)
        {
            lock(SyncObj)
            {
                // 当前巡检中，拒绝执行新任务
                if(PatrolState == ePatrolState.巡检中)
                {
                    return false;
                }
            }

            // 创建CancellationTokenSource对象。
            CancelToken = new CancellationTokenSource();

            // 异步执行巡检流程
            Task.Run((Action)(() =>
            {
                try
                {
                    // 新建巡检结果信息
                    LatestImagePatrolResult = new OldImagePatrolResult(patrolTriggerType);

                    lock(SyncObj)
                    {
                        PatrolState = ePatrolState.巡检中;
                    }

                    // 巡检开始
                    OnPatrolStartEvent();

                    // 遍历执行巡检
                    CurrnetUnitIndex = 0;
                    foreach(var model in PatrolUnitList)
                    {
                        // 判断流程有没有被手动取消
                        if(CancelToken.IsCancellationRequested)
                        {
                            throw new Exception();
                        }

                        // 巡检进度更新
                        CurrnetUnitIndex++;

                        // 巡检进度通知
                        OnPatrolStepNotifyEvent(CurrnetUnitIndex);

                        // 执行巡检
                        model.StartPatrol(LatestImagePatrolResult);
                    }

                    // 保存巡检结果信息到数据库
                    LatestImagePatrolResult.SaveToDb();

                    lock(SyncObj)
                    {
                        PatrolState = ePatrolState.巡检完成;
                    }
                }
                catch(OperationCanceledException e)
                {
                    lock(SyncObj)
                    {
                        PatrolState = ePatrolState.巡检异常;
                    }
                    ImagePatrolLogHelper.WritePatrolLog($"图像巡检执行出错：触发中断巡检流程！");
                }
                catch(Exception e)
                {
                    lock(SyncObj)
                    {
                        PatrolState = ePatrolState.巡检异常;
                    }
                    ImagePatrolLogHelper.WritePatrolLog($"图像巡检执行出错：{e.Message}");
                }
                finally
                {
                    // 巡检结束
                    OnPatrolEndEvent();
                }
            }));

            return true;
        }

        /// <summary>
        /// 开始巡检流程（同步）
        /// </summary>
        public bool DoPatrol(eImagePatrolTriggerType patrolTriggerType)
        {
            lock(SyncObj)
            {
                // 当前巡检中，拒绝执行新任务
                if(PatrolState == ePatrolState.巡检中)
                {
                    return false;
                }
            }

            // 创建CancellationTokenSource对象。
            CancelToken = new CancellationTokenSource();

            try
            {
                // 新建巡检结果信息
                LatestImagePatrolResult = new OldImagePatrolResult(patrolTriggerType);

                lock(SyncObj)
                {
                    PatrolState = ePatrolState.巡检中;
                }

                // 巡检开始
                OnPatrolStartEvent();

                // 遍历执行巡检
                CurrnetUnitIndex = 0;
                foreach(var model in PatrolUnitList)
                {
                    // 判断流程有没有被手动取消
                    if(CancelToken.IsCancellationRequested)
                    {
                        throw new Exception();
                    }

                    // 巡检进度更新
                    CurrnetUnitIndex++;

                    // 巡检进度通知
                    OnPatrolStepNotifyEvent(CurrnetUnitIndex);

                    // 执行巡检
                    model.StartPatrol(LatestImagePatrolResult);

                    if(patrolTriggerType == eImagePatrolTriggerType.手动触发)
                    {
                        Thread.Sleep(2000);
                    }
                }

                // 保存巡检结果信息到数据库
                LatestImagePatrolResult.SaveToDb();

                lock(SyncObj)
                {
                    PatrolState = ePatrolState.巡检完成;
                }

                return true;
            }
            catch(OperationCanceledException e)
            {
                lock(SyncObj)
                {
                    PatrolState = ePatrolState.巡检异常;
                }
                ImagePatrolLogHelper.WritePatrolLog($"图像巡检执行出错：触发中断巡检流程！");
                return false;
            }
            catch(Exception e)
            {
                lock(SyncObj)
                {
                    PatrolState = ePatrolState.巡检异常;
                }
                ImagePatrolLogHelper.WritePatrolLog($"图像巡检执行出错：{e.Message}");
                return false;
            }
            finally
            {
                // 保存巡检结果到缓存
                Save();

                // 巡检结束
                OnPatrolEndEvent();
            }
        }

        /// <summary>
        /// 中断巡检流程
        /// </summary>
        public void StopDoPatrol()
        {
            if(PatrolState == ePatrolState.巡检中)
            {
                CancelToken?.Cancel();
            }
        }

        /// <summary>
        /// 构建巡检单元列表
        /// </summary>
        public void RebuildPatrolUnitList()
        {
            PatrolUnitList.Clear();

            foreach(string type in UsedPatrolUnitTypeList.Distinct())
            {
                try
                {
                    if(!string.IsNullOrEmpty(type))
                    {
                        PatrolUnitList.Add((ImageUnitSmartPatrolBase)ReflectionHelper.CreateInstance(type));
                    }
                }
                catch
                {
                }
            }
        }

        #endregion

        #region 公共方法（单独巡检）

        /// <summary>
        /// 配水预处理沉砂池脏污检测
        /// 返回true时表示脏污
        /// </summary>
        /// <returns></returns>
        public void CheckSandSinkSmutState(OldImagePatrolResult patrolResult)
        {
            OnPatrolLogEvent("[配水预处理沉砂池脏污检测]开始检测...");
            try
            {
                if(ExterEquipConfigManager.GetInstance().CameraSelect.SandSinkCamera == null)
                {
                    throw new Exception("对应摄像机未配置！");
                }

                ExterEquipConfigManager.GetInstance().CameraSelect.SandSinkCamera.ScreenShot(out string picPath);
                patrolResult.SandSinkCameraImagePath = FileExtension.GetRelativePath(picPath);
                patrolResult.SandSinkSmutState = AlgorithmHelper.CheckSandSinkSmutState(new Mat(picPath)) ? eSmutState.脏污 : eSmutState.正常;

                OnPatrolLogEvent("[配水预处理沉砂池脏污检测]检测完成。");
            }
            catch(Exception ex)
            {
                OnPatrolLogEvent("[配水预处理沉砂池脏污检测]检测执行出错：" + ex.Message);
            }
        }

        /// <summary>
        /// 配水预处理管路脏污检测
        /// 返回true时表示脏污
        /// </summary>
        /// <returns></returns>
        public void CheckPipeSmutState(OldImagePatrolResult patrolResult)
        {
            OnPatrolLogEvent("[配水预处理管路脏污检测]开始检测...");
            try
            {
                if(ExterEquipConfigManager.GetInstance().CameraSelect.PipeCamera == null)
                {
                    throw new Exception("对应摄像机未配置！");
                }

                ExterEquipConfigManager.GetInstance().CameraSelect.PipeCamera.ScreenShot(out string picPath);
                patrolResult.PipeCameraImagePath = FileExtension.GetRelativePath(picPath);
                patrolResult.PipeSmutState = AlgorithmHelper.CheckPipeSmutState(new Mat(picPath)) ? eSmutState.脏污 : eSmutState.正常;

                OnPatrolLogEvent("[配水预处理管路脏污检测]检测完成。");
            }
            catch(Exception ex)
            {
                OnPatrolLogEvent("[配水预处理管路脏污检测]检测执行出错：" + ex.Message);
            }
        }

        /// <summary>
        /// 五参数流通池脏污检测
        /// 返回true时表示脏污
        /// </summary>
        /// <returns></returns>
        public void CheckFiveParamFlowPoolState(OldImagePatrolResult patrolResult)
        {
            OnPatrolLogEvent("[五参数流通池脏污检测]开始检测...");
            try
            {
                if(ExterEquipConfigManager.GetInstance().CameraSelect.FiveParamFlowPoolCamera == null)
                {
                    throw new Exception("对应摄像机未配置！");
                }

                ExterEquipConfigManager.GetInstance().CameraSelect.FiveParamFlowPoolCamera.ScreenShot(out string picPath);
                patrolResult.FiveParamFlowPoolCameraImagePath = FileExtension.GetRelativePath(picPath);
                patrolResult.FiveParamFlowPoolState = AlgorithmHelper.CheckFiveParamFlowPoolState(new Mat(picPath)) ? eSmutState.脏污 : eSmutState.正常;

                OnPatrolLogEvent("[五参数流通池脏污检测]检测完成。");
            }
            catch(Exception ex)
            {
                OnPatrolLogEvent("[五参数流通池脏污检测]检测执行出错：" + ex.Message);
            }
        }

        /// <summary>
        /// 五参数水桶液位检测
        /// 返回true时表示脏污
        /// </summary>
        /// <returns></returns>
        public void CheckFiveParamBucketState(OldImagePatrolResult patrolResult)
        {
            OnPatrolLogEvent("[五参数水桶液位检测]开始检测...");
            try
            {
                if(ExterEquipConfigManager.GetInstance().CameraSelect.FiveParamBucketCamera == null)
                {
                    throw new Exception("对应摄像机未配置！");
                }

                ExterEquipConfigManager.GetInstance().CameraSelect.FiveParamBucketCamera.ScreenShot(out string picPath);
                patrolResult.FiveParamBucketCameraImagePath = FileExtension.GetRelativePath(picPath);
                AlgorithmHelper.CheckFiveParamBucketState(new Mat(picPath), out bool wasteWaterBucketWarn, out bool wasteTankBucketWarn, out bool waterBucketWarn);
                //patrolResult.FiveParamWasteWaterBucketState = wasteWaterBucketWarn ? eEarlyWarnState.预警 : eEarlyWarnState.正常;
                patrolResult.FiveParamWasteTankBucketState = wasteTankBucketWarn ? eEarlyWarnState.预警 : eEarlyWarnState.正常;
                patrolResult.FiveParamWaterBucketState = waterBucketWarn ? eEarlyWarnState.预警 : eEarlyWarnState.正常;

                OnPatrolLogEvent("[五参数水桶液位检测]检测完成。");
            }
            catch(Exception ex)
            {
                OnPatrolLogEvent("[五参数水桶液位检测]检测执行出错：" + ex.Message);
            }
        }

        /// <summary>
        /// 高指氨氮分析仪状态检测
        /// 返回true时表示脏污
        /// </summary>
        /// <returns></returns>
        public void CheckCodMnNH4EquipState(OldImagePatrolResult patrolResult)
        {
            OnPatrolLogEvent("[高指氨氮分析仪状态检测]开始检测...");
            try
            {
                if(ExterEquipConfigManager.GetInstance().CameraSelect.CodMnNH4EquipCamera == null)
                {
                    throw new Exception("对应摄像机未配置！");
                }

                ExterEquipConfigManager.GetInstance().CameraSelect.CodMnNH4EquipCamera.ScreenShot(out string picPath);
                patrolResult.CodMnNH4EquipCameraImagePath = FileExtension.GetRelativePath(picPath);
                AlgorithmHelper.CheckSIA3900State(new Mat(picPath), out bool leftPipeSmutState, out bool leftReactionUnitSmutState, out bool rightPipeSmutState, out bool rightReactionUnitSmutState);
                patrolResult.CodMnPipeSmutState = leftPipeSmutState ? eSmutState.脏污 : eSmutState.正常;
                patrolResult.CodMnReactionUnitSmutState = leftReactionUnitSmutState ? eSmutState.脏污 : eSmutState.正常;
                patrolResult.NH4PipeSmutState = rightPipeSmutState ? eSmutState.脏污 : eSmutState.正常;
                patrolResult.NH4ReactionUnitSmutState = rightReactionUnitSmutState ? eSmutState.脏污 : eSmutState.正常;

                OnPatrolLogEvent("[高指氨氮分析仪状态检测]检测完成。");
            }
            catch(Exception ex)
            {
                OnPatrolLogEvent("[高指氨氮分析仪状态检测]检测执行出错：" + ex.Message);
            }
        }

        /// <summary>
        /// 总磷总氮分析仪状态检测
        /// 返回true时表示脏污
        /// </summary>
        /// <returns></returns>
        public void CheckTPTNEquipState(OldImagePatrolResult patrolResult)
        {
            OnPatrolLogEvent("[总磷总氮分析仪状态检测]开始检测...");
            try
            {
                if(ExterEquipConfigManager.GetInstance().CameraSelect.TPTNEquipCamera == null)
                {
                    throw new Exception("对应摄像机未配置！");
                }

                ExterEquipConfigManager.GetInstance().CameraSelect.TPTNEquipCamera.ScreenShot(out string picPath);
                patrolResult.TPTNEquipCameraImagePath = FileExtension.GetRelativePath(picPath);
                AlgorithmHelper.CheckSIA3900State(new Mat(picPath), out bool leftPipeSmutState, out bool leftReactionUnitSmutState, out bool rightPipeSmutState, out bool rightReactionUnitSmutState);
                patrolResult.TPPipeSmutState = leftPipeSmutState ? eSmutState.脏污 : eSmutState.正常;
                patrolResult.TPReactionUnitSmutState = leftReactionUnitSmutState ? eSmutState.脏污 : eSmutState.正常;
                patrolResult.TNPipeSmutState = rightPipeSmutState ? eSmutState.脏污 : eSmutState.正常;
                patrolResult.TNReactionUnitSmutState = rightReactionUnitSmutState ? eSmutState.脏污 : eSmutState.正常;

                OnPatrolLogEvent("[总磷总氮分析仪状态检测]检测完成。");
            }
            catch(Exception ex)
            {
                OnPatrolLogEvent("[总磷总氮分析仪状态检测]检测执行出错：" + ex.Message);
            }
        }

        /// <summary>
        /// 高指氨氮质控仪状态检测
        /// 返回true时表示脏污
        /// </summary>
        /// <returns></returns>
        public void CheckCodMnNH4QCDState(OldImagePatrolResult patrolResult)
        {
            OnPatrolLogEvent("[高指氨氮质控仪状态检测]开始检测...");
            try
            {
                if(ExterEquipConfigManager.GetInstance().CameraSelect.CodMnNH4QCDCamera == null)
                {
                    throw new Exception("对应摄像机未配置！");
                }

                ExterEquipConfigManager.GetInstance().CameraSelect.CodMnNH4QCDCamera.ScreenShot(out string picPath);
                patrolResult.CodMnNH4QCDCameraImagePath = FileExtension.GetRelativePath(picPath);
                AlgorithmHelper.CheckQCD3900State(new Mat(picPath), out bool leftWaterTubeSmutState, out bool leftSampleTubeSmutState, out bool leftWaterCupSmutState,
            out bool leftSampleCupSmutState, out bool rightWaterTubeSmutState, out bool rightSampleTubeSmutState, out bool rightWaterCupSmutState,
            out bool rightSampleCupSmutState, out bool wasteWaterBucketWarn, out bool wasteTankBucketWarn, out bool waterBucketWarn);

                patrolResult.CodMnWaterTubeSmutState = leftWaterTubeSmutState ? eSmutState.脏污 : eSmutState.正常;
                patrolResult.CodMnSampleTubeSmutState = leftSampleTubeSmutState ? eSmutState.脏污 : eSmutState.正常;
                patrolResult.CodMnWaterCupSmutState = leftWaterCupSmutState ? eSmutState.脏污 : eSmutState.正常;
                patrolResult.CodMnSampleCupSmutState = leftSampleCupSmutState ? eSmutState.脏污 : eSmutState.正常;
                patrolResult.NH4WaterTubeSmutState = rightWaterTubeSmutState ? eSmutState.脏污 : eSmutState.正常;
                patrolResult.NH4SampleTubeSmutState = rightSampleTubeSmutState ? eSmutState.脏污 : eSmutState.正常;
                patrolResult.NH4WaterCupSmutState = rightWaterCupSmutState ? eSmutState.脏污 : eSmutState.正常;
                patrolResult.NH4SampleCupSmutState = rightSampleCupSmutState ? eSmutState.脏污 : eSmutState.正常;
                patrolResult.CodMnNH4WasteWaterBucketState = wasteWaterBucketWarn ? eEarlyWarnState.预警 : eEarlyWarnState.正常;
                patrolResult.CodMnNH4WasteTankBucketState = wasteTankBucketWarn ? eEarlyWarnState.预警 : eEarlyWarnState.正常;
                patrolResult.CodMnNH4WaterBucketState = waterBucketWarn ? eEarlyWarnState.预警 : eEarlyWarnState.正常;

                OnPatrolLogEvent("[高指氨氮质控仪状态检测]检测执行出错！");
            }
            catch(Exception ex)
            {
                OnPatrolLogEvent("[高指氨氮质控仪状态检测]出错：" + ex.Message);
            }
        }

        /// <summary>
        /// 总磷总氮质控仪状态检测
        /// 返回true时表示脏污
        /// </summary>
        /// <returns></returns>
        public void CheckTPTNQCDState(OldImagePatrolResult patrolResult)
        {
            OnPatrolLogEvent("[总磷总氮质控仪状态检测]开始检测...");
            try
            {
                if(ExterEquipConfigManager.GetInstance().CameraSelect.TPTNQCDCamera == null)
                {
                    throw new Exception("对应摄像机未配置！");
                }

                ExterEquipConfigManager.GetInstance().CameraSelect.TPTNQCDCamera.ScreenShot(out string picPath);
                patrolResult.TPTNQCDCameraImagePath = FileExtension.GetRelativePath(picPath);
                AlgorithmHelper.CheckQCD3900State(new Mat(picPath), out bool leftWaterTubeSmutState, out bool leftSampleTubeSmutState, out bool leftWaterCupSmutState,
            out bool leftSampleCupSmutState, out bool rightWaterTubeSmutState, out bool rightSampleTubeSmutState, out bool rightWaterCupSmutState,
            out bool rightSampleCupSmutState, out bool wasteWaterBucketWarn, out bool wasteTankBucketWarn, out bool waterBucketWarn);

                patrolResult.TPWaterTubeSmutState = leftWaterTubeSmutState ? eSmutState.脏污 : eSmutState.正常;
                patrolResult.TPSampleTubeSmutState = leftSampleTubeSmutState ? eSmutState.脏污 : eSmutState.正常;
                patrolResult.TPWaterCupSmutState = leftWaterCupSmutState ? eSmutState.脏污 : eSmutState.正常;
                patrolResult.TPSampleCupSmutState = leftSampleCupSmutState ? eSmutState.脏污 : eSmutState.正常;
                patrolResult.TNWaterTubeSmutState = rightWaterTubeSmutState ? eSmutState.脏污 : eSmutState.正常;
                patrolResult.TNSampleTubeSmutState = rightSampleTubeSmutState ? eSmutState.脏污 : eSmutState.正常;
                patrolResult.TNWaterCupSmutState = rightWaterCupSmutState ? eSmutState.脏污 : eSmutState.正常;
                patrolResult.TNSampleCupSmutState = rightSampleCupSmutState ? eSmutState.脏污 : eSmutState.正常;
                patrolResult.TPTNWasteWaterBucketState = wasteWaterBucketWarn ? eEarlyWarnState.预警 : eEarlyWarnState.正常;
                patrolResult.TPTNWasteTankBucketState = wasteTankBucketWarn ? eEarlyWarnState.预警 : eEarlyWarnState.正常;
                patrolResult.TPTNWaterBucketState = waterBucketWarn ? eEarlyWarnState.预警 : eEarlyWarnState.正常;

                OnPatrolLogEvent("[总磷总氮质控仪状态检测]检测完成。");
            }
            catch(Exception ex)
            {
                OnPatrolLogEvent("[总磷总氮质控仪状态检测]检测执行出错：" + ex.Message);
            }
        }

        #endregion

        #region Event

        #region 整体

        /// <summary>
        /// 巡检开始
        /// </summary>
        public event Action PatrolStartEvent;

        /// <summary>
        /// 巡检结束
        /// </summary>
        public event Action PatrolEndEvent;

        /// <summary>
        /// 巡检日志
        /// </summary>
        public event Action<string> PatrolLogEvent;

        /// <summary>
        /// 巡检开始
        /// </summary>
        /// <param name="model"></param>
        private void OnPatrolStartEvent()
        {
            try
            {
                if(PatrolStartEvent != null)
                {
                    PatrolStartEvent();
                }
            }
            catch
            {
            }
        }

        /// <summary>
        /// 巡检结束
        /// </summary>
        /// <param name="model"></param>
        private void OnPatrolEndEvent()
        {
            try
            {
                if(PatrolEndEvent != null)
                {
                    PatrolEndEvent();
                }
            }
            catch
            {
            }
        }

        /// <summary>
        /// 巡检日志
        /// </summary>
        /// <param name="model"></param>
        public void OnPatrolLogEvent(string log)
        {
            try
            {
                if(PatrolLogEvent != null)
                {
                    PatrolLogEvent(log);
                }
            }
            catch
            {
            }
        }

        #endregion

        #region 单元

        /// <summary>
        /// 巡检进度通知
        /// </summary>
        public event Action<int> PatrolStepNotifyEvent;

        /// <summary>
        /// 巡检进度通知
        /// </summary>
        /// <param name="model"></param>
        private void OnPatrolStepNotifyEvent(int step)
        {
            try
            {
                if(PatrolStepNotifyEvent != null)
                {
                    PatrolStepNotifyEvent(step);
                }
            }
            catch
            {
            }
        }

        #endregion

        /// <summary>
        /// 清空流程状态事件通知
        /// </summary>
        public void ClearEvent()
        {
            if(PatrolStartEvent != null)
            {
                Delegate[] delegAry = PatrolStartEvent.GetInvocationList();
                foreach(Delegate @delegate in delegAry)
                {
                    var deleg = (Action)@delegate;
                    PatrolStartEvent -= deleg;
                }
            }

            if(PatrolEndEvent != null)
            {
                Delegate[] delegAry = PatrolEndEvent.GetInvocationList();
                foreach(Delegate @delegate in delegAry)
                {
                    var deleg = (Action)@delegate;
                    PatrolEndEvent -= deleg;
                }
            }

            if(PatrolLogEvent != null)
            {
                Delegate[] delegAry = PatrolLogEvent.GetInvocationList();
                foreach(Delegate @delegate in delegAry)
                {
                    var deleg = (Action<string>)@delegate;
                    PatrolLogEvent -= deleg;
                }
            }

            if(PatrolStepNotifyEvent != null)
            {
                Delegate[] delegAry = PatrolStepNotifyEvent.GetInvocationList();
                foreach(Delegate @delegate in delegAry)
                {
                    var deleg = (Action<int>)@delegate;
                    PatrolStepNotifyEvent -= deleg;
                }
            }
        }

        #endregion
    }
}