﻿using System.Collections.Generic;
using System.ComponentModel;
using Fpi.WMS3000.Equipment;

namespace Fpi.WMS3000.SystemConfig.ImagePatrol.Config
{
    /// <summary>
    /// 配水预处理管路巡检结果
    /// </summary>
    public class PipePatrolResult : ImageUnitPatrolResultBase
    {
        #region 字段属性

        /// <summary>
        /// 配水预处理管路脏污状态
        /// </summary>
        [Description("配水预处理管路脏污状态")]
        public eSmutState PipeSmutState { get; set; }

        #endregion

        #region 构造

        public PipePatrolResult()
        {
            UnitId = "Pipe";
            UnitName = "配水预处理管路";
        }

        #endregion

        #region 方法重写

        /// <summary>
        /// 打印巡检结果
        /// </summary>
        /// <returns></returns>
        public override List<string> GetResultStr()
        {
            return new List<string>
            {
                $"配水预处理管路脏污状态：{PipeSmutState}"
            };
        }

        #endregion
    }
}