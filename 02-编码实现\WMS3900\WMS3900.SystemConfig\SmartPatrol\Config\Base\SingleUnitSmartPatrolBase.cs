﻿using System;
using System.ComponentModel;
using Fpi.WMS3000.SystemConfig.SmartPatrol.Helper;

namespace Fpi.WMS3000.SystemConfig.SmartPatrol.Config
{
    /// <summary>
    /// 一个单元智能巡检基类
    /// 定义巡检方法
    /// </summary>
    public class SingleUnitSmartPatrolBase
    {
        #region 字段属性

        ///// <summary>
        ///// 单元执行顺序
        ///// 数字越小，执行越靠前
        ///// </summary>
        //[Description("单元执行顺序")]
        //public int UnitOrder;

        /// <summary>
        /// 单元编码
        /// </summary>
        [Description("单元编码")]
        public string UnitId;

        /// <summary>
        /// 单元名称
        /// </summary>
        [Description("单元名称")]
        public string UnitName;

        /// <summary>
        /// 巡检状态
        /// </summary>
        [Description("巡检状态")]
        public ePatrolState PatrolState;

        /// <summary>
        /// 巡检结果
        /// </summary>
        [Description("巡检结果")]
        public SingleUnitPatrolResultBase PatrolResult;

        #endregion

        #region 公共方法（待重写）

        /// <summary>
        /// 执行巡检任务
        /// </summary>
        /// <returns></returns>
        public virtual SingleUnitPatrolResultBase ExecutePatrol()
        {
            return null;
        }

        #endregion

        #region 公共方法（待重写）

        /// <summary>
        /// 执行巡检任务
        /// </summary>
        /// <returns></returns>
        public virtual void StartPatrol()
        {
            try
            {
                SmartPatrolLogHelper.WritePatrolLog($"[{UnitName}]开始巡检...");
                PatrolState = ePatrolState.巡检中;

                PatrolResult = ExecutePatrol();

                // 通过有没有诊断异常项，来判定诊断结果
                if(PatrolResult != null && !string.IsNullOrEmpty(InspectionStructureBuilder.GetSingleUnitErrorResultStr(PatrolResult)))
                {
                    PatrolResult.PatrolResult = ePatrolResult.异常;
                }

                PatrolState = ePatrolState.巡检完成;
                SmartPatrolLogHelper.WritePatrolLog($"[{UnitName}]巡检完成。");
            }
            catch(Exception e)
            {
                SmartPatrolLogHelper.WritePatrolLog($"[{UnitName}]巡检执行出错：{e.Message}");
                PatrolState = ePatrolState.巡检异常;
            }
        }

        #endregion

        public override string ToString()
        {
            return UnitName;
        }
    }
}