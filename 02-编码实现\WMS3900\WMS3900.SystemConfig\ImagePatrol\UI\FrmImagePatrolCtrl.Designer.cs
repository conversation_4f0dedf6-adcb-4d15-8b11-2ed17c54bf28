﻿using System.Drawing;

namespace Fpi.WMS3000.SystemConfig
{
    partial class FrmImagePatrolCtrl
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FrmImagePatrolCtrl));
            this.gbPatrolCtrl = new Sunny.UI.UIGroupBox();
            this.lblTriggerType = new Sunny.UI.UILabel();
            this.uiLabel4 = new Sunny.UI.UILabel();
            this.stepCtrl = new HZH_Controls.Controls.UCStep();
            this.lblPatrolState = new Sunny.UI.UILabel();
            this.lblStartTime = new Sunny.UI.UILabel();
            this.btnTimerConfig = new Sunny.UI.UIButton();
            this.btnOpenResult = new Sunny.UI.UIButton();
            this.uiLabel2 = new Sunny.UI.UILabel();
            this.uiLabel1 = new Sunny.UI.UILabel();
            this.btnStartDo = new Sunny.UI.UIButton();
            this.gifAvatar = new Sunny.UI.UIGifAvatar();
            this.txtResultInfo = new Sunny.UI.UIRichTextBox();
            this.gbProcessInfo = new Sunny.UI.UIGroupBox();
            this.btnParamConfig = new Sunny.UI.UIButton();
            this.gbPatrolCtrl.SuspendLayout();
            this.gbProcessInfo.SuspendLayout();
            this.SuspendLayout();
            // 
            // gbPatrolCtrl
            // 
            this.gbPatrolCtrl.Controls.Add(this.btnParamConfig);
            this.gbPatrolCtrl.Controls.Add(this.lblTriggerType);
            this.gbPatrolCtrl.Controls.Add(this.uiLabel4);
            this.gbPatrolCtrl.Controls.Add(this.stepCtrl);
            this.gbPatrolCtrl.Controls.Add(this.lblPatrolState);
            this.gbPatrolCtrl.Controls.Add(this.lblStartTime);
            this.gbPatrolCtrl.Controls.Add(this.btnTimerConfig);
            this.gbPatrolCtrl.Controls.Add(this.btnOpenResult);
            this.gbPatrolCtrl.Controls.Add(this.uiLabel2);
            this.gbPatrolCtrl.Controls.Add(this.uiLabel1);
            this.gbPatrolCtrl.Controls.Add(this.btnStartDo);
            this.gbPatrolCtrl.Controls.Add(this.gifAvatar);
            this.gbPatrolCtrl.Dock = System.Windows.Forms.DockStyle.Top;
            this.gbPatrolCtrl.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.gbPatrolCtrl.Location = new System.Drawing.Point(2, 35);
            this.gbPatrolCtrl.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.gbPatrolCtrl.MinimumSize = new System.Drawing.Size(1, 1);
            this.gbPatrolCtrl.Name = "gbPatrolCtrl";
            this.gbPatrolCtrl.Padding = new System.Windows.Forms.Padding(0, 32, 0, 0);
            this.gbPatrolCtrl.Size = new System.Drawing.Size(1161, 167);
            this.gbPatrolCtrl.TabIndex = 0;
            this.gbPatrolCtrl.Text = "巡检控制";
            this.gbPatrolCtrl.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // lblTriggerType
            // 
            this.lblTriggerType.AutoSize = true;
            this.lblTriggerType.BackColor = System.Drawing.Color.Transparent;
            this.lblTriggerType.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblTriggerType.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.lblTriggerType.Location = new System.Drawing.Point(498, 40);
            this.lblTriggerType.Name = "lblTriggerType";
            this.lblTriggerType.Size = new System.Drawing.Size(52, 21);
            this.lblTriggerType.TabIndex = 10;
            this.lblTriggerType.Text = "------";
            // 
            // uiLabel4
            // 
            this.uiLabel4.AutoSize = true;
            this.uiLabel4.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel4.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel4.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel4.Location = new System.Drawing.Point(405, 40);
            this.uiLabel4.Name = "uiLabel4";
            this.uiLabel4.Size = new System.Drawing.Size(90, 21);
            this.uiLabel4.TabIndex = 9;
            this.uiLabel4.Text = "触发方式：";
            // 
            // stepCtrl
            // 
            this.stepCtrl.BackColor = System.Drawing.Color.Transparent;
            this.stepCtrl.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.stepCtrl.Font = new System.Drawing.Font("微软雅黑", 10F);
            this.stepCtrl.ImgCompleted = ((System.Drawing.Image)(resources.GetObject("stepCtrl.ImgCompleted")));
            this.stepCtrl.LineWidth = 10;
            this.stepCtrl.Location = new System.Drawing.Point(0, 90);
            this.stepCtrl.Name = "stepCtrl";
            this.stepCtrl.Size = new System.Drawing.Size(1161, 77);
            this.stepCtrl.StepBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(189)))), ((int)(((byte)(189)))), ((int)(((byte)(189)))));
            this.stepCtrl.StepFontColor = System.Drawing.Color.White;
            this.stepCtrl.StepForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(77)))), ((int)(((byte)(59)))));
            this.stepCtrl.StepIndex = 0;
            this.stepCtrl.Steps = new string[] {
        "开始",
        "step1",
        "step2",
        "step3"};
            this.stepCtrl.StepWidth = 35;
            this.stepCtrl.TabIndex = 11;
            // 
            // lblPatrolState
            // 
            this.lblPatrolState.AutoSize = true;
            this.lblPatrolState.BackColor = System.Drawing.Color.Transparent;
            this.lblPatrolState.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblPatrolState.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.lblPatrolState.Location = new System.Drawing.Point(105, 40);
            this.lblPatrolState.Name = "lblPatrolState";
            this.lblPatrolState.Size = new System.Drawing.Size(52, 21);
            this.lblPatrolState.TabIndex = 6;
            this.lblPatrolState.Text = "------";
            // 
            // lblStartTime
            // 
            this.lblStartTime.AutoSize = true;
            this.lblStartTime.BackColor = System.Drawing.Color.Transparent;
            this.lblStartTime.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.lblStartTime.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.lblStartTime.Location = new System.Drawing.Point(269, 40);
            this.lblStartTime.Name = "lblStartTime";
            this.lblStartTime.Size = new System.Drawing.Size(52, 21);
            this.lblStartTime.TabIndex = 8;
            this.lblStartTime.Text = "------";
            // 
            // btnTimerConfig
            // 
            this.btnTimerConfig.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnTimerConfig.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnTimerConfig.Location = new System.Drawing.Point(945, 33);
            this.btnTimerConfig.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnTimerConfig.Name = "btnTimerConfig";
            this.btnTimerConfig.Size = new System.Drawing.Size(100, 35);
            this.btnTimerConfig.TabIndex = 2;
            this.btnTimerConfig.Text = "定时巡检设置";
            this.btnTimerConfig.TipsFont = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnTimerConfig.Click += new System.EventHandler(this.btnTimerConfig_Click);
            // 
            // btnOpenResult
            // 
            this.btnOpenResult.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnOpenResult.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnOpenResult.Location = new System.Drawing.Point(836, 33);
            this.btnOpenResult.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnOpenResult.Name = "btnOpenResult";
            this.btnOpenResult.Size = new System.Drawing.Size(100, 35);
            this.btnOpenResult.TabIndex = 1;
            this.btnOpenResult.Text = "查看结果";
            this.btnOpenResult.TipsFont = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnOpenResult.Click += new System.EventHandler(this.btnOpenResult_Click);
            // 
            // uiLabel2
            // 
            this.uiLabel2.AutoSize = true;
            this.uiLabel2.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel2.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel2.Location = new System.Drawing.Point(12, 40);
            this.uiLabel2.Name = "uiLabel2";
            this.uiLabel2.Size = new System.Drawing.Size(90, 21);
            this.uiLabel2.TabIndex = 5;
            this.uiLabel2.Text = "巡检状态：";
            // 
            // uiLabel1
            // 
            this.uiLabel1.AutoSize = true;
            this.uiLabel1.BackColor = System.Drawing.Color.Transparent;
            this.uiLabel1.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.uiLabel1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(48)))), ((int)(((byte)(48)))));
            this.uiLabel1.Location = new System.Drawing.Point(176, 40);
            this.uiLabel1.Name = "uiLabel1";
            this.uiLabel1.Size = new System.Drawing.Size(90, 21);
            this.uiLabel1.TabIndex = 7;
            this.uiLabel1.Text = "开始时间：";
            // 
            // btnStartDo
            // 
            this.btnStartDo.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnStartDo.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnStartDo.Location = new System.Drawing.Point(656, 33);
            this.btnStartDo.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnStartDo.Name = "btnStartDo";
            this.btnStartDo.Size = new System.Drawing.Size(100, 35);
            this.btnStartDo.TabIndex = 0;
            this.btnStartDo.Text = "开始";
            this.btnStartDo.TipsFont = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnStartDo.Click += new System.EventHandler(this.btnStartDo_Click);
            // 
            // gifAvatar
            // 
            this.gifAvatar.AvatarSize = 180;
            this.gifAvatar.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(243)))), ((int)(((byte)(249)))), ((int)(((byte)(255)))));
            this.gifAvatar.FillColor = System.Drawing.Color.FromArgb(((int)(((byte)(243)))), ((int)(((byte)(249)))), ((int)(((byte)(255)))));
            this.gifAvatar.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.gifAvatar.Image = global::Fpi.WMS3000.SystemConfig.Properties.Resources._4;
            this.gifAvatar.Location = new System.Drawing.Point(766, 18);
            this.gifAvatar.MinimumSize = new System.Drawing.Size(1, 1);
            this.gifAvatar.Name = "gifAvatar";
            this.gifAvatar.RectColor = System.Drawing.Color.FromArgb(((int)(((byte)(243)))), ((int)(((byte)(249)))), ((int)(((byte)(255)))));
            this.gifAvatar.Size = new System.Drawing.Size(64, 64);
            this.gifAvatar.TabIndex = 4;
            // 
            // txtResultInfo
            // 
            this.txtResultInfo.Dock = System.Windows.Forms.DockStyle.Fill;
            this.txtResultInfo.FillColor = System.Drawing.Color.White;
            this.txtResultInfo.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.txtResultInfo.Location = new System.Drawing.Point(1, 32);
            this.txtResultInfo.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.txtResultInfo.MinimumSize = new System.Drawing.Size(1, 1);
            this.txtResultInfo.Name = "txtResultInfo";
            this.txtResultInfo.Padding = new System.Windows.Forms.Padding(2);
            this.txtResultInfo.RadiusSides = Sunny.UI.UICornerRadiusSides.None;
            this.txtResultInfo.RectSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.None;
            this.txtResultInfo.ShowText = false;
            this.txtResultInfo.Size = new System.Drawing.Size(1159, 563);
            this.txtResultInfo.TabIndex = 0;
            this.txtResultInfo.TextAlignment = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // gbProcessInfo
            // 
            this.gbProcessInfo.Controls.Add(this.txtResultInfo);
            this.gbProcessInfo.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gbProcessInfo.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.gbProcessInfo.Location = new System.Drawing.Point(2, 202);
            this.gbProcessInfo.Margin = new System.Windows.Forms.Padding(4, 5, 4, 5);
            this.gbProcessInfo.MinimumSize = new System.Drawing.Size(1, 1);
            this.gbProcessInfo.Name = "gbProcessInfo";
            this.gbProcessInfo.Padding = new System.Windows.Forms.Padding(1, 32, 1, 1);
            this.gbProcessInfo.Size = new System.Drawing.Size(1161, 596);
            this.gbProcessInfo.TabIndex = 1;
            this.gbProcessInfo.Text = "执行日志";
            this.gbProcessInfo.TextAlignment = System.Drawing.ContentAlignment.MiddleLeft;
            // 
            // btnParamConfig
            // 
            this.btnParamConfig.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnParamConfig.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnParamConfig.Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnParamConfig.Location = new System.Drawing.Point(1054, 33);
            this.btnParamConfig.MinimumSize = new System.Drawing.Size(1, 1);
            this.btnParamConfig.Name = "btnParamConfig";
            this.btnParamConfig.Size = new System.Drawing.Size(100, 35);
            this.btnParamConfig.TabIndex = 3;
            this.btnParamConfig.Text = "参数设置";
            this.btnParamConfig.TipsFont = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnParamConfig.Click += new System.EventHandler(this.btnParamConfig_Click);
            // 
            // FrmImagePatrolCtrl
            // 
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.None;
            this.BackColor = System.Drawing.Color.White;
            this.ClientSize = new System.Drawing.Size(1165, 800);
            this.Controls.Add(this.gbProcessInfo);
            this.Controls.Add(this.gbPatrolCtrl);
            this.EscClose = true;
            this.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(15)))), ((int)(((byte)(68)))), ((int)(((byte)(134)))));
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "FrmImagePatrolCtrl";
            this.Padding = new System.Windows.Forms.Padding(2, 35, 2, 2);
            this.ShowIcon = false;
            this.ShowInTaskbar = false;
            this.ShowRadius = true;
            this.ShowShadow = false;
            this.Text = "图像巡检";
            this.ZoomScaleRect = new System.Drawing.Rectangle(15, 15, 800, 600);
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.FrmImagePatrolCtrl_FormClosing);
            this.Load += new System.EventHandler(this.FrmImagePatrolCtrl_Load);
            this.gbPatrolCtrl.ResumeLayout(false);
            this.gbPatrolCtrl.PerformLayout();
            this.gbProcessInfo.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion
        private Sunny.UI.UIGroupBox gbPatrolCtrl;
        private Sunny.UI.UIGifAvatar gifAvatar;
        private Sunny.UI.UIButton btnStartDo;
        private Sunny.UI.UIRichTextBox txtResultInfo;
        private Sunny.UI.UILabel uiLabel2;
        private Sunny.UI.UILabel uiLabel1;
        private Sunny.UI.UIButton btnOpenResult;
        private Sunny.UI.UIGroupBox gbProcessInfo;
        private Sunny.UI.UIButton btnTimerConfig;
        private Sunny.UI.UILabel lblPatrolState;
        private Sunny.UI.UILabel lblStartTime;
        private HZH_Controls.Controls.UCStep stepCtrl;
        private Sunny.UI.UILabel lblTriggerType;
        private Sunny.UI.UILabel uiLabel4;
        private Sunny.UI.UIButton btnParamConfig;
    }
}