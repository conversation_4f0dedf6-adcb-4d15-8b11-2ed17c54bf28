﻿using System;
using Fpi.Devices;
using Fpi.WMS3000.DB;
using Fpi.WMS3000.Equipment.SIA3900;
using Sunny.UI;

namespace Fpi.WMS3000.Equipment.UI
{
    /// <summary>
    /// SIA3900谱育水质智能化仪表 关键参数显示
    /// </summary>
    public partial class FrmSIA3900KeyParams : UIForm
    {
        #region 字段属性

        private SIA3900DeviceKeyParams _sIA3900DeviceKeyParams;

        #endregion

        #region 构造

        public FrmSIA3900KeyParams()
        {
            InitializeComponent();
        }

        public FrmSIA3900KeyParams(SIA3900DeviceKeyParams sIA3900DeviceKeyParams, Device device, DateTime dataTime) : this()
        {
            _sIA3900DeviceKeyParams = sIA3900DeviceKeyParams;
            this.Text = $"{device?.name}关键参数";
            lblNodeName.Text = device?.GetDevTargetPolNode()?.name;
            lblDataTime.Text = dataTime.ToString(DbConfig.DATETIME_FORMAT);
            CommonFunctionHelper.ReflectAttributeToUI(pnlMain, _sIA3900DeviceKeyParams);
        }

        #endregion
    }
}