﻿using Fpi.Communication.Protocols;
using Fpi.HB.Business.Protocols;

namespace Fpi.WMS3000.Remote.FPISZHHttp
{
    /// <summary>
    /// 谱育科技数智化水站数据传输协议（Http）
    /// </summary>
    public class FPISZHHttpProtocol : RemoteProtocol
    {
        /// <summary>
        /// 协议名称
        /// </summary>
        public override string FriendlyName => "谱育科技数智化水站数据传输协议（Http）";

        /// <summary>
        /// 解析器
        /// </summary>
        /// <returns></returns>
        protected override Parser ConstructParser()
        {
            return new SimpleParser();
        }

        /// <summary>
        /// 描述
        /// </summary>
        /// <returns></returns>
        protected override ProtocolDesc ConstructProtocolDesc()
        {
            return new FPISZHHttpProtocolDesc();
        }

        /// <summary>
        /// 发送器
        /// </summary>
        /// <returns></returns>
        protected override Sender ConstructSender()
        {
            return new FPISZHHttpSender();
        }
    }
}
