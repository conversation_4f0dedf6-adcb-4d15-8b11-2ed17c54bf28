﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using Fpi.Alarm;
using Fpi.Devices;
using Fpi.Util.EnumRelated;
using Fpi.WMS3000.Equipment;
using Fpi.WMS3000.Equipment.Config;
using Fpi.WMS3000.Equipment.QCD3900;
using Fpi.WMS3000.SystemConfig.ImagePatrol.Config;

namespace Fpi.WMS3000.SystemConfig.SmartPatrol.Config
{
    /// <summary>
    /// 质控仪巡检结果
    /// </summary>
    public class QCDEquipResult : SubModelResultBase
    {
        #region 字段属性

        #region 仪表状态

        /// <summary>
        /// 通讯状态
        /// </summary>
        [Description("通讯状态")]
        public eModuleWorkingState ComState { get; set; }

        /// <summary>
        /// 系统状态
        /// </summary>
        [Description("系统状态")]
        public eQCD3900DeviceState SystemState { get; set; }

        /// <summary>
        /// 报警状态
        /// </summary>
        [Description("报警状态")]
        public eModuleWorkingState AlarmState { get; set; }

        /// <summary>
        /// 当前报警列表
        /// </summary>
        [Description("当前报警列表")]
        public List<string> CurrentAlarmList { get; set; } = new List<string>();

        ///// <summary>
        ///// 器件自检结果
        ///// 异常时显示（自检类型，自检时间，是否正常）
        ///// 暂不实现
        ///// </summary>
        //[Description("器件自检结果")]
        //public eModuleWorkingState SelfCheckResult { get; set; }

        #endregion

        #region 脏污状态

        /// <summary>
        /// 质控水样管脏污
        /// </summary>
        [Description("质控水样管脏污")]
        public eSmutState WaterTubeSmutState { get; set; }

        /// <summary>
        /// 质控标样管脏污
        /// </summary>
        [Description("质控标样管脏污")]
        public eSmutState SampleTubeSmutState { get; set; }

        /// <summary>
        /// 质控水样杯脏污
        /// </summary>
        [Description("质控水样杯脏污")]
        public eSmutState WaterCupSmutState { get; set; }

        /// <summary>
        /// 质控标样杯脏污
        /// </summary>
        [Description("质控标样杯脏污")]
        public eSmutState SampleCupSmutState { get; set; }

        #endregion

        #region 器件使用信息

        /// <summary>
        /// 水样管使用信息
        /// </summary>
        [Description("水样管使用信息")]
        public ElectrodeUsageStatistics WaterTubeUsageState { get; set; } = new ElectrodeUsageStatistics("水样管");

        /// <summary>
        /// 储液环使用信息
        /// </summary>
        [Description("储液环使用信息")]
        public ElectrodeUsageStatistics ReceiverRingUsageState { get; set; } = new ElectrodeUsageStatistics("储液环");

        /// <summary>
        /// 液位检测器1使用信息
        /// </summary>
        [Description("液位检测器1使用信息")]
        public ElectrodeUsageStatistics LevelDetector1UsageState { get; set; } = new ElectrodeUsageStatistics("液位检测器1");

        /// <summary>
        /// 液位检测器2使用信息
        /// </summary>
        [Description("液位检测器2使用信息")]
        public ElectrodeUsageStatistics LevelDetector2UsageState { get; set; } = new ElectrodeUsageStatistics("液位检测器2");

        /// <summary>
        /// 电磁阀组1使用信息
        /// </summary>
        [Description("电磁阀组1使用信息")]
        public KeyDeviceUsageState ValveGroup1UsageState { get; set; } = new KeyDeviceUsageState("电磁阀组1");

        /// <summary>
        /// 电磁阀组2使用信息
        /// </summary>
        [Description("电磁阀组2使用信息")]
        public KeyDeviceUsageState ValveGroup2UsageState { get; set; } = new KeyDeviceUsageState("电磁阀组2");

        /// <summary>
        /// 纯水泵PU1使用信息
        /// </summary>
        [Description("纯水泵PU1使用信息")]
        public KeyDeviceUsageState Pump1UsageState { get; set; } = new KeyDeviceUsageState("纯水泵PU1");

        /// <summary>
        /// 定容泵PU2使用信息
        /// </summary>
        [Description("定容泵PU2使用信息")]
        public KeyDeviceUsageState Pump2UsageState { get; set; } = new KeyDeviceUsageState("定容泵PU2");

        /// <summary>
        /// 样品杯气泵PU3使用信息
        /// </summary>
        [Description("样品杯气泵PU3使用信息")]
        public KeyDeviceUsageState Pump3UsageState { get; set; } = new KeyDeviceUsageState("样品杯气泵PU3");

        /// <summary>
        /// 标样杯气泵PU4使用信息
        /// </summary>
        [Description("标样杯气泵PU4使用信息")]
        public KeyDeviceUsageState Pump4UsageState { get; set; } = new KeyDeviceUsageState("标样杯气泵PU4");

        /// <summary>
        /// 原水夹管阀SV1使用信息
        /// </summary>
        [Description("原水夹管阀SV1使用信息")]
        public KeyDeviceUsageState SV1UsageState { get; set; } = new KeyDeviceUsageState("原水夹管阀SV1");

        /// <summary>
        /// 水样杯夹管阀SV2使用信息
        /// </summary>
        [Description("水样杯夹管阀SV2使用信息")]
        public KeyDeviceUsageState SV2UsageState { get; set; } = new KeyDeviceUsageState("水样杯夹管阀SV2");

        /// <summary>
        /// 三通电磁阀SV3使用信息
        /// </summary>
        [Description("三通电磁阀SV3使用信息")]
        public KeyDeviceUsageState SV3UsageState { get; set; } = new KeyDeviceUsageState("三通电磁阀SV3");

        /// <summary>
        /// 三通电磁阀SV4使用信息
        /// </summary>
        [Description("三通电磁阀SV4使用信息")]
        public KeyDeviceUsageState SV4UsageState { get; set; } = new KeyDeviceUsageState("三通电磁阀SV4");

        /// <summary>
        /// 标样杯夹管阀SV5使用信息
        /// </summary>
        [Description("标样杯夹管阀SV5使用信息")]
        public KeyDeviceUsageState SV5UsageState { get; set; } = new KeyDeviceUsageState("标样杯夹管阀SV5");

        /// <summary>
        /// 柱塞泵使用信息
        /// </summary>
        [Description("柱塞泵使用信息")]
        public KeyDeviceUsageState PlungerPumpUsageState { get; set; } = new KeyDeviceUsageState("柱塞泵");

        #endregion

        #endregion

        #region 构造

        public QCDEquipResult()
        {
            ModelName = "质控仪";
        }

        public QCDEquipResult(QCD3900Equip qcdEquip)
        {
            if(qcdEquip == null)
            {
                return;
            }

            if(Enum.TryParse(qcdEquip.TypeDesc, out eDeviceMeasureType deviceType))
            {
                ModelName = EnumOperate.GetEnumDesc(deviceType) + "质控仪";
            }
            else
            {
                ModelName = qcdEquip.name;
            }

            #region 仪表状态

            ComState = qcdEquip.IsComStateError() ? eModuleWorkingState.异常 : eModuleWorkingState.正常;
            SystemState = qcdEquip.DeviceStateParams.DeviceState;
            AlarmState = qcdEquip.IsAlarmExceptComError() ? eModuleWorkingState.异常 : eModuleWorkingState.正常;
            if(AlarmState == eModuleWorkingState.异常)
            {
                CurrentAlarmList = AlarmManager.GetInstance().GetCurrentAlarms().
                    Where(x => x.AlarmSource.id == qcdEquip.AlarmSourceId && x.AlarmCode.id != qcdEquip.ComErrorAlarmCodeId).
                    Select(x => x.AlarmCode.description).
                    Distinct().
                    ToList();
            }

            #endregion

            #region 脏污状态

            if(qcdEquip.TypeDesc == eDeviceMeasureType.CODMn.ToString())
            {
                WaterTubeSmutState = ImagePatrolManager.GetInstance().LatestImagePatrolResult.CodMnWaterTubeSmutState;
                SampleTubeSmutState = ImagePatrolManager.GetInstance().LatestImagePatrolResult.CodMnSampleTubeSmutState;
                WaterCupSmutState = ImagePatrolManager.GetInstance().LatestImagePatrolResult.CodMnWaterCupSmutState;
                SampleCupSmutState = ImagePatrolManager.GetInstance().LatestImagePatrolResult.CodMnSampleCupSmutState;
            }
            else if(qcdEquip.TypeDesc == eDeviceMeasureType.NH4.ToString())
            {
                WaterTubeSmutState = ImagePatrolManager.GetInstance().LatestImagePatrolResult.NH4WaterTubeSmutState;
                SampleTubeSmutState = ImagePatrolManager.GetInstance().LatestImagePatrolResult.NH4SampleTubeSmutState;
                WaterCupSmutState = ImagePatrolManager.GetInstance().LatestImagePatrolResult.NH4WaterCupSmutState;
                SampleCupSmutState = ImagePatrolManager.GetInstance().LatestImagePatrolResult.NH4SampleCupSmutState;
            }
            else if(qcdEquip.TypeDesc == eDeviceMeasureType.TP.ToString())
            {
                WaterTubeSmutState = ImagePatrolManager.GetInstance().LatestImagePatrolResult.TPWaterTubeSmutState;
                SampleTubeSmutState = ImagePatrolManager.GetInstance().LatestImagePatrolResult.TPSampleTubeSmutState;
                WaterCupSmutState = ImagePatrolManager.GetInstance().LatestImagePatrolResult.TPWaterCupSmutState;
                SampleCupSmutState = ImagePatrolManager.GetInstance().LatestImagePatrolResult.TPSampleCupSmutState;
            }
            else if(qcdEquip.TypeDesc == eDeviceMeasureType.TN.ToString())
            {
                WaterTubeSmutState = ImagePatrolManager.GetInstance().LatestImagePatrolResult.TNWaterTubeSmutState;
                SampleTubeSmutState = ImagePatrolManager.GetInstance().LatestImagePatrolResult.TNSampleTubeSmutState;
                WaterCupSmutState = ImagePatrolManager.GetInstance().LatestImagePatrolResult.TNWaterCupSmutState;
                SampleCupSmutState = ImagePatrolManager.GetInstance().LatestImagePatrolResult.TNSampleCupSmutState;
            }

            #endregion

            #region 器件使用信息

            // 水样管
            WaterTubeUsageState.ElectrodeReplaceDays = qcdEquip.ElementLifeInfos.WaterTubeChangeTime;
            WaterTubeUsageState.ElectrodeLogestUseTime = qcdEquip.ElementLifeInfos.WaterTubeLogestUseTime;
            // 储液环
            ReceiverRingUsageState.ElectrodeReplaceDays = qcdEquip.ElementLifeInfos.ReceiverRingChangeTime;
            ReceiverRingUsageState.ElectrodeLogestUseTime = qcdEquip.ElementLifeInfos.ReceiverRingLogestUseTime;
            // 液位检测器1
            LevelDetector1UsageState.ElectrodeReplaceDays = qcdEquip.ElementLifeInfos.LevelDetector1ChangeTime;
            LevelDetector1UsageState.ElectrodeLogestUseTime = qcdEquip.ElementLifeInfos.LevelDetector1LogestUseTime;
            // 液位检测器2
            LevelDetector2UsageState.ElectrodeReplaceDays = qcdEquip.ElementLifeInfos.LevelDetector2ChangeTime;
            LevelDetector2UsageState.ElectrodeLogestUseTime = qcdEquip.ElementLifeInfos.LevelDetector2LogestUseTime;
            // 防止字典不存在报错
            try
            {
                // 电磁阀组1
                ValveGroup1UsageState.UsageCount = (int)qcdEquip.ElementLifeInfos.ElementParamInfos[eQCD3900ElementToAnsysisType.电磁阀组1DO21].Residual;
                ValveGroup1UsageState.MaxUsageCount = (int)qcdEquip.ElementLifeInfos.ElementParamInfos[eQCD3900ElementToAnsysisType.电磁阀组1DO21].Total;
                // 电磁阀组2
                ValveGroup2UsageState.UsageCount = (int)qcdEquip.ElementLifeInfos.ElementParamInfos[eQCD3900ElementToAnsysisType.电磁阀组2DO22].Residual;
                ValveGroup2UsageState.MaxUsageCount = (int)qcdEquip.ElementLifeInfos.ElementParamInfos[eQCD3900ElementToAnsysisType.电磁阀组2DO22].Total;
                // 纯水泵PU1
                Pump1UsageState.UsageCount = (int)qcdEquip.ElementLifeInfos.ElementParamInfos[eQCD3900ElementToAnsysisType.纯水泵PU1].Residual;
                Pump1UsageState.MaxUsageCount = (int)qcdEquip.ElementLifeInfos.ElementParamInfos[eQCD3900ElementToAnsysisType.纯水泵PU1].Total;
                // 定容泵PU2
                Pump2UsageState.UsageCount = (int)qcdEquip.ElementLifeInfos.ElementParamInfos[eQCD3900ElementToAnsysisType.定容泵PU2].Residual;
                Pump2UsageState.MaxUsageCount = (int)qcdEquip.ElementLifeInfos.ElementParamInfos[eQCD3900ElementToAnsysisType.定容泵PU2].Total;
                // 样品杯气泵PU3
                Pump3UsageState.UsageCount = (int)qcdEquip.ElementLifeInfos.ElementParamInfos[eQCD3900ElementToAnsysisType.样品杯气泵PU3].Residual;
                Pump3UsageState.MaxUsageCount = (int)qcdEquip.ElementLifeInfos.ElementParamInfos[eQCD3900ElementToAnsysisType.样品杯气泵PU3].Total;
                // 标样杯气泵PU4
                Pump4UsageState.UsageCount = (int)qcdEquip.ElementLifeInfos.ElementParamInfos[eQCD3900ElementToAnsysisType.标样杯气泵PU4].Residual;
                Pump4UsageState.MaxUsageCount = (int)qcdEquip.ElementLifeInfos.ElementParamInfos[eQCD3900ElementToAnsysisType.标样杯气泵PU4].Total;
                // 原水夹管阀SV1
                SV1UsageState.UsageCount = (int)qcdEquip.ElementLifeInfos.ElementParamInfos[eQCD3900ElementToAnsysisType.原水夹管阀SV1].Residual;
                SV1UsageState.MaxUsageCount = (int)qcdEquip.ElementLifeInfos.ElementParamInfos[eQCD3900ElementToAnsysisType.原水夹管阀SV1].Total;
                // 水样杯夹管阀SV2
                SV2UsageState.UsageCount = (int)qcdEquip.ElementLifeInfos.ElementParamInfos[eQCD3900ElementToAnsysisType.水样杯夹管阀SV2].Residual;
                SV2UsageState.MaxUsageCount = (int)qcdEquip.ElementLifeInfos.ElementParamInfos[eQCD3900ElementToAnsysisType.水样杯夹管阀SV2].Total;
                // 三通电磁阀SV3
                SV3UsageState.UsageCount = (int)qcdEquip.ElementLifeInfos.ElementParamInfos[eQCD3900ElementToAnsysisType.三通电磁阀SV3].Residual;
                SV3UsageState.MaxUsageCount = (int)qcdEquip.ElementLifeInfos.ElementParamInfos[eQCD3900ElementToAnsysisType.三通电磁阀SV3].Total;
                // 三通电磁阀SV4
                SV4UsageState.UsageCount = (int)qcdEquip.ElementLifeInfos.ElementParamInfos[eQCD3900ElementToAnsysisType.三通电磁阀SV4].Residual;
                SV4UsageState.MaxUsageCount = (int)qcdEquip.ElementLifeInfos.ElementParamInfos[eQCD3900ElementToAnsysisType.三通电磁阀SV4].Total;
                // 标样杯夹管阀SV5
                SV5UsageState.UsageCount = (int)qcdEquip.ElementLifeInfos.ElementParamInfos[eQCD3900ElementToAnsysisType.标样杯夹管阀SV5].Residual;
                SV5UsageState.MaxUsageCount = (int)qcdEquip.ElementLifeInfos.ElementParamInfos[eQCD3900ElementToAnsysisType.标样杯夹管阀SV5].Total;
                // 柱塞泵
                PlungerPumpUsageState.UsageCount = (int)qcdEquip.ElementLifeInfos.ElementParamInfos[eQCD3900ElementToAnsysisType.柱塞泵].Residual;
                PlungerPumpUsageState.MaxUsageCount = (int)qcdEquip.ElementLifeInfos.ElementParamInfos[eQCD3900ElementToAnsysisType.柱塞泵].Total;
            }
            catch
            {
            }

            #endregion

            // 结果状态判断
            if(ComState != eModuleWorkingState.正常 || SystemState != eQCD3900DeviceState.系统正常 || AlarmState != eModuleWorkingState.正常 || WaterTubeSmutState != eSmutState.正常 || SampleTubeSmutState != eSmutState.正常 || WaterCupSmutState != eSmutState.正常 || SampleCupSmutState != eSmutState.正常)
            {
                PatrolResult = ePatrolResult.异常;
            }
        }

        #endregion

        #region 方法重写

        /// <summary>
        /// 打印巡检结果
        /// </summary>
        /// <returns></returns>
        public override string GetResultStr()
        {
            // 报警详情
            string alarmDetail = string.Join(",", CurrentAlarmList);

            StringBuilder resultStr = new StringBuilder();

            resultStr.AppendLine(ModelName)
                .AppendLine($"巡检结果：{PatrolResult}")
                .AppendLine($"1.仪表状态")
                .AppendLine($"通讯状态：{ComState}")
                .AppendLine($"系统状态：{SystemState}")
                .AppendLine($"报警状态：{AlarmState}");
            if(!string.IsNullOrEmpty(alarmDetail))
            {
                resultStr.AppendLine(alarmDetail);
            }
            resultStr.AppendLine($"质控水样管状态：{WaterTubeSmutState}")
                .AppendLine($"质控标样管状态：{SampleTubeSmutState}")
                .AppendLine($"质控水样管状态：{WaterCupSmutState}")
                .AppendLine($"质控水样管状态：{SampleCupSmutState}")
                .AppendLine($"2.器件使用信息：")
                .Append(WaterTubeUsageState.GetResultStr())
                .Append(ReceiverRingUsageState.GetResultStr())
                .Append(ValveGroup1UsageState.GetResultStr())
                .Append(ValveGroup2UsageState.GetResultStr())
                .Append(Pump1UsageState.GetResultStr())
                .Append(Pump2UsageState.GetResultStr())
                .Append(Pump3UsageState.GetResultStr())
                .Append(Pump4UsageState.GetResultStr())
                .Append(SV1UsageState.GetResultStr())
                .Append(SV2UsageState.GetResultStr())
                .Append(SV3UsageState.GetResultStr())
                .Append(SV4UsageState.GetResultStr())
                .Append(SV5UsageState.GetResultStr())
                .Append(PlungerPumpUsageState.GetResultStr())
                .Append(LevelDetector1UsageState.GetResultStr())
                .Append(LevelDetector2UsageState.GetResultStr());

            return resultStr.ToString();
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 打印异常巡检结果
        /// </summary>
        /// <returns></returns>
        public string GetErrorResultStr()
        {
            StringBuilder resultStr = new StringBuilder();

            if(ComState != eModuleWorkingState.正常)
            {
                resultStr.Append("通讯状态异常，");
            }
            if(SystemState != eQCD3900DeviceState.系统正常)
            {
                resultStr.Append($"系统状态为{SystemState.ToString()}，");
            }
            if(AlarmState != eModuleWorkingState.正常)
            {
                // 报警详情
                string alarmDetail = string.Join("、", CurrentAlarmList);

                resultStr.Append($"报警详情:{alarmDetail}，");
            }
            if(WaterTubeSmutState != eSmutState.正常)
            {
                resultStr.Append("质控水样管脏污，");
            }
            if(SampleTubeSmutState != eSmutState.正常)
            {
                resultStr.Append("质控标样管脏污，");
            }
            if(WaterCupSmutState != eSmutState.正常)
            {
                resultStr.Append("质控水样杯脏污，");
            }
            if(SampleCupSmutState != eSmutState.正常)
            {
                resultStr.Append("质控标样杯脏污，");
            }

            return resultStr.ToString().TrimEnd('，');
        }

        #endregion
    }
}