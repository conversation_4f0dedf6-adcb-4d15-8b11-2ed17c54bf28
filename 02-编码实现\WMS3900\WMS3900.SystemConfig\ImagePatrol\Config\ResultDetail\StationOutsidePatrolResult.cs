using System.Collections.Generic;
using System.ComponentModel;
using Fpi.WMS3000.Equipment.Config;

namespace Fpi.WMS3000.SystemConfig.ImagePatrol.Config
{
    /// <summary>
    /// 监测站外巡检结果
    /// </summary>
    public class StationOutsidePatrolResult : ImageUnitPatrolResultBase
    {
        #region 字段属性

        /// <summary>
        /// 监测站外围人员入侵
        /// </summary>
        [Description("监测站外围人员入侵")]
        public eModuleWorkingState StationOutsideIntrusion { get; set; }

        #endregion

        #region 构造

        public StationOutsidePatrolResult()
        {
            UnitId = "StationOutside";
            UnitName = "监测站外";
        }

        #endregion

        #region 方法重写

        /// <summary>
        /// 打印巡检结果
        /// </summary>
        /// <returns></returns>
        public override List<string> GetResultStr()
        {
            return new List<string>
            {
                $"监测站外围人员入侵：{StationOutsideIntrusion}"
            };
        }

        #endregion
    }
}
