﻿using System;
using System.Collections.Generic;
using Fpi.Communication.Protocols;
using Fpi.HB.Business.Protocols.Helper;
using Fpi.WMS3000.Remote.FPISZH;
using Fpi.WMS3000.Remote.GJDBS;
using Fpi.WMS3000.Remote.GJDBS.Config;
using Fpi.WMS3000.Remote.GJDBS.GJDBSDataFrame;

namespace Fpi.WMS3000.Pollution.Remote.FPISZHWRY
{
    /// <summary>
    /// 谱育科技数智化污染源数据传输协议-数据接收器
    /// </summary>
    public class FPISZHWRYReceiver : FPISZHReceiver
    {
        #region 数据解析\处理

        /// <summary>
        /// 重写部分反控处理逻辑
        /// </summary>
        /// <param name="dataFrm"></param>
        /// <param name="exeRtn"></param>
        /// <returns></returns>
        protected override bool DealRequestCommand(GJDBSCommand dataFrm, ref eExeRtn exeRtn)
        {
            try
            {
                switch(dataFrm.CN)
                {
                    case "2011": // 取污染物实时数据
                        CustomDataAddendum(dataFrm, eUploadDataType.实时数据, ref exeRtn);
                        break;

                    case "2051": // 取污染物十分钟数据
                        CustomDataAddendum(dataFrm, eUploadDataType.分钟数据, ref exeRtn);
                        break;

                    case "2061": // 取污染物小时数据
                        CustomDataAddendum(dataFrm, eUploadDataType.小时数据, ref exeRtn);
                        break;

                    case "2031": // 取污染物日数据
                        CustomDataAddendum(dataFrm, eUploadDataType.日数据, ref exeRtn);
                        break;

                    // 非定制功能，走标准处理方式
                    default:
                        return false;
                }
            }
            catch
            {
                exeRtn = eExeRtn.执行失败但不知道原因;
            }

            return true;
        }

        #endregion

        #region 数据补遗

        /// <summary>
        /// 数据补遗
        /// </summary>
        /// <param name="dataFrm"></param>
        /// <param name="uploadType"></param>
        /// <param name="exeRtn"></param>
        private void CustomDataAddendum(GJDBSCommand dataFrm, eUploadDataType uploadType, ref eExeRtn exeRtn)
        {
            if(null != dataFrm.CP && !string.IsNullOrEmpty(dataFrm.CP.BeginTime) && !string.IsNullOrEmpty(dataFrm.CP.EndTime))
            {
                try
                {
                    DateTime begin = DateTime.ParseExact(dataFrm.CP.BeginTime, GJDBSProtocolDesc.DateTimeFormat, null);
                    DateTime end = DateTime.ParseExact(dataFrm.CP.EndTime, GJDBSProtocolDesc.DateTimeFormat, null);
                    try
                    {
                        SendAddendumData(dataFrm, uploadType, begin, end);
                    }
                    catch
                    {
                        exeRtn = eExeRtn.执行失败但不知道原因;
                    }
                }
                catch
                {
                    exeRtn = eExeRtn.命令请求条件错误;
                }
            }
            else
            {
                exeRtn = eExeRtn.命令请求条件错误;
            }
        }

        /// <summary>
        /// 发送补遗数据
        /// </summary>
        /// <param name="uploadType"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        private void SendAddendumData(GJDBSCommand dataFrm, eUploadDataType uploadType, DateTime startTime, DateTime endTime)
        {
            if(_desc != null)
            {
                try
                {
                    // 待发送数据表
                    var cmdList = new List<GJDBSCommand>();
                    switch(uploadType)
                    {
                        // 定制部分数据类型，走自定义协议处理；
                        case eUploadDataType.实时数据:
                            cmdList.AddRange(FPISZHWRYHelper.BuildRealTimeData(_desc, startTime, endTime, eGetDataCount.所有数据, dataFrm.QN));
                            break;
                        case eUploadDataType.分钟数据:
                        case eUploadDataType.小时数据:
                        case eUploadDataType.日数据:
                            cmdList.AddRange(FPISZHWRYHelper.BuildHistoryData(_desc, startTime, endTime, uploadType, eGetDataCount.所有数据, dataFrm.QN));
                            break;
                        default:
                            throw new Exception("非定制部分数据类型，请走标准协议处理！");
                    }

                    // 发送数据
                    foreach(GJDBSCommand cmd in cmdList)
                    {
                        // 数据不需回应
                        cmd.Flag = dataFrm.Flag - 1;
                        _desc.SendDataFrame(cmd);
                    }
                }
                catch(Exception e)
                {
                    ProtocolLogHelper.ShowMsg($"上传{uploadType}类型数据出错:{e.Message}");
                }
            }
            else
            {
                throw new Exception("协议描述器为空！");
            }
        }

        #endregion
    }
}