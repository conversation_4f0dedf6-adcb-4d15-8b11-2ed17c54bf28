﻿using System.ComponentModel;
using System.Text;
using Fpi.Util.Extensions;
using Fpi.WMS3000.Equipment;
using Fpi.WMS3000.Equipment.Config;
using Fpi.WMS3000.SystemConfig.SmartPatrol.Helper;

namespace Fpi.WMS3000.SystemConfig.SmartPatrol.Config
{
    /// <summary>
    /// 外部采水设施模块巡检结果
    /// </summary>
    public class ExterCSModelResult : SubModelResultBase
    {
        #region 字段属性

        /// <summary>
        /// 采水点水体颜色
        /// 视频图像识别：采水点水体状态为正常（清澈/浑浊），异常（有色）；
        /// </summary>
        [Description("采水点水体颜色")]
        public eWaterColor WaterState { get; set; }

        /// <summary>
        /// 采水点漂浮物
        /// 视频图像识别：采水装置划定区域内检测到有漂浮物；
        /// </summary>
        [Description("采水点漂浮物")]
        public eModuleWorkingState FloatState { get; set; }

        /// <summary>
        /// 采水点水位状态
        /// 视频图像识别：采水点位水位与前一天水位上升或下降30%，则预警；
        /// </summary>
        [Description("采水点水位状态")]
        public eModuleWorkingState WaterLevelState { get; set; }

        /// <summary>
        /// 采水点人员入侵
        /// 视频图像识别：采水点检测到有人员入侵；
        /// </summary>
        [Description("采水点人员入侵")]
        public eModuleWorkingState InvasionState { get; set; }

        /// <summary>
        /// 采水装置偏移
        /// 视频图像识别：采水装置漂浮出划定的区域范围，则偏移；
        /// </summary>
        [Description("采水装置偏移")]
        public eWaterTrapMigration MigrationState { get; set; }

        /// <summary>
        /// 采水深度状态
        /// 采水深度为0.5~1.0米，则正常，若低于0.5米或高于1.0米则判断预警（可能搁浅或采水浮筒浮力不足）；并记录采水深度；
        /// </summary>
        [Description("采水深度状态")]
        public eModuleWorkingState DepthState { get; set; }

        /// <summary>
        /// 采水深度
        /// </summary>
        [Description("采水深度(米)")]
        public double WaterDepth { get; set; }

        #endregion

        #region 构造

        public ExterCSModelResult()
        {
            ModelName = "外部采水设施";
        }

        #endregion

        #region 方法重写

        /// <summary>
        /// 打印巡检结果
        /// </summary>
        /// <returns></returns>
        public override string GetResultStr()
        {
            var invasionStateStr = InvasionState == eModuleWorkingState.正常 ? "正常" : "入侵";
            var floatStateStr = FloatState == eModuleWorkingState.正常 ? "正常" : "漂浮";
            var depthStateStr = DepthState == eModuleWorkingState.正常 ? "正常" : "可能搁浅";

            StringBuilder resultStr = new StringBuilder();

            resultStr.AppendLine(ModelName)
                .AppendLine($"巡检结果：{PatrolResult}")
                .AppendLine($"采水点水体颜色：{WaterState}")
                .AppendLine($"采水点人员入侵：{invasionStateStr}")
                .AppendLine($"采水点漂浮物：{floatStateStr}")
                .AppendLine($"采水深度状态：{depthStateStr}，采水深度：{WaterDepth.ToDisplayFormat(PatrolDataDisplayHelper.FloatFormat, "米")}")
                .AppendLine($"采水点水位状态：{WaterLevelState}")
                .AppendLine($"采水装置偏移：{MigrationState}");

            return resultStr.ToString();
        }

        #endregion
    }
}